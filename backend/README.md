# 图像增强Web应用 - 后端服务

基于FastAPI的图像增强后端API服务，提供图像上传、处理和下载功能。

## 🚀 快速启动

### 方法一：使用项目根目录脚本（推荐）
```bash
# 在项目根目录执行（自动停止已运行的服务）
./run.sh

# 停止所有服务
./stop.sh
```

### 方法二：手动启动
```bash
# 1. 激活conda环境
source ../activate_env.sh

# 2. 安装依赖（首次运行）
pip install -r requirments.txt

# 3. 启动服务
python test_simple.py
```

### 方法三：使用uvicorn启动
```bash
# 激活环境后
source ../activate_env.sh
uvicorn test_simple:app --host 0.0.0.0 --port 8001 --reload
```

## 📋 环境要求

- Python 3.9+
- conda环境：`imageenhanceproweb`
- 依赖包：见 `requirments.txt`

## 🔧 依赖安装

```bash
# 激活环境
source ../activate_env.sh

# 安装所有依赖
pip install -r requirments.txt

# 或单独安装关键依赖
pip install fastapi uvicorn python-multipart opencv-python numpy
```

## 🌐 API接口

### 基础信息
- **服务地址**: http://localhost:8001
- **API文档**: http://localhost:8001/docs (Swagger UI)

### 主要接口

#### 1. 健康检查
```
GET /
```
**响应示例**:
```json
{
  "message": "图像增强API服务正在运行",
  "version": "1.0.0"
}
```

#### 2. 图像增强
```
POST /enhance/
```
**请求**:
- Content-Type: `multipart/form-data`
- 参数: `file` (图像文件)

**响应示例**:
```json
{
  "filename": "example.jpg",
  "enhanced_url": "enhanced_example.jpg",
  "message": "图像增强完成"
}
```

#### 3. 获取处理结果
```
GET /result/{filename}
```
**响应**: 增强后的图像文件

## 📁 目录结构

```
backend/
├── app/                    # 原始应用代码
│   ├── main.py            # 主程序入口
│   └── utils/             # 工具函数
│       └── enhance.py     # 图像增强逻辑
├── models/                # AI模型相关
│   └── sr_model.py        # 超分辨率模型
├── uploads/               # 上传文件存储
│   └── processed/         # 处理后文件存储
├── test_simple.py         # 简化版服务（当前使用）
├── minimal_test.py        # 最小测试服务
├── requirments.txt        # Python依赖
└── README.md             # 本文件
```

## 🔍 功能特性

- ✅ 图像文件上传验证
- ✅ 4倍超分辨率增强（双三次插值+锐化）
- ✅ CORS跨域支持
- ✅ 完整错误处理和日志
- ✅ 自动创建必要目录
- ✅ 文件类型和大小验证

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :8001
   # 修改端口或杀死占用进程
   ```

2. **依赖缺失**
   ```bash
   # 重新安装依赖
   pip install -r requirments.txt
   ```

3. **环境问题**
   ```bash
   # 检查conda环境
   conda env list
   # 重新激活环境
   source ../activate_env.sh
   ```

### 日志查看
服务运行时会在控制台输出详细日志，包括：
- 请求处理状态
- 错误信息
- 文件处理进度

## 🔄 开发模式

启动开发模式（自动重载）：
```bash
source ../activate_env.sh
uvicorn test_simple:app --host 0.0.0.0 --port 8001 --reload
```

## 📝 更新日志

- **v1.0.0** (2025-01-13): 初始版本，基础图像增强功能
- 支持图像上传、处理、下载
- 集成OpenCV图像处理算法
- 完整的错误处理机制