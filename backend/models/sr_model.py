import cv2
import numpy as np
import os
import logging
import torch

logger = logging.getLogger(__name__)

class SimpleSRModel:
    """简单的图像超分辨率模型，使用OpenCV的插值方法作为备选方案"""

    def __init__(self, scale=4):
        self.scale = scale
        logger.info("使用简单插值方法进行图像超分辨率")

    def enhance(self, img, outscale=None):
        """
        使用双三次插值进行图像放大
        Args:
            img: 输入图像 (numpy array)
            outscale: 放大倍数
        Returns:
            tuple: (放大后的图像, None)
        """
        if img is None:
            raise ValueError("输入图像为空")

        if outscale is None:
            outscale = self.scale

        height, width = img.shape[:2]
        new_height = int(height * outscale)
        new_width = int(width * outscale)

        # 使用双三次插值进行放大
        enhanced = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # 应用一些简单的锐化滤波器来改善效果
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        # 混合原始放大图像和锐化图像
        result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)

        return result, None

class RealESRGANModel:
    """RealESRGAN模型封装类"""

    def __init__(self, model_path, scale=4, device='cpu'):
        self.model_path = model_path
        self.scale = scale
        self.device = device
        self.upsampler = None
        self._load_model()

    def _load_model(self):
        """加载RealESRGAN模型"""
        try:
            from realesrgan import RealESRGANer
            from basicsr.archs.srvgg_arch import SRVGGNetCompact

            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            logger.info(f"加载RealESRGAN模型: {self.model_path}")

            # 创建模型架构
            model = SRVGGNetCompact(num_feat=64, num_conv=32, upscale=4, act_type='prelu')

            # 加载模型权重
            model_weights = torch.load(self.model_path, map_location=self.device)
            if 'params' in model_weights:
                model.load_state_dict(model_weights['params'])
            else:
                model.load_state_dict(model_weights)

            model.eval()

            # 创建RealESRGANer实例
            self.upsampler = RealESRGANer(
                scale=self.scale,
                model_path=self.model_path,
                model=model,
                tile=0,
                tile_pad=10,
                pre_pad=0,
                half=False,
                device=self.device
            )

            logger.info("RealESRGAN模型加载成功")

        except Exception as e:
            logger.error(f"RealESRGAN模型加载失败: {str(e)}")
            raise e

    def enhance(self, img, outscale=None):
        """
        使用RealESRGAN进行图像增强
        Args:
            img: 输入图像 (numpy array)
            outscale: 放大倍数
        Returns:
            tuple: (增强后的图像, None)
        """
        if self.upsampler is None:
            raise RuntimeError("模型未正确加载")

        if img is None:
            raise ValueError("输入图像为空")

        try:
            # RealESRGAN处理
            output, _ = self.upsampler.enhance(img, outscale=outscale or self.scale)
            return output, None

        except Exception as e:
            logger.error(f"RealESRGAN增强失败: {str(e)}")
            raise e

# 全局模型实例
sr_model = None

def get_sr_model(scale=4, use_realesrgan=True):
    """
    获取超分辨率模型实例
    Args:
        scale: 放大倍数 (2, 4)
        use_realesrgan: 是否尝试使用RealESRGAN模型
    Returns:
        模型实例
    """
    global sr_model
    if sr_model is None:
        if use_realesrgan:
            try:
                # 尝试加载RealESRGAN模型
                current_dir = os.path.dirname(os.path.abspath(__file__))
                model_path = os.path.join(current_dir, '..', 'realesr-general-x4v3.pth')

                if os.path.exists(model_path):
                    logger.info(f"尝试加载RealESRGAN模型: {model_path}")
                    sr_model = RealESRGANModel(model_path, scale=scale, device='cpu')
                    logger.info("RealESRGAN模型加载成功")
                else:
                    raise FileNotFoundError(f"模型文件不存在: {model_path}")

            except Exception as e:
                logger.warning(f"RealESRGAN模型加载失败: {str(e)}")
                logger.info("降级使用简单插值方法")
                sr_model = SimpleSRModel(scale=scale)
        else:
            logger.info("使用简单插值方法")
            sr_model = SimpleSRModel(scale=scale)

    return sr_model

def reset_model():
    """重置模型实例，用于切换模型类型"""
    global sr_model
    sr_model = None