import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from models.sr_model import get_sr_model, reset_model
import cv2
import numpy as np
import logging

logger = logging.getLogger(__name__)

def apply_sharpening(img, strength=0.3):
    """
    应用锐化效果
    Args:
        img: 输入图像
        strength: 锐化强度 (0.0-1.0)
    Returns:
        锐化后的图像
    """
    if strength <= 0:
        return img

    # 创建锐化核
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(img, -1, kernel)

    # 混合原图和锐化图像
    result = cv2.addWeighted(img, 1-strength, sharpened, strength, 0)
    return result

def apply_denoising(img, strength=10):
    """
    应用降噪处理
    Args:
        img: 输入图像
        strength: 降噪强度 (1-30)
    Returns:
        降噪后的图像
    """
    if strength <= 0:
        return img

    # 使用非局部均值降噪
    denoised = cv2.fastNlMeansDenoisingColored(img, None, strength, strength, 7, 21)
    return denoised

def apply_color_enhancement(img, saturation=1.0, contrast=1.0, brightness=0):
    """
    应用色彩增强
    Args:
        img: 输入图像
        saturation: 饱和度倍数 (0.0-2.0)
        contrast: 对比度倍数 (0.0-2.0)
        brightness: 亮度调整 (-100 to 100)
    Returns:
        增强后的图像
    """
    result = img.copy()

    # 调整亮度和对比度
    result = cv2.convertScaleAbs(result, alpha=contrast, beta=brightness)

    # 调整饱和度
    if saturation != 1.0:
        hsv = cv2.cvtColor(result, cv2.COLOR_BGR2HSV).astype(np.float32)
        hsv[:, :, 1] = hsv[:, :, 1] * saturation
        hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)
        result = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)

    return result

def apply_beauty_filter(img, smooth_strength=0.3):
    """
    应用美颜滤镜
    Args:
        img: 输入图像
        smooth_strength: 平滑强度 (0.0-1.0)
    Returns:
        美颜后的图像
    """
    if smooth_strength <= 0:
        return img

    # 双边滤波进行皮肤平滑
    smooth = cv2.bilateralFilter(img, 15, 80, 80)

    # 保留细节的混合
    result = cv2.addWeighted(img, 1-smooth_strength, smooth, smooth_strength, 0)

    # 轻微提升对比度
    result = cv2.convertScaleAbs(result, alpha=1.1, beta=5)

    return result

def enhance_image(input_path, output_path, **kwargs):
    """
    增强图像的主函数
    Args:
        input_path: 输入图像路径
        output_path: 输出图像路径
        **kwargs: 增强参数
            - scale: 超分辨率倍数 (2, 4)
            - use_realesrgan: 是否使用RealESRGAN (True/False)
            - sharpening: 锐化强度 (0.0-1.0)
            - denoising: 降噪强度 (0-30)
            - saturation: 饱和度 (0.0-2.0)
            - contrast: 对比度 (0.0-2.0)
            - brightness: 亮度 (-100 to 100)
            - beauty: 美颜强度 (0.0-1.0)
    """
    try:
        logger.info(f"开始增强图像: {input_path}")
        logger.info(f"增强参数: {kwargs}")

        # 解析参数
        scale = kwargs.get('scale', 4)
        use_realesrgan = kwargs.get('use_realesrgan', True)
        sharpening = kwargs.get('sharpening', 0.0)
        denoising = kwargs.get('denoising', 0)
        saturation = kwargs.get('saturation', 1.0)
        contrast = kwargs.get('contrast', 1.0)
        brightness = kwargs.get('brightness', 0)
        beauty = kwargs.get('beauty', 0.0)

        # 重置模型以支持不同参数
        if scale != 4 or not use_realesrgan:
            reset_model()

        # 获取模型实例
        sr_model = get_sr_model(scale=scale, use_realesrgan=use_realesrgan)

        # 读取图像
        img = cv2.imread(input_path, cv2.IMREAD_COLOR)
        if img is None:
            raise ValueError(f"无法读取图像文件: {input_path}")

        logger.info(f"原始图像尺寸: {img.shape}")

        # 预处理：降噪
        if denoising > 0:
            logger.info(f"应用降噪处理，强度: {denoising}")
            img = apply_denoising(img, denoising)

        # 预处理：美颜
        if beauty > 0:
            logger.info(f"应用美颜滤镜，强度: {beauty}")
            img = apply_beauty_filter(img, beauty)

        # 超分辨率处理
        logger.info(f"进行超分辨率处理，倍数: {scale}x")
        output, _ = sr_model.enhance(img, outscale=scale)

        logger.info(f"超分辨率后图像尺寸: {output.shape}")

        # 后处理：色彩增强
        if saturation != 1.0 or contrast != 1.0 or brightness != 0:
            logger.info(f"应用色彩增强 - 饱和度: {saturation}, 对比度: {contrast}, 亮度: {brightness}")
            output = apply_color_enhancement(output, saturation, contrast, brightness)

        # 后处理：锐化
        if sharpening > 0:
            logger.info(f"应用锐化处理，强度: {sharpening}")
            output = apply_sharpening(output, sharpening)

        # 保存结果
        cv2.imwrite(output_path, output)

        logger.info(f"图像增强完成: {output_path}")

    except Exception as e:
        logger.error(f"图像增强失败: {str(e)}")
        raise e