# 🚀 AI图像增强Web应用

一个基于AI技术的图像超分辨率增强Web应用，提供简单易用的图像质量提升服务。

## ✨ 功能特性

### 🚀 核心功能
- 🖼️ **图像上传**: 支持拖拽上传和点击选择，兼容多种图像格式
- 🔍 **RealESRGAN超分辨率**: 使用先进的AI模型进行2x/4x图像分辨率提升
- 📱 **响应式设计**: 完美适配桌面、平板和手机设备
- ⚡ **实时处理**: 快速图像处理，即时查看效果
- 📥 **便捷下载**: 一键下载增强后的高质量图像
- 🔄 **对比展示**: 原图与增强图并排对比，效果一目了然

### 🎨 高级功能
- 🎛️ **多种增强选项**: 锐化、降噪、色彩增强、美颜滤镜
- 🎯 **预设配置**: 人像优化、风景增强、复古风格等预设
- ⚙️ **自定义参数**: 精细调节饱和度、对比度、亮度等参数
- 🔧 **智能降级**: 模型不可用时自动切换到备选算法
- 📊 **参数显示**: 查看每次处理使用的具体参数

## 🏗️ 技术架构

### 后端 (FastAPI + Python)
- **框架**: FastAPI - 高性能异步Web框架
- **AI模型**: RealESRGAN - 先进的超分辨率神经网络
- **图像处理**: OpenCV + NumPy - 专业图像处理库
- **备选算法**: 双三次插值 + 锐化滤波器
- **API文档**: 自动生成Swagger文档

### 前端 (React)
- **框架**: React 18 - 现代化前端框架
- **构建工具**: Create React App
- **样式**: 内联样式 + 响应式设计
- **交互**: 现代化用户界面

## 🚀 快速开始

### 一键启动（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd imageenhanceproweb

# 一键启动前后端服务（自动停止已运行的服务）
./run.sh

# 停止所有服务
./stop.sh
```

启动后访问：
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8001
- **API文档**: http://localhost:8001/docs

**注意**: `./run.sh` 会自动检测并停止已运行的服务，然后启动新的服务实例。

### 手动启动

#### 1. 环境准备
```bash
# 创建conda环境
conda create -n imageenhanceproweb python=3.9 -y

# 激活环境
source ./activate_env.sh
```

#### 2. 启动后端
```bash
cd backend

# 安装依赖
pip install -r requirments.txt
pip install python-multipart

# 启动服务
python test_simple.py
```

#### 3. 启动前端
```bash
cd frontend

# 安装依赖
npm install

# 启动服务
npm start
```

## 📁 项目结构

```
imageenhanceproweb/
├── backend/                 # 后端服务
│   ├── app/                # 应用代码
│   ├── models/             # AI模型
│   ├── uploads/            # 文件存储
│   ├── test_simple.py      # 主服务文件
│   ├── requirments.txt     # Python依赖
│   └── README.md          # 后端说明
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   ├── package.json       # 项目配置
│   └── README.md         # 前端说明
├── activate_env.sh        # 环境激活脚本
├── run.sh                # 一键启动脚本
├── stop.sh               # 一键停止脚本
├── prd.md                # 产品需求文档
└── README.md             # 项目总览（本文件）
```

## 🔧 环境要求

### 系统要求
- **操作系统**: macOS, Linux, Windows
- **Python**: 3.9+
- **Node.js**: 14.0+
- **Conda**: 推荐使用Anaconda或Miniconda

### 浏览器支持
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 🎯 使用指南

1. **上传图像**: 点击上传区域或拖拽图像文件
2. **等待处理**: 系统自动进行图像增强处理
3. **查看结果**: 对比原图与增强后的效果
4. **下载图像**: 点击下载按钮保存增强图像

## 🔍 API接口

### 图像增强
```http
POST /enhance/
Content-Type: multipart/form-data

参数: file (图像文件)
```

### 获取结果
```http
GET /result/{filename}
```

详细API文档请访问: http://localhost:8001/docs

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用情况
   lsof -i :3000  # 前端
   lsof -i :8001  # 后端
   ```

2. **依赖安装失败**
   ```bash
   # 重新安装Python依赖
   pip install -r backend/requirments.txt
   
   # 重新安装Node.js依赖
   cd frontend && npm install
   ```

3. **环境问题**
   ```bash
   # 检查conda环境
   conda env list
   
   # 重新激活环境
   source ./activate_env.sh
   ```

## 📈 性能优化

- **图像处理**: 使用高效的OpenCV算法
- **前端渲染**: React虚拟DOM优化
- **网络传输**: 文件压缩和缓存策略
- **错误处理**: 完善的异常捕获机制

## 🔮 未来规划

### 短期目标
- [ ] 集成RealESRGAN模型提升增强效果
- [ ] 添加更多图像处理选项
- [ ] 支持批量图像处理

### 长期目标
- [ ] 用户系统和历史记录
- [ ] 多种AI模型选择
- [ ] 移动端原生应用
- [ ] 云端部署和CDN加速

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 项目讨论区

---

**享受AI图像增强的乐趣！** 🎨✨
