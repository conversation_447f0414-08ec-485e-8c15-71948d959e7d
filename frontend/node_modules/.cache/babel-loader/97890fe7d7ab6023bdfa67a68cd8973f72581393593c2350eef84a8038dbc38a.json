{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const handleUpload = async formData => {\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      backgroundColor: '#f5f5f5',\n      padding: '10px',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1400px',\n        margin: '0 auto',\n        backgroundColor: 'white',\n        borderRadius: '10px',\n        padding: '20px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            color: '#333',\n            marginBottom: '5px',\n            fontSize: '2rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F AI\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            fontSize: '1rem',\n            marginBottom: '10px'\n          },\n          children: \"\\u4F7F\\u7528RealESRGAN\\u6280\\u672F\\u63D0\\u5347\\u56FE\\u50CF\\u5206\\u8FA8\\u7387\\u548C\\u8D28\\u91CF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8d7da',\n          color: '#721c24',\n          padding: '15px',\n          borderRadius: '5px',\n          marginBottom: '20px',\n          border: '1px solid #f5c6cb'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u9519\\u8BEF:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), \" \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleReset,\n          style: {\n            marginLeft: '10px',\n            padding: '5px 10px',\n            backgroundColor: '#dc3545',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer'\n          },\n          children: \"\\u91CD\\u8BD5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#d1ecf1',\n          color: '#0c5460',\n          padding: '15px',\n          borderRadius: '5px',\n          marginBottom: '20px',\n          border: '1px solid #bee5eb',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '10px'\n          },\n          children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: result ? '1fr 1fr' : '1fr',\n          gap: '20px',\n          alignItems: 'start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginTop: '20px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleReset,\n              style: {\n                padding: '10px 20px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '5px',\n                cursor: 'pointer',\n                fontSize: '14px'\n              },\n              children: \"\\u5904\\u7406\\u65B0\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(ResultView, {\n            result: result,\n            originalImage: originalImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"RavOJqoDvKIC3V1TFpTBDCe1kuU=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "handleUpload", "formData", "file", "get", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "handleReset", "style", "minHeight", "backgroundColor", "padding", "fontFamily", "children", "max<PERSON><PERSON><PERSON>", "margin", "borderRadius", "boxShadow", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "onClick", "marginLeft", "cursor", "display", "gridTemplateColumns", "gap", "alignItems", "onUpload", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n\n  const handleUpload = async (formData) => {\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n\n  return (\n    <div style={{ \n      minHeight: '100vh',\n      backgroundColor: '#f5f5f5',\n      padding: '10px',\n      fontFamily: 'Arial, sans-serif'\n    }}>\n      <div style={{\n        maxWidth: '1400px',\n        margin: '0 auto',\n        backgroundColor: 'white',\n        borderRadius: '10px',\n        padding: '20px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'\n      }}>\n        <header style={{ textAlign: 'center', marginBottom: '20px' }}>\n          <h1 style={{ \n            color: '#333', \n            marginBottom: '5px',\n            fontSize: '2rem'\n          }}>\n            🖼️ AI图像增强工具\n          </h1>\n          <p style={{ \n            color: '#666', \n            fontSize: '1rem',\n            marginBottom: '10px'\n          }}>\n            使用RealESRGAN技术提升图像分辨率和质量\n          </p>\n        </header>\n\n        {error && (\n          <div style={{ \n            backgroundColor: '#f8d7da', \n            color: '#721c24', \n            padding: '15px', \n            borderRadius: '5px', \n            marginBottom: '20px',\n            border: '1px solid #f5c6cb'\n          }}>\n            <strong>错误:</strong> {error}\n            <button \n              onClick={handleReset}\n              style={{\n                marginLeft: '10px',\n                padding: '5px 10px',\n                backgroundColor: '#dc3545',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer'\n              }}\n            >\n              重试\n            </button>\n          </div>\n        )}\n\n        {isLoading && (\n          <div style={{ \n            backgroundColor: '#d1ecf1', \n            color: '#0c5460', \n            padding: '15px', \n            borderRadius: '5px', \n            marginBottom: '20px',\n            border: '1px solid #bee5eb',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '10px' }}>\n              正在处理图像，请稍候...\n            </div>\n          </div>\n        )}\n\n        <div style={{ \n          display: 'grid', \n          gridTemplateColumns: result ? '1fr 1fr' : '1fr',\n          gap: '20px',\n          alignItems: 'start'\n        }}>\n          <div>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n            \n            {result && (\n              <div style={{ textAlign: 'center', marginTop: '20px' }}>\n                <button \n                  onClick={handleReset}\n                  style={{\n                    padding: '10px 20px',\n                    backgroundColor: '#6c757d',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  }}\n                >\n                  处理新图像\n                </button>\n              </div>\n            )}\n          </div>\n          \n          {result && (\n            <div>\n              <ResultView result={result} originalImage={originalImage} />\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMe,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvCN,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMK,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKR,gBAAgB,CAACQ,CAAC,CAACC,MAAM,CAAChB,MAAM,CAAC;MACxDY,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCvB,SAAS,CAAC0B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,OAAO,EAAEwB,GAAG,CAAC;MAC3BvB,QAAQ,CAACuB,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4B,WAAW,GAAGA,CAAA,KAAM;IACxB9B,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,oBACEV,OAAA;IAAKmC,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eACAxC,OAAA;MAAKmC,KAAK,EAAE;QACVM,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE,QAAQ;QAChBL,eAAe,EAAE,OAAO;QACxBM,YAAY,EAAE,MAAM;QACpBL,OAAO,EAAE,MAAM;QACfM,SAAS,EAAE;MACb,CAAE;MAAAJ,QAAA,gBACAxC,OAAA;QAAQmC,KAAK,EAAE;UAAEU,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC3DxC,OAAA;UAAImC,KAAK,EAAE;YACTY,KAAK,EAAE,MAAM;YACbD,YAAY,EAAE,KAAK;YACnBE,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpD,OAAA;UAAGmC,KAAK,EAAE;YACRY,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBF,YAAY,EAAE;UAChB,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAER7C,KAAK,iBACJP,OAAA;QAAKmC,KAAK,EAAE;UACVE,eAAe,EAAE,SAAS;UAC1BU,KAAK,EAAE,SAAS;UAChBT,OAAO,EAAE,MAAM;UACfK,YAAY,EAAE,KAAK;UACnBG,YAAY,EAAE,MAAM;UACpBO,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,gBACAxC,OAAA;UAAAwC,QAAA,EAAQ;QAAG;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC7C,KAAK,eAC3BP,OAAA;UACEsD,OAAO,EAAEpB,WAAY;UACrBC,KAAK,EAAE;YACLoB,UAAU,EAAE,MAAM;YAClBjB,OAAO,EAAE,UAAU;YACnBD,eAAe,EAAE,SAAS;YAC1BU,KAAK,EAAE,OAAO;YACdM,MAAM,EAAE,MAAM;YACdV,YAAY,EAAE,KAAK;YACnBa,MAAM,EAAE;UACV,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA/C,SAAS,iBACRL,OAAA;QAAKmC,KAAK,EAAE;UACVE,eAAe,EAAE,SAAS;UAC1BU,KAAK,EAAE,SAAS;UAChBT,OAAO,EAAE,MAAM;UACfK,YAAY,EAAE,KAAK;UACnBG,YAAY,EAAE,MAAM;UACpBO,MAAM,EAAE,mBAAmB;UAC3BR,SAAS,EAAE;QACb,CAAE;QAAAL,QAAA,eACAxC,OAAA;UAAKmC,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDpD,OAAA;QAAKmC,KAAK,EAAE;UACVsB,OAAO,EAAE,MAAM;UACfC,mBAAmB,EAAEvD,MAAM,GAAG,SAAS,GAAG,KAAK;UAC/CwD,GAAG,EAAE,MAAM;UACXC,UAAU,EAAE;QACd,CAAE;QAAApB,QAAA,gBACAxC,OAAA;UAAAwC,QAAA,gBACExC,OAAA,CAACH,UAAU;YAACgE,QAAQ,EAAElD,YAAa;YAACN,SAAS,EAAEA;UAAU;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAE3DjD,MAAM,iBACLH,OAAA;YAAKmC,KAAK,EAAE;cAAEU,SAAS,EAAE,QAAQ;cAAEiB,SAAS,EAAE;YAAO,CAAE;YAAAtB,QAAA,eACrDxC,OAAA;cACEsD,OAAO,EAAEpB,WAAY;cACrBC,KAAK,EAAE;gBACLG,OAAO,EAAE,WAAW;gBACpBD,eAAe,EAAE,SAAS;gBAC1BU,KAAK,EAAE,OAAO;gBACdM,MAAM,EAAE,MAAM;gBACdV,YAAY,EAAE,KAAK;gBACnBa,MAAM,EAAE,SAAS;gBACjBR,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELjD,MAAM,iBACLH,OAAA;UAAAwC,QAAA,eACExC,OAAA,CAACF,UAAU;YAACK,MAAM,EAAEA,MAAO;YAACM,aAAa,EAAEA;UAAc;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClD,EAAA,CA9JQD,GAAG;AAAA8D,EAAA,GAAH9D,GAAG;AAgKZ,eAAeA,GAAG;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}