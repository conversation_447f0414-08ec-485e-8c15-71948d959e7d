{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ResultView=_ref=>{let{result,originalImage}=_ref;const[imageLoaded,setImageLoaded]=useState(false);const[imageError,setImageError]=useState(false);const[showParams,setShowParams]=useState(false);const[splitPosition,setSplitPosition]=useState(50);// 分割线位置百分比\nconst[isDragging,setIsDragging]=useState(false);const[viewMode,setViewMode]=useState('split');// 'split', 'side-by-side'\nconst containerRef=useRef(null);const handleImageLoad=()=>{setImageLoaded(true);setImageError(false);};const handleImageError=()=>{setImageError(true);setImageLoaded(false);};const downloadImage=()=>{const link=document.createElement('a');link.href=`http://localhost:8001/result/${result.enhanced_url}`;link.download=`enhanced_${result.filename}`;document.body.appendChild(link);link.click();document.body.removeChild(link);};// 分割线拖拽处理\nconst handleMouseDown=e=>{setIsDragging(true);e.preventDefault();};const handleMouseMove=e=>{if(!isDragging||!containerRef.current)return;const rect=containerRef.current.getBoundingClientRect();const x=e.clientX-rect.left;const percentage=Math.max(0,Math.min(100,x/rect.width*100));setSplitPosition(percentage);};const handleMouseUp=()=>{setIsDragging(false);};// 添加全局鼠标事件监听\nuseEffect(()=>{if(isDragging){document.addEventListener('mousemove',handleMouseMove);document.addEventListener('mouseup',handleMouseUp);return()=>{document.removeEventListener('mousemove',handleMouseMove);document.removeEventListener('mouseup',handleMouseUp);};}},[isDragging]);// 重置分割线位置\nconst resetSplit=()=>{setSplitPosition(50);};return/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'20px',padding:'20px',border:'1px solid #ddd',borderRadius:'10px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'20px',flexWrap:'wrap',gap:'10px'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{color:'#28a745',margin:0},children:\"\\u2705 \\u5904\\u7406\\u5B8C\\u6210\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('split'),style:{padding:'6px 12px',backgroundColor:viewMode==='split'?'#007bff':'#6c757d',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'14px'},children:\"\\u5206\\u5272\\u5BF9\\u6BD4\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('side-by-side'),style:{padding:'6px 12px',backgroundColor:viewMode==='side-by-side'?'#007bff':'#6c757d',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'14px'},children:\"\\u5E76\\u6392\\u5BF9\\u6BD4\"}),viewMode==='split'&&/*#__PURE__*/_jsx(\"button\",{onClick:resetSplit,style:{padding:'6px 12px',backgroundColor:'#28a745',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'14px'},children:\"\\u91CD\\u7F6E\"})]})]}),result.message&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#6c757d',marginBottom:'15px'},children:result.message}),result.params_used&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'20px',padding:'10px',backgroundColor:'#f8f9fa',borderRadius:'5px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'10px'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:0,marginRight:'10px'},children:\"\\u5904\\u7406\\u53C2\\u6570\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowParams(!showParams),style:{padding:'4px 8px',backgroundColor:'#6c757d',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'12px'},children:showParams?'隐藏':'显示'})]}),showParams&&/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#495057'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(150px, 1fr))',gap:'8px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8D85\\u5206\\u500D\\u6570:\"}),\" \",result.params_used.scale,\"x\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"AI\\u6A21\\u578B:\"}),\" \",result.params_used.use_realesrgan?'RealESRGAN':'简单插值']}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9510\\u5316:\"}),\" \",(result.params_used.sharpening*100).toFixed(0),\"%\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u964D\\u566A:\"}),\" \",result.params_used.denoising]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9971\\u548C\\u5EA6:\"}),\" \",result.params_used.saturation.toFixed(1)]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5BF9\\u6BD4\\u5EA6:\"}),\" \",result.params_used.contrast.toFixed(1)]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u4EAE\\u5EA6:\"}),\" \",result.params_used.brightness>0?'+':'',result.params_used.brightness]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u7F8E\\u989C:\"}),\" \",(result.params_used.beauty*100).toFixed(0),\"%\"]})]})})]}),viewMode==='split'?/*#__PURE__*/// 分割线对比模式\n_jsxs(\"div\",{ref:containerRef,style:{position:'relative',width:'100%',maxWidth:'800px',margin:'0 auto',cursor:isDragging?'ew-resize':'default',userSelect:'none'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',marginBottom:'15px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,color:'#495057',fontSize:'18px'},children:\"\\u62D6\\u62FD\\u5206\\u5272\\u7EBF\\u5BF9\\u6BD4\\u539F\\u56FE\\u4E0E\\u589E\\u5F3A\\u6548\\u679C\"}),/*#__PURE__*/_jsxs(\"p\",{style:{margin:'5px 0',fontSize:'14px',color:'#6c757d'},children:[\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5F53\\u524D\\u4F4D\\u7F6E\\uFF1A\",splitPosition.toFixed(0),\"%\"]})]}),originalImage&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',display:'inline-block',width:'100%'},children:[imageError?/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'400px',border:'2px dashed #dc3545',borderRadius:'8px',display:'flex',alignItems:'center',justifyContent:'center',color:'#dc3545',backgroundColor:'#f8d7da'},children:\"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"}):/*#__PURE__*/_jsx(\"img\",{src:`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,alt:\"\\u589E\\u5F3A\\u56FE\\u50CF\",onLoad:handleImageLoad,onError:handleImageError,style:{width:'100%',maxWidth:'800px',height:'auto',border:'2px solid #28a745',borderRadius:'8px',display:'block',opacity:imageLoaded?1:0.5}}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,width:`${splitPosition}%`,height:'100%',overflow:'hidden',borderRadius:'8px 0 0 8px'},children:/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"\\u539F\\u59CB\\u56FE\\u50CF\",style:{width:`${100*100/splitPosition}%`,height:'100%',objectFit:'cover',border:'2px solid #ddd',borderRadius:'8px'}})}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:`${splitPosition}%`,width:'4px',height:'100%',backgroundColor:'#fff',cursor:'ew-resize',boxShadow:'0 0 10px rgba(0,0,0,0.3)',transform:'translateX(-2px)',zIndex:10},onMouseDown:handleMouseDown,children:/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',width:'20px',height:'40px',backgroundColor:'#007bff',borderRadius:'10px',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'12px',fontWeight:'bold'},children:\"\\u27F7\"})}),!imageLoaded&&!imageError&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',color:'#6c757d',backgroundColor:'rgba(255,255,255,0.8)',padding:'10px',borderRadius:'5px'},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})]}):/*#__PURE__*/// 并排对比模式\n_jsxs(\"div\",{style:{display:'flex',gap:'20px',flexWrap:'wrap',justifyContent:'center'},children:[originalImage&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',flex:'1',minWidth:'300px',maxWidth:'400px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'10px',color:'#495057'},children:\"\\u539F\\u59CB\\u56FE\\u50CF\"}),/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"\\u539F\\u59CB\\u56FE\\u50CF\",style:{width:'100%',height:'auto',maxHeight:'400px',border:'2px solid #007bff',borderRadius:'8px',objectFit:'contain'}})]}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',flex:'1',minWidth:'300px',maxWidth:'400px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'10px',color:'#495057'},children:\"\\u589E\\u5F3A\\u56FE\\u50CF\"}),!imageLoaded&&!imageError&&/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'300px',display:'flex',alignItems:'center',justifyContent:'center',border:'2px dashed #ccc',borderRadius:'8px',color:'#6c757d'},children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"}),imageError&&/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'300px',display:'flex',alignItems:'center',justifyContent:'center',border:'2px solid #dc3545',borderRadius:'8px',color:'#dc3545',backgroundColor:'#f8d7da'},children:\"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(\"img\",{src:`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,alt:\"\\u589E\\u5F3A\\u56FE\\u50CF\",onLoad:handleImageLoad,onError:handleImageError,style:{width:'100%',height:'auto',maxHeight:'400px',border:'2px solid #28a745',borderRadius:'8px',objectFit:'contain',display:imageLoaded?'block':'none'}})]})]}),imageLoaded&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',marginTop:'20px'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:downloadImage,style:{padding:'10px 20px',backgroundColor:'#28a745',color:'white',border:'none',borderRadius:'5px',cursor:'pointer',fontSize:'16px',marginRight:'10px'},children:\"\\uD83D\\uDCE5 \\u4E0B\\u8F7D\\u589E\\u5F3A\\u56FE\\u50CF\"}),/*#__PURE__*/_jsx(\"a\",{href:`http://localhost:8001/result/${result.enhanced_url}`,target:\"_blank\",rel:\"noopener noreferrer\",style:{padding:'10px 20px',backgroundColor:'#007bff',color:'white',textDecoration:'none',borderRadius:'5px',fontSize:'16px'},children:\"\\uD83D\\uDD0D \\u5728\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"})]})]});};export default ResultView;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "ResultView", "_ref", "result", "originalImage", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "containerRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "e", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleMouseUp", "addEventListener", "removeEventListener", "resetSplit", "style", "marginTop", "padding", "border", "borderRadius", "children", "display", "justifyContent", "alignItems", "marginBottom", "flexWrap", "gap", "color", "margin", "onClick", "backgroundColor", "cursor", "fontSize", "message", "params_used", "marginRight", "gridTemplateColumns", "scale", "use_realesrgan", "sharpening", "toFixed", "denoising", "saturation", "contrast", "brightness", "beauty", "ref", "position", "max<PERSON><PERSON><PERSON>", "userSelect", "textAlign", "height", "src", "Date", "now", "alt", "onLoad", "onError", "opacity", "top", "overflow", "objectFit", "boxShadow", "transform", "zIndex", "onMouseDown", "fontWeight", "flex", "min<PERSON><PERSON><PERSON>", "maxHeight", "target", "rel", "textDecoration"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const containerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ marginTop: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '10px' }}>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px', flexWrap: 'wrap', gap: '10px' }}>\n        <h2 style={{ color: '#28a745', margin: 0 }}>✅ 处理完成</h2>\n\n        {/* 视图模式切换 */}\n        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: viewMode === 'split' ? '#007bff' : '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: viewMode === 'side-by-side' ? '#007bff' : '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '14px'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {result.message && (\n        <p style={{ color: '#6c757d', marginBottom: '15px' }}>{result.message}</p>\n      )}\n\n      {/* 显示使用的参数 */}\n      {result.params_used && (\n        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>\n            <h4 style={{ margin: 0, marginRight: '10px' }}>处理参数</h4>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              {showParams ? '隐藏' : '显示'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ fontSize: '14px', color: '#495057' }}>\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '8px' }}>\n                <div><strong>超分倍数:</strong> {result.params_used.scale}x</div>\n                <div><strong>AI模型:</strong> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n                <div><strong>锐化:</strong> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n                <div><strong>降噪:</strong> {result.params_used.denoising}</div>\n                <div><strong>饱和度:</strong> {result.params_used.saturation.toFixed(1)}</div>\n                <div><strong>对比度:</strong> {result.params_used.contrast.toFixed(1)}</div>\n                <div><strong>亮度:</strong> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n                <div><strong>美颜:</strong> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 图像对比区域 */}\n      {viewMode === 'split' ? (\n        // 分割线对比模式\n        <div\n          ref={containerRef}\n          style={{\n            position: 'relative',\n            width: '100%',\n            maxWidth: '800px',\n            margin: '0 auto',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none'\n          }}\n        >\n          <div style={{ textAlign: 'center', marginBottom: '15px' }}>\n            <h3 style={{ margin: 0, color: '#495057', fontSize: '18px' }}>拖拽分割线对比原图与增强效果</h3>\n            <p style={{ margin: '5px 0', fontSize: '14px', color: '#6c757d' }}>\n              左侧：原始图像 | 右侧：增强图像 | 当前位置：{splitPosition.toFixed(0)}%\n            </p>\n          </div>\n\n          {originalImage && (\n            <div style={{ position: 'relative', display: 'inline-block', width: '100%' }}>\n              {/* 增强图像作为背景 */}\n              {imageError ? (\n                <div style={{\n                  width: '100%',\n                  height: '400px',\n                  border: '2px dashed #dc3545',\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#dc3545',\n                  backgroundColor: '#f8d7da'\n                }}>\n                  增强图像加载失败\n                </div>\n              ) : (\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    width: '100%',\n                    maxWidth: '800px',\n                    height: 'auto',\n                    border: '2px solid #28a745',\n                    borderRadius: '8px',\n                    display: 'block',\n                    opacity: imageLoaded ? 1 : 0.5\n                  }}\n                />\n              )}\n\n              {/* 原始图像覆盖层 */}\n              <div\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: `${splitPosition}%`,\n                  height: '100%',\n                  overflow: 'hidden',\n                  borderRadius: '8px 0 0 8px'\n                }}\n              >\n                <img\n                  src={originalImage}\n                  alt=\"原始图像\"\n                  style={{\n                    width: `${100 * 100 / splitPosition}%`,\n                    height: '100%',\n                    objectFit: 'cover',\n                    border: '2px solid #ddd',\n                    borderRadius: '8px'\n                  }}\n                />\n              </div>\n\n              {/* 分割线 */}\n              <div\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: `${splitPosition}%`,\n                  width: '4px',\n                  height: '100%',\n                  backgroundColor: '#fff',\n                  cursor: 'ew-resize',\n                  boxShadow: '0 0 10px rgba(0,0,0,0.3)',\n                  transform: 'translateX(-2px)',\n                  zIndex: 10\n                }}\n                onMouseDown={handleMouseDown}\n              >\n                {/* 分割线手柄 */}\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    width: '20px',\n                    height: '40px',\n                    backgroundColor: '#007bff',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '12px',\n                    fontWeight: 'bold'\n                  }}\n                >\n                  ⟷\n                </div>\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  color: '#6c757d',\n                  backgroundColor: 'rgba(255,255,255,0.8)',\n                  padding: '10px',\n                  borderRadius: '5px'\n                }}>\n                  加载中...\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      ) : (\n        // 并排对比模式\n        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', justifyContent: 'center' }}>\n          {/* 原始图像 */}\n          {originalImage && (\n            <div style={{ textAlign: 'center', flex: '1', minWidth: '300px', maxWidth: '400px' }}>\n              <h3 style={{ marginBottom: '10px', color: '#495057' }}>原始图像</h3>\n              <img\n                src={originalImage}\n                alt=\"原始图像\"\n                style={{\n                  width: '100%',\n                  height: 'auto',\n                  maxHeight: '400px',\n                  border: '2px solid #007bff',\n                  borderRadius: '8px',\n                  objectFit: 'contain'\n                }}\n              />\n            </div>\n          )}\n\n          {/* 增强图像 */}\n          <div style={{ textAlign: 'center', flex: '1', minWidth: '300px', maxWidth: '400px' }}>\n            <h3 style={{ marginBottom: '10px', color: '#495057' }}>增强图像</h3>\n\n            {!imageLoaded && !imageError && (\n              <div style={{\n                width: '100%',\n                height: '300px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '2px dashed #ccc',\n                borderRadius: '8px',\n                color: '#6c757d'\n              }}>\n                正在加载图像...\n              </div>\n            )}\n\n            {imageError && (\n              <div style={{\n                width: '100%',\n                height: '300px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '2px solid #dc3545',\n                borderRadius: '8px',\n                color: '#dc3545',\n                backgroundColor: '#f8d7da'\n              }}>\n                图像加载失败\n              </div>\n            )}\n\n            <img\n              src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n              alt=\"增强图像\"\n              onLoad={handleImageLoad}\n              onError={handleImageError}\n              style={{\n                width: '100%',\n                height: 'auto',\n                maxHeight: '400px',\n                border: '2px solid #28a745',\n                borderRadius: '8px',\n                objectFit: 'contain',\n                display: imageLoaded ? 'block' : 'none'\n              }}\n            />\n          </div>\n        </div>\n      )}\n\n      {imageLoaded && (\n        <div style={{ textAlign: 'center', marginTop: '20px' }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '10px 20px',\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '5px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              marginRight: '10px'\n            }}\n          >\n            📥 下载增强图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '10px 20px',\n              backgroundColor: '#007bff',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '5px',\n              fontSize: '16px'\n            }}\n          >\n            🔍 在新窗口查看\n          </a>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAA+B,IAA9B,CAAEC,MAAM,CAAEC,aAAc,CAAC,CAAAF,IAAA,CAC3C,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACiB,aAAa,CAAEC,gBAAgB,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAAE;AACxD,KAAM,CAACmB,UAAU,CAAEC,aAAa,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqB,QAAQ,CAAEC,WAAW,CAAC,CAAGtB,QAAQ,CAAC,OAAO,CAAC,CAAE;AACnD,KAAM,CAAAuB,YAAY,CAAGtB,MAAM,CAAC,IAAI,CAAC,CAEjC,KAAM,CAAAuB,eAAe,CAAGA,CAAA,GAAM,CAC5BZ,cAAc,CAAC,IAAI,CAAC,CACpBE,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAW,gBAAgB,CAAGA,CAAA,GAAM,CAC7BX,aAAa,CAAC,IAAI,CAAC,CACnBF,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAc,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAG,gCAAgCrB,MAAM,CAACsB,YAAY,EAAE,CACjEJ,IAAI,CAACK,QAAQ,CAAG,YAAYvB,MAAM,CAACwB,QAAQ,EAAE,CAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC,CAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC,CACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAW,eAAe,CAAIC,CAAC,EAAK,CAC7BnB,aAAa,CAAC,IAAI,CAAC,CACnBmB,CAAC,CAACC,cAAc,CAAC,CAAC,CACpB,CAAC,CAED,KAAM,CAAAC,eAAe,CAAIF,CAAC,EAAK,CAC7B,GAAI,CAACpB,UAAU,EAAI,CAACI,YAAY,CAACmB,OAAO,CAAE,OAE1C,KAAM,CAAAC,IAAI,CAAGpB,YAAY,CAACmB,OAAO,CAACE,qBAAqB,CAAC,CAAC,CACzD,KAAM,CAAAC,CAAC,CAAGN,CAAC,CAACO,OAAO,CAAGH,IAAI,CAACI,IAAI,CAC/B,KAAM,CAAAC,UAAU,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAAC,GAAG,CAAGN,CAAC,CAAGF,IAAI,CAACS,KAAK,CAAI,GAAG,CAAC,CAAC,CACrElC,gBAAgB,CAAC8B,UAAU,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAK,aAAa,CAAGA,CAAA,GAAM,CAC1BjC,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED;AACAlB,SAAS,CAAC,IAAM,CACd,GAAIiB,UAAU,CAAE,CACdS,QAAQ,CAAC0B,gBAAgB,CAAC,WAAW,CAAEb,eAAe,CAAC,CACvDb,QAAQ,CAAC0B,gBAAgB,CAAC,SAAS,CAAED,aAAa,CAAC,CACnD,MAAO,IAAM,CACXzB,QAAQ,CAAC2B,mBAAmB,CAAC,WAAW,CAAEd,eAAe,CAAC,CAC1Db,QAAQ,CAAC2B,mBAAmB,CAAC,SAAS,CAAEF,aAAa,CAAC,CACxD,CAAC,CACH,CACF,CAAC,CAAE,CAAClC,UAAU,CAAC,CAAC,CAEhB;AACA,KAAM,CAAAqC,UAAU,CAAGA,CAAA,GAAM,CACvBtC,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,mBACEZ,KAAA,QAAKmD,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,gBAAgB,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eACjGxD,KAAA,QAAKmD,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,YAAY,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAAN,QAAA,eAC1I1D,IAAA,OAAIqD,KAAK,CAAE,CAAEY,KAAK,CAAE,SAAS,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAC,iCAAM,CAAI,CAAC,cAGvDxD,KAAA,QAAKmD,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEK,GAAG,CAAE,KAAK,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC5D1D,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMjD,WAAW,CAAC,OAAO,CAAE,CACpCmC,KAAK,CAAE,CACLE,OAAO,CAAE,UAAU,CACnBa,eAAe,CAAEnD,QAAQ,GAAK,OAAO,CAAG,SAAS,CAAG,SAAS,CAC7DgD,KAAK,CAAE,OAAO,CACdT,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBY,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAZ,QAAA,CACH,0BAED,CAAQ,CAAC,cACT1D,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMjD,WAAW,CAAC,cAAc,CAAE,CAC3CmC,KAAK,CAAE,CACLE,OAAO,CAAE,UAAU,CACnBa,eAAe,CAAEnD,QAAQ,GAAK,cAAc,CAAG,SAAS,CAAG,SAAS,CACpEgD,KAAK,CAAE,OAAO,CACdT,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBY,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAZ,QAAA,CACH,0BAED,CAAQ,CAAC,CACRzC,QAAQ,GAAK,OAAO,eACnBjB,IAAA,WACEmE,OAAO,CAAEf,UAAW,CACpBC,KAAK,CAAE,CACLE,OAAO,CAAE,UAAU,CACnBa,eAAe,CAAE,SAAS,CAC1BH,KAAK,CAAE,OAAO,CACdT,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBY,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAZ,QAAA,CACH,cAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,CAELrD,MAAM,CAACkE,OAAO,eACbvE,IAAA,MAAGqD,KAAK,CAAE,CAAEY,KAAK,CAAE,SAAS,CAAEH,YAAY,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAErD,MAAM,CAACkE,OAAO,CAAI,CAC1E,CAGAlE,MAAM,CAACmE,WAAW,eACjBtE,KAAA,QAAKmD,KAAK,CAAE,CAAES,YAAY,CAAE,MAAM,CAAEP,OAAO,CAAE,MAAM,CAAEa,eAAe,CAAE,SAAS,CAAEX,YAAY,CAAE,KAAM,CAAE,CAAAC,QAAA,eACrGxD,KAAA,QAAKmD,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAJ,QAAA,eAC1E1D,IAAA,OAAIqD,KAAK,CAAE,CAAEa,MAAM,CAAE,CAAC,CAAEO,WAAW,CAAE,MAAO,CAAE,CAAAf,QAAA,CAAC,0BAAI,CAAI,CAAC,cACxD1D,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMvD,aAAa,CAAC,CAACD,UAAU,CAAE,CAC1C0C,KAAK,CAAE,CACLE,OAAO,CAAE,SAAS,CAClBa,eAAe,CAAE,SAAS,CAC1BH,KAAK,CAAE,OAAO,CACdT,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBY,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAZ,QAAA,CAED/C,UAAU,CAAG,IAAI,CAAG,IAAI,CACnB,CAAC,EACN,CAAC,CAELA,UAAU,eACTX,IAAA,QAAKqD,KAAK,CAAE,CAAEiB,QAAQ,CAAE,MAAM,CAAEL,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,cACjDxD,KAAA,QAAKmD,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEe,mBAAmB,CAAE,sCAAsC,CAAEV,GAAG,CAAE,KAAM,CAAE,CAAAN,QAAA,eACvGxD,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACrD,MAAM,CAACmE,WAAW,CAACG,KAAK,CAAC,GAAC,EAAK,CAAC,cAC7DzE,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,iBAAK,CAAQ,CAAC,IAAC,CAACrD,MAAM,CAACmE,WAAW,CAACI,cAAc,CAAG,YAAY,CAAG,MAAM,EAAM,CAAC,cAC7F1E,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAAC,CAACrD,MAAM,CAACmE,WAAW,CAACK,UAAU,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,cACnF5E,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACrD,MAAM,CAACmE,WAAW,CAACO,SAAS,EAAM,CAAC,cAC9D7E,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,qBAAI,CAAQ,CAAC,IAAC,CAACrD,MAAM,CAACmE,WAAW,CAACQ,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cAC3E5E,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,qBAAI,CAAQ,CAAC,IAAC,CAACrD,MAAM,CAACmE,WAAW,CAACS,QAAQ,CAACH,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cACzE5E,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACrD,MAAM,CAACmE,WAAW,CAACU,UAAU,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAE7E,MAAM,CAACmE,WAAW,CAACU,UAAU,EAAM,CAAC,cAC7GhF,KAAA,QAAAwD,QAAA,eAAK1D,IAAA,WAAA0D,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAAC,CAACrD,MAAM,CAACmE,WAAW,CAACW,MAAM,CAAG,GAAG,EAAEL,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,EAC5E,CAAC,CACH,CACN,EACE,CACN,CAGA7D,QAAQ,GAAK,OAAO,cACnB;AACAf,KAAA,QACEkF,GAAG,CAAEjE,YAAa,CAClBkC,KAAK,CAAE,CACLgC,QAAQ,CAAE,UAAU,CACpBrC,KAAK,CAAE,MAAM,CACbsC,QAAQ,CAAE,OAAO,CACjBpB,MAAM,CAAE,QAAQ,CAChBG,MAAM,CAAEtD,UAAU,CAAG,WAAW,CAAG,SAAS,CAC5CwE,UAAU,CAAE,MACd,CAAE,CAAA7B,QAAA,eAEFxD,KAAA,QAAKmD,KAAK,CAAE,CAAEmC,SAAS,CAAE,QAAQ,CAAE1B,YAAY,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACxD1D,IAAA,OAAIqD,KAAK,CAAE,CAAEa,MAAM,CAAE,CAAC,CAAED,KAAK,CAAE,SAAS,CAAEK,QAAQ,CAAE,MAAO,CAAE,CAAAZ,QAAA,CAAC,sFAAc,CAAI,CAAC,cACjFxD,KAAA,MAAGmD,KAAK,CAAE,CAAEa,MAAM,CAAE,OAAO,CAAEI,QAAQ,CAAE,MAAM,CAAEL,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,EAAC,0HACxC,CAAC7C,aAAa,CAACiE,OAAO,CAAC,CAAC,CAAC,CAAC,GACrD,EAAG,CAAC,EACD,CAAC,CAELxE,aAAa,eACZJ,KAAA,QAAKmD,KAAK,CAAE,CAAEgC,QAAQ,CAAE,UAAU,CAAE1B,OAAO,CAAE,cAAc,CAAEX,KAAK,CAAE,MAAO,CAAE,CAAAU,QAAA,EAE1EjD,UAAU,cACTT,IAAA,QAAKqD,KAAK,CAAE,CACVL,KAAK,CAAE,MAAM,CACbyC,MAAM,CAAE,OAAO,CACfjC,MAAM,CAAE,oBAAoB,CAC5BC,YAAY,CAAE,KAAK,CACnBE,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBK,KAAK,CAAE,SAAS,CAChBG,eAAe,CAAE,SACnB,CAAE,CAAAV,QAAA,CAAC,kDAEH,CAAK,CAAC,cAEN1D,IAAA,QACE0F,GAAG,CAAE,gCAAgCrF,MAAM,CAACsB,YAAY,MAAMgE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CAC3EC,GAAG,CAAC,0BAAM,CACVC,MAAM,CAAE1E,eAAgB,CACxB2E,OAAO,CAAE1E,gBAAiB,CAC1BgC,KAAK,CAAE,CACLL,KAAK,CAAE,MAAM,CACbsC,QAAQ,CAAE,OAAO,CACjBG,MAAM,CAAE,MAAM,CACdjC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBE,OAAO,CAAE,OAAO,CAChBqC,OAAO,CAAEzF,WAAW,CAAG,CAAC,CAAG,GAC7B,CAAE,CACH,CACF,cAGDP,IAAA,QACEqD,KAAK,CAAE,CACLgC,QAAQ,CAAE,UAAU,CACpBY,GAAG,CAAE,CAAC,CACNtD,IAAI,CAAE,CAAC,CACPK,KAAK,CAAE,GAAGnC,aAAa,GAAG,CAC1B4E,MAAM,CAAE,MAAM,CACdS,QAAQ,CAAE,QAAQ,CAClBzC,YAAY,CAAE,aAChB,CAAE,CAAAC,QAAA,cAEF1D,IAAA,QACE0F,GAAG,CAAEpF,aAAc,CACnBuF,GAAG,CAAC,0BAAM,CACVxC,KAAK,CAAE,CACLL,KAAK,CAAE,GAAG,GAAG,CAAG,GAAG,CAAGnC,aAAa,GAAG,CACtC4E,MAAM,CAAE,MAAM,CACdU,SAAS,CAAE,OAAO,CAClB3C,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CACC,CAAC,cAGNzD,IAAA,QACEqD,KAAK,CAAE,CACLgC,QAAQ,CAAE,UAAU,CACpBY,GAAG,CAAE,CAAC,CACNtD,IAAI,CAAE,GAAG9B,aAAa,GAAG,CACzBmC,KAAK,CAAE,KAAK,CACZyC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,MAAM,CACvBC,MAAM,CAAE,WAAW,CACnB+B,SAAS,CAAE,0BAA0B,CACrCC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,EACV,CAAE,CACFC,WAAW,CAAErE,eAAgB,CAAAwB,QAAA,cAG7B1D,IAAA,QACEqD,KAAK,CAAE,CACLgC,QAAQ,CAAE,UAAU,CACpBY,GAAG,CAAE,KAAK,CACVtD,IAAI,CAAE,KAAK,CACX0D,SAAS,CAAE,uBAAuB,CAClCrD,KAAK,CAAE,MAAM,CACbyC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,SAAS,CAC1BX,YAAY,CAAE,MAAM,CACpBE,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBK,KAAK,CAAE,OAAO,CACdK,QAAQ,CAAE,MAAM,CAChBkC,UAAU,CAAE,MACd,CAAE,CAAA9C,QAAA,CACH,QAED,CAAK,CAAC,CACH,CAAC,CAEL,CAACnD,WAAW,EAAI,CAACE,UAAU,eAC1BT,IAAA,QAAKqD,KAAK,CAAE,CACVgC,QAAQ,CAAE,UAAU,CACpBY,GAAG,CAAE,KAAK,CACVtD,IAAI,CAAE,KAAK,CACX0D,SAAS,CAAE,uBAAuB,CAClCpC,KAAK,CAAE,SAAS,CAChBG,eAAe,CAAE,uBAAuB,CACxCb,OAAO,CAAE,MAAM,CACfE,YAAY,CAAE,KAChB,CAAE,CAAAC,QAAA,CAAC,uBAEH,CAAK,CACN,EACE,CACN,EACE,CAAC,cAEN;AACAxD,KAAA,QAAKmD,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEK,GAAG,CAAE,MAAM,CAAED,QAAQ,CAAE,MAAM,CAAEH,cAAc,CAAE,QAAS,CAAE,CAAAF,QAAA,EAEtFpD,aAAa,eACZJ,KAAA,QAAKmD,KAAK,CAAE,CAAEmC,SAAS,CAAE,QAAQ,CAAEiB,IAAI,CAAE,GAAG,CAAEC,QAAQ,CAAE,OAAO,CAAEpB,QAAQ,CAAE,OAAQ,CAAE,CAAA5B,QAAA,eACnF1D,IAAA,OAAIqD,KAAK,CAAE,CAAES,YAAY,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,CAAC,0BAAI,CAAI,CAAC,cAChE1D,IAAA,QACE0F,GAAG,CAAEpF,aAAc,CACnBuF,GAAG,CAAC,0BAAM,CACVxC,KAAK,CAAE,CACLL,KAAK,CAAE,MAAM,CACbyC,MAAM,CAAE,MAAM,CACdkB,SAAS,CAAE,OAAO,CAClBnD,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnB0C,SAAS,CAAE,SACb,CAAE,CACH,CAAC,EACC,CACN,cAGDjG,KAAA,QAAKmD,KAAK,CAAE,CAAEmC,SAAS,CAAE,QAAQ,CAAEiB,IAAI,CAAE,GAAG,CAAEC,QAAQ,CAAE,OAAO,CAAEpB,QAAQ,CAAE,OAAQ,CAAE,CAAA5B,QAAA,eACnF1D,IAAA,OAAIqD,KAAK,CAAE,CAAES,YAAY,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,CAAC,0BAAI,CAAI,CAAC,CAE/D,CAACnD,WAAW,EAAI,CAACE,UAAU,eAC1BT,IAAA,QAAKqD,KAAK,CAAE,CACVL,KAAK,CAAE,MAAM,CACbyC,MAAM,CAAE,OAAO,CACf9B,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBJ,MAAM,CAAE,iBAAiB,CACzBC,YAAY,CAAE,KAAK,CACnBQ,KAAK,CAAE,SACT,CAAE,CAAAP,QAAA,CAAC,yCAEH,CAAK,CACN,CAEAjD,UAAU,eACTT,IAAA,QAAKqD,KAAK,CAAE,CACVL,KAAK,CAAE,MAAM,CACbyC,MAAM,CAAE,OAAO,CACf9B,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBJ,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBQ,KAAK,CAAE,SAAS,CAChBG,eAAe,CAAE,SACnB,CAAE,CAAAV,QAAA,CAAC,sCAEH,CAAK,CACN,cAED1D,IAAA,QACE0F,GAAG,CAAE,gCAAgCrF,MAAM,CAACsB,YAAY,MAAMgE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CAC3EC,GAAG,CAAC,0BAAM,CACVC,MAAM,CAAE1E,eAAgB,CACxB2E,OAAO,CAAE1E,gBAAiB,CAC1BgC,KAAK,CAAE,CACLL,KAAK,CAAE,MAAM,CACbyC,MAAM,CAAE,MAAM,CACdkB,SAAS,CAAE,OAAO,CAClBnD,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnB0C,SAAS,CAAE,SAAS,CACpBxC,OAAO,CAAEpD,WAAW,CAAG,OAAO,CAAG,MACnC,CAAE,CACH,CAAC,EACC,CAAC,EACH,CACN,CAEAA,WAAW,eACVL,KAAA,QAAKmD,KAAK,CAAE,CAAEmC,SAAS,CAAE,QAAQ,CAAElC,SAAS,CAAE,MAAO,CAAE,CAAAI,QAAA,eACrD1D,IAAA,WACEmE,OAAO,CAAE7C,aAAc,CACvB+B,KAAK,CAAE,CACLE,OAAO,CAAE,WAAW,CACpBa,eAAe,CAAE,SAAS,CAC1BH,KAAK,CAAE,OAAO,CACdT,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBY,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBG,WAAW,CAAE,MACf,CAAE,CAAAf,QAAA,CACH,mDAED,CAAQ,CAAC,cAET1D,IAAA,MACE0B,IAAI,CAAE,gCAAgCrB,MAAM,CAACsB,YAAY,EAAG,CAC5DiF,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBxD,KAAK,CAAE,CACLE,OAAO,CAAE,WAAW,CACpBa,eAAe,CAAE,SAAS,CAC1BH,KAAK,CAAE,OAAO,CACd6C,cAAc,CAAE,MAAM,CACtBrD,YAAY,CAAE,KAAK,CACnBa,QAAQ,CAAE,MACZ,CAAE,CAAAZ,QAAA,CACH,mDAED,CAAG,CAAC,EACD,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}