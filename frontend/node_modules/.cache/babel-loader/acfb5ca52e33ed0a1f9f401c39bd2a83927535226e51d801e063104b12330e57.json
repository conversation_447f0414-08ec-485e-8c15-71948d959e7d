{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/').then(res => res.json()).then(data => {\n      if (data && data.presets) {\n        setPresets(data.presets);\n        if (data.presets.default) {\n          setParams(data.presets.default.params);\n        }\n      } else {\n        console.warn('预设配置格式不正确:', data);\n        // 设置默认的预设配置\n        setPresets({\n          default: {\n            params: params\n          },\n          portrait: {\n            params: {\n              ...params,\n              beauty: 0.3,\n              sharpening: 0.2\n            }\n          },\n          landscape: {\n            params: {\n              ...params,\n              saturation: 1.2,\n              contrast: 1.1\n            }\n          },\n          vintage: {\n            params: {\n              ...params,\n              saturation: 0.8,\n              contrast: 0.9\n            }\n          },\n          fast: {\n            params: {\n              ...params,\n              use_realesrgan: false\n            }\n          }\n        });\n      }\n    }).catch(err => {\n      console.error('获取预设配置失败:', err);\n      // 设置默认的预设配置\n      setPresets({\n        default: {\n          params: params\n        },\n        portrait: {\n          params: {\n            ...params,\n            beauty: 0.3,\n            sharpening: 0.2\n          }\n        },\n        landscape: {\n          params: {\n            ...params,\n            saturation: 1.2,\n            contrast: 1.1\n          }\n        },\n        vintage: {\n          params: {\n            ...params,\n            saturation: 0.8,\n            contrast: 0.9\n          }\n        },\n        fast: {\n          params: {\n            ...params,\n            use_realesrgan: false\n          }\n        }\n      });\n    });\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n      setSelectedFile(file);\n      setError(null);\n      const reader = new FileReader();\n      reader.onload = e => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n  const SliderControl = ({\n    label,\n    value,\n    min,\n    max,\n    step,\n    onChange,\n    unit = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '6px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          color: '#ddd',\n          fontSize: '13px',\n          fontWeight: '500'\n        },\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#aaa',\n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        },\n        children: [typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value, unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"range\",\n      min: min,\n      max: max,\n      step: step,\n      value: value,\n      onChange: e => onChange(parseFloat(e.target.value)),\n      style: {\n        width: '100%',\n        height: '4px',\n        borderRadius: '2px',\n        background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${(value - min) / (max - min) * 100}%, #555 ${(value - min) / (max - min) * 100}%, #555 100%)`,\n        outline: 'none',\n        appearance: 'none',\n        cursor: 'pointer'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #555'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '8px',\n            backgroundColor: '#333',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            color: '#ddd',\n            fontSize: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '12px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: previewUrl,\n            alt: \"\\u9884\\u89C8\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '120px',\n              borderRadius: '4px',\n              border: '1px solid #555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            color: '#ff6b6b',\n            fontSize: '12px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #555'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 12px 0',\n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          },\n          children: \"\\u57FA\\u672C\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '6px',\n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            },\n            children: \"\\u9884\\u8BBE\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedPreset,\n            onChange: e => handlePresetChange(e.target.value),\n            style: {\n              width: '100%',\n              padding: '6px 8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"default\",\n              children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"portrait\",\n              children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"landscape\",\n              children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"vintage\",\n              children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"fast\",\n              children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"custom\",\n              children: \"\\u81EA\\u5B9A\\u4E49\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n          label: \"\\u8D85\\u5206\\u500D\\u6570\",\n          value: params.scale,\n          min: 2,\n          max: 4,\n          step: 2,\n          onChange: value => handleParamChange('scale', value),\n          unit: \"x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              cursor: 'pointer',\n              color: '#ddd',\n              fontSize: '13px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: params.use_realesrgan,\n              onChange: e => handleParamChange('use_realesrgan', e.target.checked),\n              style: {\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), \"\\u4F7F\\u7528RealESRGAN\\u6A21\\u578B\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u9510\\u5316\\u548C\\u964D\\u566A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u9510\\u5316\",\n            value: params.sharpening,\n            min: 0,\n            max: 1,\n            step: 0.05,\n            onChange: value => handleParamChange('sharpening', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u964D\\u566A\",\n            value: params.denoising,\n            min: 0,\n            max: 30,\n            step: 1,\n            onChange: value => handleParamChange('denoising', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u8272\\u5F69\\u8C03\\u6574\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u9971\\u548C\\u5EA6\",\n            value: params.saturation,\n            min: 0,\n            max: 2,\n            step: 0.1,\n            onChange: value => handleParamChange('saturation', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u5BF9\\u6BD4\\u5EA6\",\n            value: params.contrast,\n            min: 0,\n            max: 2,\n            step: 0.05,\n            onChange: value => handleParamChange('contrast', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u4EAE\\u5EA6\",\n            value: params.brightness,\n            min: -100,\n            max: 100,\n            step: 5,\n            onChange: value => handleParamChange('brightness', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u7F8E\\u989C\\u6548\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u7F8E\\u989C\\u5F3A\\u5EA6\",\n            value: params.beauty,\n            min: 0,\n            max: 1,\n            step: 0.05,\n            onChange: value => handleParamChange('beauty', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"xSC9RCNijr9WJFhDDQlEUkoMNHo=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fetch", "then", "res", "json", "data", "default", "console", "warn", "portrait", "landscape", "vintage", "fast", "catch", "err", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "size", "type", "startsWith", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "SliderControl", "label", "min", "max", "step", "onChange", "unit", "style", "marginBottom", "children", "display", "justifyContent", "alignItems", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "min<PERSON><PERSON><PERSON>", "textAlign", "toFixed", "parseFloat", "width", "height", "borderRadius", "background", "outline", "appearance", "cursor", "flexDirection", "onSubmit", "flex", "padding", "borderBottom", "accept", "required", "disabled", "backgroundColor", "border", "marginTop", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "margin", "checked", "marginRight", "overflowY", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/')\n      .then(res => res.json())\n      .then(data => {\n        if (data && data.presets) {\n          setPresets(data.presets);\n          if (data.presets.default) {\n            setParams(data.presets.default.params);\n          }\n        } else {\n          console.warn('预设配置格式不正确:', data);\n          // 设置默认的预设配置\n          setPresets({\n            default: { params: params },\n            portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },\n            landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },\n            vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },\n            fast: { params: { ...params, use_realesrgan: false } }\n          });\n        }\n      })\n      .catch(err => {\n        console.error('获取预设配置失败:', err);\n        // 设置默认的预设配置\n        setPresets({\n          default: { params: params },\n          portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },\n          landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },\n          vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },\n          fast: { params: { ...params, use_realesrgan: false } }\n        });\n      });\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      setSelectedFile(file);\n      setError(null);\n\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    \n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  const SliderControl = ({ label, value, min, max, step, onChange, unit = '' }) => (\n    <div style={{ marginBottom: '16px' }}>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        marginBottom: '6px'\n      }}>\n        <label style={{ \n          color: '#ddd', \n          fontSize: '13px',\n          fontWeight: '500'\n        }}>\n          {label}\n        </label>\n        <span style={{ \n          color: '#aaa', \n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        }}>\n          {typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value}{unit}\n        </span>\n      </div>\n      <input \n        type=\"range\"\n        min={min}\n        max={max}\n        step={step}\n        value={value}\n        onChange={(e) => onChange(parseFloat(e.target.value))}\n        style={{\n          width: '100%',\n          height: '4px',\n          borderRadius: '2px',\n          background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${((value - min) / (max - min)) * 100}%, #555 ${((value - min) / (max - min)) * 100}%, #555 100%)`,\n          outline: 'none',\n          appearance: 'none',\n          cursor: 'pointer'\n        }}\n      />\n    </div>\n  );\n\n  return (\n    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <form onSubmit={handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n        \n        {/* 文件上传区域 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <label style={{ \n            display: 'block', \n            marginBottom: '8px', \n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          }}>\n            选择图像文件\n          </label>\n          <input\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{\n              width: '100%',\n              padding: '8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            }}\n          />\n          \n          {previewUrl && (\n            <div style={{ marginTop: '12px', textAlign: 'center' }}>\n              <img\n                src={previewUrl}\n                alt=\"预览\"\n                style={{\n                  maxWidth: '100%',\n                  maxHeight: '120px',\n                  borderRadius: '4px',\n                  border: '1px solid #555'\n                }}\n              />\n            </div>\n          )}\n          \n          {error && (\n            <div style={{ \n              marginTop: '8px',\n              color: '#ff6b6b', \n              fontSize: '12px'\n            }}>\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 基本设置 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <h3 style={{ \n            margin: '0 0 12px 0', \n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          }}>\n            基本设置\n          </h3>\n          \n          {/* 预设选择 */}\n          <div style={{ marginBottom: '16px' }}>\n            <label style={{ \n              display: 'block', \n              marginBottom: '6px', \n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            }}>\n              预设配置\n            </label>\n            <select \n              value={selectedPreset}\n              onChange={(e) => handlePresetChange(e.target.value)}\n              style={{ \n                width: '100%', \n                padding: '6px 8px', \n                backgroundColor: '#333',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                color: '#ddd',\n                fontSize: '12px'\n              }}\n            >\n              <option value=\"default\">默认设置</option>\n              <option value=\"portrait\">人像优化</option>\n              <option value=\"landscape\">风景增强</option>\n              <option value=\"vintage\">复古风格</option>\n              <option value=\"fast\">快速处理</option>\n              <option value=\"custom\">自定义</option>\n            </select>\n          </div>\n\n          {/* 超分倍数 */}\n          <SliderControl\n            label=\"超分倍数\"\n            value={params.scale}\n            min={2}\n            max={4}\n            step={2}\n            onChange={(value) => handleParamChange('scale', value)}\n            unit=\"x\"\n          />\n\n          {/* RealESRGAN开关 */}\n          <div style={{ marginBottom: '16px' }}>\n            <label style={{ \n              display: 'flex', \n              alignItems: 'center', \n              cursor: 'pointer',\n              color: '#ddd',\n              fontSize: '13px'\n            }}>\n              <input \n                type=\"checkbox\"\n                checked={params.use_realesrgan}\n                onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}\n                style={{ marginRight: '8px' }}\n              />\n              使用RealESRGAN模型\n            </label>\n          </div>\n        </div>\n\n        {/* 高级调整 */}\n        <div style={{\n          flex: 1,\n          overflowY: 'auto'\n        }}>\n          {/* 锐化和降噪 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              锐化和降噪\n            </h3>\n\n            <SliderControl\n              label=\"锐化\"\n              value={params.sharpening}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('sharpening', value)}\n            />\n\n            <SliderControl\n              label=\"降噪\"\n              value={params.denoising}\n              min={0}\n              max={30}\n              step={1}\n              onChange={(value) => handleParamChange('denoising', value)}\n            />\n          </div>\n\n          {/* 色彩调整 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              色彩调整\n            </h3>\n\n            <SliderControl\n              label=\"饱和度\"\n              value={params.saturation}\n              min={0}\n              max={2}\n              step={0.1}\n              onChange={(value) => handleParamChange('saturation', value)}\n            />\n\n            <SliderControl\n              label=\"对比度\"\n              value={params.contrast}\n              min={0}\n              max={2}\n              step={0.05}\n              onChange={(value) => handleParamChange('contrast', value)}\n            />\n\n            <SliderControl\n              label=\"亮度\"\n              value={params.brightness}\n              min={-100}\n              max={100}\n              step={5}\n              onChange={(value) => handleParamChange('brightness', value)}\n            />\n          </div>\n\n          {/* 美颜效果 */}\n          <div style={{\n            padding: '16px'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              美颜效果\n            </h3>\n\n            <SliderControl\n              label=\"美颜强度\"\n              value={params.beauty}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('beauty', value)}\n            />\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default UploadForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC;IACnCoB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA1B,SAAS,CAAC,MAAM;IACd2B,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAAClB,OAAO,EAAE;QACxBC,UAAU,CAACiB,IAAI,CAAClB,OAAO,CAAC;QACxB,IAAIkB,IAAI,CAAClB,OAAO,CAACmB,OAAO,EAAE;UACxBd,SAAS,CAACa,IAAI,CAAClB,OAAO,CAACmB,OAAO,CAACf,MAAM,CAAC;QACxC;MACF,CAAC,MAAM;QACLgB,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEH,IAAI,CAAC;QAChC;QACAjB,UAAU,CAAC;UACTkB,OAAO,EAAE;YAAEf,MAAM,EAAEA;UAAO,CAAC;UAC3BkB,QAAQ,EAAE;YAAElB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAES,MAAM,EAAE,GAAG;cAAEL,UAAU,EAAE;YAAI;UAAE,CAAC;UACjEe,SAAS,EAAE;YAAEnB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEM,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAI;UAAE,CAAC;UACpEa,OAAO,EAAE;YAAEpB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEM,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAI;UAAE,CAAC;UAClEc,IAAI,EAAE;YAAErB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEG,cAAc,EAAE;YAAM;UAAE;QACvD,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACDmB,KAAK,CAACC,GAAG,IAAI;MACZP,OAAO,CAACtB,KAAK,CAAC,WAAW,EAAE6B,GAAG,CAAC;MAC/B;MACA1B,UAAU,CAAC;QACTkB,OAAO,EAAE;UAAEf,MAAM,EAAEA;QAAO,CAAC;QAC3BkB,QAAQ,EAAE;UAAElB,MAAM,EAAE;YAAE,GAAGA,MAAM;YAAES,MAAM,EAAE,GAAG;YAAEL,UAAU,EAAE;UAAI;QAAE,CAAC;QACjEe,SAAS,EAAE;UAAEnB,MAAM,EAAE;YAAE,GAAGA,MAAM;YAAEM,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI;QAAE,CAAC;QACpEa,OAAO,EAAE;UAAEpB,MAAM,EAAE;YAAE,GAAGA,MAAM;YAAEM,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI;QAAE,CAAC;QAClEc,IAAI,EAAE;UAAErB,MAAM,EAAE;YAAE,GAAGA,MAAM;YAAEG,cAAc,EAAE;UAAM;QAAE;MACvD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,kBAAkB,GAAIC,SAAS,IAAK;IACxC1B,iBAAiB,CAAC0B,SAAS,CAAC;IAC5B,IAAI7B,OAAO,IAAIA,OAAO,CAAC6B,SAAS,CAAC,EAAE;MACjCxB,SAAS,CAACL,OAAO,CAAC6B,SAAS,CAAC,CAACzB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxC3B,SAAS,CAAC4B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACH7B,iBAAiB,CAAC,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAM+B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,IAAIA,IAAI,CAACG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCxC,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;MAEA,IAAI,CAACqC,IAAI,CAACI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnC1C,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;MAEAJ,eAAe,CAACyC,IAAI,CAAC;MACrBrC,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM2C,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIT,CAAC,IAAKtC,aAAa,CAACsC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLzC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMkD,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAMkD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEzD,YAAY,CAAC;IACrCuD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACjD,MAAM,CAAC,CAAC;IACjDb,QAAQ,CAAC0D,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAC;IAAEC,KAAK;IAAEvB,KAAK;IAAEwB,GAAG;IAAEC,GAAG;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,IAAI,GAAG;EAAG,CAAC,kBAC1EvE,OAAA;IAAKwE,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACnC1E,OAAA;MAAKwE,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBJ,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,gBACA1E,OAAA;QAAOwE,KAAK,EAAE;UACZM,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAN,QAAA,EACCR;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACRpF,OAAA;QAAMwE,KAAK,EAAE;UACXM,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBM,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAE;QAAAZ,QAAA,GACC,OAAO/B,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC4C,OAAO,CAAClB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG1B,KAAK,EAAE4B,IAAI;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNpF,OAAA;MACEmD,IAAI,EAAC,OAAO;MACZgB,GAAG,EAAEA,GAAI;MACTC,GAAG,EAAEA,GAAI;MACTC,IAAI,EAAEA,IAAK;MACX1B,KAAK,EAAEA,KAAM;MACb2B,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAACkB,UAAU,CAAC1C,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;MACtD6B,KAAK,EAAE;QACLiB,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,KAAK;QACbC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,iDAAkD,CAACjD,KAAK,GAAGwB,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,WAAY,CAACxB,KAAK,GAAGwB,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,eAAe;QAC7J0B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE;MACV;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACEpF,OAAA;IAAKwE,KAAK,EAAE;MAAEkB,MAAM,EAAE,MAAM;MAAEf,OAAO,EAAE,MAAM;MAAEqB,aAAa,EAAE;IAAS,CAAE;IAAAtB,QAAA,eACvE1E,OAAA;MAAMiG,QAAQ,EAAEvC,YAAa;MAACc,KAAK,EAAE;QAAE0B,IAAI,EAAE,CAAC;QAAEvB,OAAO,EAAE,MAAM;QAAEqB,aAAa,EAAE;MAAS,CAAE;MAAAtB,QAAA,gBAGzF1E,OAAA;QAAKwE,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA1B,QAAA,gBACA1E,OAAA;UAAOwE,KAAK,EAAE;YACZG,OAAO,EAAE,OAAO;YAChBF,YAAY,EAAE,KAAK;YACnBK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpF,OAAA;UACEmD,IAAI,EAAC,MAAM;UACXmB,QAAQ,EAAEzB,gBAAiB;UAC3BwD,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAEpG,SAAU;UACpBqE,KAAK,EAAE;YACLiB,KAAK,EAAE,MAAM;YACbU,OAAO,EAAE,KAAK;YACdK,eAAe,EAAE,MAAM;YACvBC,MAAM,EAAE,gBAAgB;YACxBd,YAAY,EAAE,KAAK;YACnBb,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE;UACZ;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAED7E,UAAU,iBACTP,OAAA;UAAKwE,KAAK,EAAE;YAAEkC,SAAS,EAAE,MAAM;YAAEpB,SAAS,EAAE;UAAS,CAAE;UAAAZ,QAAA,eACrD1E,OAAA;YACE2G,GAAG,EAAEpG,UAAW;YAChBqG,GAAG,EAAC,cAAI;YACRpC,KAAK,EAAE;cACLqC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClBnB,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE;YACV;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA3E,KAAK,iBACJT,OAAA;UAAKwE,KAAK,EAAE;YACVkC,SAAS,EAAE,KAAK;YAChB5B,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,EACCjE;QAAK;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpF,OAAA;QAAKwE,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA1B,QAAA,gBACA1E,OAAA;UAAIwE,KAAK,EAAE;YACTuC,MAAM,EAAE,YAAY;YACpBjC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGLpF,OAAA;UAAKwE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACnC1E,OAAA;YAAOwE,KAAK,EAAE;cACZG,OAAO,EAAE,OAAO;cAChBF,YAAY,EAAE,KAAK;cACnBK,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRpF,OAAA;YACE2C,KAAK,EAAE9B,cAAe;YACtByD,QAAQ,EAAGxB,CAAC,IAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;YACpD6B,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACbU,OAAO,EAAE,SAAS;cAClBK,eAAe,EAAE,MAAM;cACvBC,MAAM,EAAE,gBAAgB;cACxBd,YAAY,EAAE,KAAK;cACnBb,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,gBAEF1E,OAAA;cAAQ2C,KAAK,EAAC,SAAS;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCpF,OAAA;cAAQ2C,KAAK,EAAC,UAAU;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCpF,OAAA;cAAQ2C,KAAK,EAAC,WAAW;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCpF,OAAA;cAAQ2C,KAAK,EAAC,SAAS;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCpF,OAAA;cAAQ2C,KAAK,EAAC,MAAM;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCpF,OAAA;cAAQ2C,KAAK,EAAC,QAAQ;cAAA+B,QAAA,EAAC;YAAG;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNpF,OAAA,CAACiE,aAAa;UACZC,KAAK,EAAC,0BAAM;UACZvB,KAAK,EAAE5B,MAAM,CAACE,KAAM;UACpBkD,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,CAAE;UACRC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,OAAO,EAAEE,KAAK,CAAE;UACvD4B,IAAI,EAAC;QAAG;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGFpF,OAAA;UAAKwE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,eACnC1E,OAAA;YAAOwE,KAAK,EAAE;cACZG,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBkB,MAAM,EAAE,SAAS;cACjBjB,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,gBACA1E,OAAA;cACEmD,IAAI,EAAC,UAAU;cACf6D,OAAO,EAAEjG,MAAM,CAACG,cAAe;cAC/BoD,QAAQ,EAAGxB,CAAC,IAAKL,iBAAiB,CAAC,gBAAgB,EAAEK,CAAC,CAACE,MAAM,CAACgE,OAAO,CAAE;cACvExC,KAAK,EAAE;gBAAEyC,WAAW,EAAE;cAAM;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,sCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA;QAAKwE,KAAK,EAAE;UACV0B,IAAI,EAAE,CAAC;UACPgB,SAAS,EAAE;QACb,CAAE;QAAAxC,QAAA,gBAEA1E,OAAA;UAAKwE,KAAK,EAAE;YACV2B,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,gBACA1E,OAAA;YAAIwE,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELpF,OAAA,CAACiE,aAAa;YACZC,KAAK,EAAC,cAAI;YACVvB,KAAK,EAAE5B,MAAM,CAACI,UAAW;YACzBgD,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAEFpF,OAAA,CAACiE,aAAa;YACZC,KAAK,EAAC,cAAI;YACVvB,KAAK,EAAE5B,MAAM,CAACK,SAAU;YACxB+C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRC,IAAI,EAAE,CAAE;YACRC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,WAAW,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpF,OAAA;UAAKwE,KAAK,EAAE;YACV2B,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,gBACA1E,OAAA;YAAIwE,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELpF,OAAA,CAACiE,aAAa;YACZC,KAAK,EAAC,oBAAK;YACXvB,KAAK,EAAE5B,MAAM,CAACM,UAAW;YACzB8C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,GAAI;YACVC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAEFpF,OAAA,CAACiE,aAAa;YACZC,KAAK,EAAC,oBAAK;YACXvB,KAAK,EAAE5B,MAAM,CAACO,QAAS;YACvB6C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,UAAU,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEFpF,OAAA,CAACiE,aAAa;YACZC,KAAK,EAAC,cAAI;YACVvB,KAAK,EAAE5B,MAAM,CAACQ,UAAW;YACzB4C,GAAG,EAAE,CAAC,GAAI;YACVC,GAAG,EAAE,GAAI;YACTC,IAAI,EAAE,CAAE;YACRC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpF,OAAA;UAAKwE,KAAK,EAAE;YACV2B,OAAO,EAAE;UACX,CAAE;UAAAzB,QAAA,gBACA1E,OAAA;YAAIwE,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELpF,OAAA,CAACiE,aAAa;YACZC,KAAK,EAAC,0BAAM;YACZvB,KAAK,EAAE5B,MAAM,CAACS,MAAO;YACrB2C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,QAAQ,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChF,EAAA,CA7YIH,UAAU;AAAAkH,EAAA,GAAVlH,UAAU;AA+YhB,eAAeA,UAAU;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}