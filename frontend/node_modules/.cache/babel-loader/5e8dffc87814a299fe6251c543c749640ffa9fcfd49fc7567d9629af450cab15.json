{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/').then(res => res.json()).then(data => {\n      setPresets(data.presets);\n      if (data.presets.default) {\n        setParams(data.presets.default.params);\n      }\n    }).catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom'); // 切换到自定义模式\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    setError(null);\n    if (file) {\n      // 验证文件类型\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      // 验证文件大小 (限制为10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n      setSelectedFile(file);\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = e => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-input\",\n          style: {\n            display: 'block',\n            marginBottom: '5px',\n            fontWeight: 'bold'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-input\",\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            marginBottom: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'red',\n          marginBottom: '10px',\n          padding: '5px',\n          border: '1px solid red',\n          borderRadius: '3px'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontWeight: 'bold'\n          },\n          children: \"\\u539F\\u59CB\\u56FE\\u50CF\\u9884\\u89C8:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: previewUrl,\n          alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n          style: {\n            maxWidth: '300px',\n            maxHeight: '300px',\n            border: '1px solid #ddd',\n            borderRadius: '5px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '15px',\n          padding: '12px',\n          border: '1px solid #ddd',\n          borderRadius: '5px',\n          backgroundColor: '#f9f9f9'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginTop: 0,\n            marginBottom: '12px',\n            fontSize: '16px'\n          },\n          children: \"\\u589E\\u5F3A\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '2fr 1fr',\n            gap: '10px',\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '3px',\n                fontWeight: 'bold',\n                fontSize: '14px'\n              },\n              children: \"\\u9884\\u8BBE\\u914D\\u7F6E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedPreset,\n              onChange: e => handlePresetChange(e.target.value),\n              style: {\n                width: '100%',\n                padding: '6px',\n                borderRadius: '3px',\n                border: '1px solid #ccc',\n                fontSize: '14px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"default\",\n                children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"portrait\",\n                children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"landscape\",\n                children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"vintage\",\n                children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"fast\",\n                children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"custom\",\n                children: \"\\u81EA\\u5B9A\\u4E49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'end'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                cursor: 'pointer',\n                fontSize: '14px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: showAdvanced,\n                onChange: e => setShowAdvanced(e.target.checked),\n                style: {\n                  marginRight: '6px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), \"\\u9AD8\\u7EA7\\u8BBE\\u7F6E\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '12px',\n            marginBottom: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '3px',\n                fontWeight: 'bold',\n                fontSize: '14px'\n              },\n              children: [\"\\u8D85\\u5206\\u500D\\u6570: \", params.scale, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"2\",\n              max: \"4\",\n              step: \"2\",\n              value: params.scale,\n              onChange: e => handleParamChange('scale', parseInt(e.target.value)),\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'end',\n              paddingBottom: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                cursor: 'pointer',\n                fontSize: '14px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: params.use_realesrgan,\n                onChange: e => handleParamChange('use_realesrgan', e.target.checked),\n                style: {\n                  marginRight: '6px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), \"RealESRGAN\\u6A21\\u578B\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), showAdvanced && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '12px',\n            paddingTop: '12px',\n            borderTop: '1px solid #ddd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginTop: 0,\n              marginBottom: '10px',\n              fontSize: '14px'\n            },\n            children: \"\\u9AD8\\u7EA7\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n              gap: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '3px',\n                  fontWeight: 'bold',\n                  fontSize: '13px'\n                },\n                children: [\"\\u9510\\u5316: \", (params.sharpening * 100).toFixed(0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"1\",\n                step: \"0.05\",\n                value: params.sharpening,\n                onChange: e => handleParamChange('sharpening', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '3px',\n                  fontWeight: 'bold',\n                  fontSize: '13px'\n                },\n                children: [\"\\u964D\\u566A: \", params.denoising]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"30\",\n                step: \"1\",\n                value: params.denoising,\n                onChange: e => handleParamChange('denoising', parseInt(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '3px',\n                  fontWeight: 'bold',\n                  fontSize: '13px'\n                },\n                children: [\"\\u9971\\u548C\\u5EA6: \", params.saturation.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"2\",\n                step: \"0.1\",\n                value: params.saturation,\n                onChange: e => handleParamChange('saturation', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '3px',\n                  fontWeight: 'bold',\n                  fontSize: '13px'\n                },\n                children: [\"\\u5BF9\\u6BD4\\u5EA6: \", params.contrast.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"2\",\n                step: \"0.05\",\n                value: params.contrast,\n                onChange: e => handleParamChange('contrast', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '3px',\n                  fontWeight: 'bold',\n                  fontSize: '13px'\n                },\n                children: [\"\\u4EAE\\u5EA6: \", params.brightness > 0 ? '+' : '', params.brightness]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"-100\",\n                max: \"100\",\n                step: \"5\",\n                value: params.brightness,\n                onChange: e => handleParamChange('brightness', parseInt(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '3px',\n                  fontWeight: 'bold',\n                  fontSize: '13px'\n                },\n                children: [\"\\u7F8E\\u989C: \", (params.beauty * 100).toFixed(0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"1\",\n                step: \"0.05\",\n                value: params.beauty,\n                onChange: e => handleParamChange('beauty', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: !selectedFile || isLoading,\n        style: {\n          padding: '12px 24px',\n          backgroundColor: isLoading ? '#ccc' : '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          fontSize: '16px',\n          fontWeight: 'bold',\n          width: '100%'\n        },\n        children: isLoading ? '正在增强...' : '开始增强图像'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"NSfU0BSMQA4M8PwsbFT6TBGAtlQ=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "showAdvanced", "setShowAdvanced", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fetch", "then", "res", "json", "data", "default", "catch", "err", "console", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "type", "startsWith", "size", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "style", "marginBottom", "children", "onSubmit", "htmlFor", "display", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "onChange", "accept", "required", "disabled", "color", "padding", "border", "borderRadius", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "backgroundColor", "marginTop", "fontSize", "gridTemplateColumns", "gap", "width", "alignItems", "cursor", "checked", "marginRight", "min", "max", "step", "parseInt", "paddingBottom", "paddingTop", "borderTop", "toFixed", "parseFloat", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/')\n      .then(res => res.json())\n      .then(data => {\n        setPresets(data.presets);\n        if (data.presets.default) {\n          setParams(data.presets.default.params);\n        }\n      })\n      .catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom'); // 切换到自定义模式\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    setError(null);\n\n    if (file) {\n      // 验证文件类型\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      // 验证文件大小 (限制为10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      setSelectedFile(file);\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  return (\n    <div style={{ marginBottom: '20px' }}>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '15px' }}>\n          <label htmlFor=\"file-input\" style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n            选择图像文件:\n          </label>\n          <input\n            id=\"file-input\"\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{ marginBottom: '10px' }}\n          />\n        </div>\n\n        {error && (\n          <div style={{ color: 'red', marginBottom: '10px', padding: '5px', border: '1px solid red', borderRadius: '3px' }}>\n            {error}\n          </div>\n        )}\n\n        {previewUrl && (\n          <div style={{ marginBottom: '15px' }}>\n            <p style={{ fontWeight: 'bold' }}>原始图像预览:</p>\n            <img\n              src={previewUrl}\n              alt=\"原始图像\"\n              style={{ maxWidth: '300px', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '5px' }}\n            />\n          </div>\n        )}\n\n        {/* 参数配置面板 */}\n        <div style={{ marginBottom: '15px', padding: '12px', border: '1px solid #ddd', borderRadius: '5px', backgroundColor: '#f9f9f9' }}>\n          <h3 style={{ marginTop: 0, marginBottom: '12px', fontSize: '16px' }}>增强设置</h3>\n\n          {/* 预设选择和高级设置 */}\n          <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '10px', marginBottom: '12px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '14px' }}>\n                预设配置:\n              </label>\n              <select\n                value={selectedPreset}\n                onChange={(e) => handlePresetChange(e.target.value)}\n                style={{ width: '100%', padding: '6px', borderRadius: '3px', border: '1px solid #ccc', fontSize: '14px' }}\n              >\n                <option value=\"default\">默认设置</option>\n                <option value=\"portrait\">人像优化</option>\n                <option value=\"landscape\">风景增强</option>\n                <option value=\"vintage\">复古风格</option>\n                <option value=\"fast\">快速处理</option>\n                <option value=\"custom\">自定义</option>\n              </select>\n            </div>\n\n            <div style={{ display: 'flex', alignItems: 'end' }}>\n              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', fontSize: '14px' }}>\n                <input\n                  type=\"checkbox\"\n                  checked={showAdvanced}\n                  onChange={(e) => setShowAdvanced(e.target.checked)}\n                  style={{ marginRight: '6px' }}\n                />\n                高级设置\n              </label>\n            </div>\n          </div>\n\n          {/* 基础设置 */}\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '10px' }}>\n            {/* 超分倍数 */}\n            <div>\n              <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '14px' }}>\n                超分倍数: {params.scale}x\n              </label>\n              <input\n                type=\"range\"\n                min=\"2\"\n                max=\"4\"\n                step=\"2\"\n                value={params.scale}\n                onChange={(e) => handleParamChange('scale', parseInt(e.target.value))}\n                style={{ width: '100%' }}\n              />\n            </div>\n\n            {/* 使用RealESRGAN */}\n            <div style={{ display: 'flex', alignItems: 'end', paddingBottom: '8px' }}>\n              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', fontSize: '14px' }}>\n                <input\n                  type=\"checkbox\"\n                  checked={params.use_realesrgan}\n                  onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}\n                  style={{ marginRight: '6px' }}\n                />\n                RealESRGAN模型\n              </label>\n            </div>\n          </div>\n\n          {/* 高级设置 */}\n          {showAdvanced && (\n            <div style={{ marginTop: '12px', paddingTop: '12px', borderTop: '1px solid #ddd' }}>\n              <h4 style={{ marginTop: 0, marginBottom: '10px', fontSize: '14px' }}>高级参数</h4>\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>\n\n                {/* 锐化强度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>\n                    锐化: {(params.sharpening * 100).toFixed(0)}%\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1\"\n                    step=\"0.05\"\n                    value={params.sharpening}\n                    onChange={(e) => handleParamChange('sharpening', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 降噪强度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>\n                    降噪: {params.denoising}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"30\"\n                    step=\"1\"\n                    value={params.denoising}\n                    onChange={(e) => handleParamChange('denoising', parseInt(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 饱和度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>\n                    饱和度: {params.saturation.toFixed(1)}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"2\"\n                    step=\"0.1\"\n                    value={params.saturation}\n                    onChange={(e) => handleParamChange('saturation', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 对比度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>\n                    对比度: {params.contrast.toFixed(1)}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"2\"\n                    step=\"0.05\"\n                    value={params.contrast}\n                    onChange={(e) => handleParamChange('contrast', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 亮度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>\n                    亮度: {params.brightness > 0 ? '+' : ''}{params.brightness}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"-100\"\n                    max=\"100\"\n                    step=\"5\"\n                    value={params.brightness}\n                    onChange={(e) => handleParamChange('brightness', parseInt(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 美颜强度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>\n                    美颜: {(params.beauty * 100).toFixed(0)}%\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1\"\n                    step=\"0.05\"\n                    value={params.beauty}\n                    onChange={(e) => handleParamChange('beauty', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={!selectedFile || isLoading}\n          style={{\n            padding: '12px 24px',\n            backgroundColor: isLoading ? '#ccc' : '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            fontSize: '16px',\n            fontWeight: 'bold',\n            width: '100%'\n          }}\n        >\n          {isLoading ? '正在增强...' : '开始增强图像'}\n        </button>\n      </form>\n    </div>\n  );\n};\n\nexport default UploadForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC;IACnCsB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA5B,SAAS,CAAC,MAAM;IACd6B,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZnB,UAAU,CAACmB,IAAI,CAACpB,OAAO,CAAC;MACxB,IAAIoB,IAAI,CAACpB,OAAO,CAACqB,OAAO,EAAE;QACxBd,SAAS,CAACa,IAAI,CAACpB,OAAO,CAACqB,OAAO,CAACf,MAAM,CAAC;MACxC;IACF,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAAC1B,KAAK,CAAC,WAAW,EAAEyB,GAAG,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,kBAAkB,GAAIC,SAAS,IAAK;IACxCvB,iBAAiB,CAACuB,SAAS,CAAC;IAC5B,IAAI1B,OAAO,CAAC0B,SAAS,CAAC,EAAE;MACtBnB,SAAS,CAACP,OAAO,CAAC0B,SAAS,CAAC,CAACpB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxCtB,SAAS,CAACuB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACH1B,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC;EAED,MAAM4B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BpC,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAIkC,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCtC,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;;MAEA;MACA,IAAIkC,IAAI,CAACK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCvC,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;MAEAJ,eAAe,CAACsC,IAAI,CAAC;;MAErB;MACA,MAAMM,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIT,CAAC,IAAKnC,aAAa,CAACmC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLtC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAM+C,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACnD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAM+C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtD,YAAY,CAAC;IACrCoD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC5C,MAAM,CAAC,CAAC;IACjDf,QAAQ,CAACuD,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEzD,OAAA;IAAK8D,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,eACnChE,OAAA;MAAMiE,QAAQ,EAAEV,YAAa;MAAAS,QAAA,gBAC3BhE,OAAA;QAAK8D,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnChE,OAAA;UAAOkE,OAAO,EAAC,YAAY;UAACJ,KAAK,EAAE;YAAEK,OAAO,EAAE,OAAO;YAAEJ,YAAY,EAAE,KAAK;YAAEK,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAElG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRxE,OAAA;UACEyE,EAAE,EAAC,YAAY;UACf1B,IAAI,EAAC,MAAM;UACX2B,QAAQ,EAAEhC,gBAAiB;UAC3BiC,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAE1E,SAAU;UACpB2D,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL/D,KAAK,iBACJT,OAAA;QAAK8D,KAAK,EAAE;UAAEgB,KAAK,EAAE,KAAK;UAAEf,YAAY,EAAE,MAAM;UAAEgB,OAAO,EAAE,KAAK;UAAEC,MAAM,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAjB,QAAA,EAC9GvD;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAjE,UAAU,iBACTP,OAAA;QAAK8D,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnChE,OAAA;UAAG8D,KAAK,EAAE;YAAEM,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7CxE,OAAA;UACEkF,GAAG,EAAE3E,UAAW;UAChB4E,GAAG,EAAC,0BAAM;UACVrB,KAAK,EAAE;YAAEsB,QAAQ,EAAE,OAAO;YAAEC,SAAS,EAAE,OAAO;YAAEL,MAAM,EAAE,gBAAgB;YAAEC,YAAY,EAAE;UAAM;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGDxE,OAAA;QAAK8D,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEgB,OAAO,EAAE,MAAM;UAAEC,MAAM,EAAE,gBAAgB;UAAEC,YAAY,EAAE,KAAK;UAAEK,eAAe,EAAE;QAAU,CAAE;QAAAtB,QAAA,gBAC/HhE,OAAA;UAAI8D,KAAK,EAAE;YAAEyB,SAAS,EAAE,CAAC;YAAExB,YAAY,EAAE,MAAM;YAAEyB,QAAQ,EAAE;UAAO,CAAE;UAAAxB,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG9ExE,OAAA;UAAK8D,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEsB,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAE3B,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjGhE,OAAA;YAAAgE,QAAA,gBACEhE,OAAA;cAAO8D,KAAK,EAAE;gBAAEK,OAAO,EAAE,OAAO;gBAAEJ,YAAY,EAAE,KAAK;gBAAEK,UAAU,EAAE,MAAM;gBAAEoB,QAAQ,EAAE;cAAO,CAAE;cAAAxB,QAAA,EAAC;YAE/F;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cACEwC,KAAK,EAAE3B,cAAe;cACtB6D,QAAQ,EAAG/B,CAAC,IAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;cACpDsB,KAAK,EAAE;gBAAE6B,KAAK,EAAE,MAAM;gBAAEZ,OAAO,EAAE,KAAK;gBAAEE,YAAY,EAAE,KAAK;gBAAED,MAAM,EAAE,gBAAgB;gBAAEQ,QAAQ,EAAE;cAAO,CAAE;cAAAxB,QAAA,gBAE1GhE,OAAA;gBAAQwC,KAAK,EAAC,SAAS;gBAAAwB,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCxE,OAAA;gBAAQwC,KAAK,EAAC,UAAU;gBAAAwB,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxE,OAAA;gBAAQwC,KAAK,EAAC,WAAW;gBAAAwB,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCxE,OAAA;gBAAQwC,KAAK,EAAC,SAAS;gBAAAwB,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCxE,OAAA;gBAAQwC,KAAK,EAAC,MAAM;gBAAAwB,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCxE,OAAA;gBAAQwC,KAAK,EAAC,QAAQ;gBAAAwB,QAAA,EAAC;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxE,OAAA;YAAK8D,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEyB,UAAU,EAAE;YAAM,CAAE;YAAA5B,QAAA,eACjDhE,OAAA;cAAO8D,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEyB,UAAU,EAAE,QAAQ;gBAAEC,MAAM,EAAE,SAAS;gBAAEL,QAAQ,EAAE;cAAO,CAAE;cAAAxB,QAAA,gBAC3FhE,OAAA;gBACE+C,IAAI,EAAC,UAAU;gBACf+C,OAAO,EAAE/E,YAAa;gBACtB2D,QAAQ,EAAG/B,CAAC,IAAK3B,eAAe,CAAC2B,CAAC,CAACE,MAAM,CAACiD,OAAO,CAAE;gBACnDhC,KAAK,EAAE;kBAAEiC,WAAW,EAAE;gBAAM;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,4BAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA;UAAK8D,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEsB,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAE3B,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBAEjGhE,OAAA;YAAAgE,QAAA,gBACEhE,OAAA;cAAO8D,KAAK,EAAE;gBAAEK,OAAO,EAAE,OAAO;gBAAEJ,YAAY,EAAE,KAAK;gBAAEK,UAAU,EAAE,MAAM;gBAAEoB,QAAQ,EAAE;cAAO,CAAE;cAAAxB,QAAA,GAAC,4BACvF,EAAC/C,MAAM,CAACE,KAAK,EAAC,GACtB;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cACE+C,IAAI,EAAC,OAAO;cACZiD,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,GAAG;cACPC,IAAI,EAAC,GAAG;cACR1D,KAAK,EAAEvB,MAAM,CAACE,KAAM;cACpBuD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,OAAO,EAAE6D,QAAQ,CAACxD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;cACtEsB,KAAK,EAAE;gBAAE6B,KAAK,EAAE;cAAO;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxE,OAAA;YAAK8D,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEyB,UAAU,EAAE,KAAK;cAAEQ,aAAa,EAAE;YAAM,CAAE;YAAApC,QAAA,eACvEhE,OAAA;cAAO8D,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEyB,UAAU,EAAE,QAAQ;gBAAEC,MAAM,EAAE,SAAS;gBAAEL,QAAQ,EAAE;cAAO,CAAE;cAAAxB,QAAA,gBAC3FhE,OAAA;gBACE+C,IAAI,EAAC,UAAU;gBACf+C,OAAO,EAAE7E,MAAM,CAACG,cAAe;gBAC/BsD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,gBAAgB,EAAEK,CAAC,CAACE,MAAM,CAACiD,OAAO,CAAE;gBACvEhC,KAAK,EAAE;kBAAEiC,WAAW,EAAE;gBAAM;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,0BAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLzD,YAAY,iBACXf,OAAA;UAAK8D,KAAK,EAAE;YAAEyB,SAAS,EAAE,MAAM;YAAEc,UAAU,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAiB,CAAE;UAAAtC,QAAA,gBACjFhE,OAAA;YAAI8D,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAExB,YAAY,EAAE,MAAM;cAAEyB,QAAQ,EAAE;YAAO,CAAE;YAAAxB,QAAA,EAAC;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ExE,OAAA;YAAK8D,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEsB,mBAAmB,EAAE,sCAAsC;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBAGxGhE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE,MAAM;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,GAAC,gBACzF,EAAC,CAAC/C,MAAM,CAACI,UAAU,GAAG,GAAG,EAAEkF,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5C;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZiD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACX1D,KAAK,EAAEvB,MAAM,CAACI,UAAW;gBACzBqD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEkE,UAAU,CAAC7D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC7EsB,KAAK,EAAE;kBAAE6B,KAAK,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE,MAAM;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,GAAC,gBACzF,EAAC/C,MAAM,CAACK,SAAS;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZiD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRC,IAAI,EAAC,GAAG;gBACR1D,KAAK,EAAEvB,MAAM,CAACK,SAAU;gBACxBoD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,WAAW,EAAE6D,QAAQ,CAACxD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC1EsB,KAAK,EAAE;kBAAE6B,KAAK,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE,MAAM;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,GAAC,sBACxF,EAAC/C,MAAM,CAACM,UAAU,CAACgF,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZiD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,KAAK;gBACV1D,KAAK,EAAEvB,MAAM,CAACM,UAAW;gBACzBmD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEkE,UAAU,CAAC7D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC7EsB,KAAK,EAAE;kBAAE6B,KAAK,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE,MAAM;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,GAAC,sBACxF,EAAC/C,MAAM,CAACO,QAAQ,CAAC+E,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZiD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACX1D,KAAK,EAAEvB,MAAM,CAACO,QAAS;gBACvBkD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,UAAU,EAAEkE,UAAU,CAAC7D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC3EsB,KAAK,EAAE;kBAAE6B,KAAK,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE,MAAM;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,GAAC,gBACzF,EAAC/C,MAAM,CAACQ,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAER,MAAM,CAACQ,UAAU;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZiD,GAAG,EAAC,MAAM;gBACVC,GAAG,EAAC,KAAK;gBACTC,IAAI,EAAC,GAAG;gBACR1D,KAAK,EAAEvB,MAAM,CAACQ,UAAW;gBACzBiD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAE6D,QAAQ,CAACxD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC3EsB,KAAK,EAAE;kBAAE6B,KAAK,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE,MAAM;kBAAEoB,QAAQ,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,GAAC,gBACzF,EAAC,CAAC/C,MAAM,CAACS,MAAM,GAAG,GAAG,EAAE6E,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZiD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACX1D,KAAK,EAAEvB,MAAM,CAACS,MAAO;gBACrBgD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,QAAQ,EAAEkE,UAAU,CAAC7D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBACzEsB,KAAK,EAAE;kBAAE6B,KAAK,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxE,OAAA;QACE+C,IAAI,EAAC,QAAQ;QACb8B,QAAQ,EAAE,CAACxE,YAAY,IAAIF,SAAU;QACrC2D,KAAK,EAAE;UACLiB,OAAO,EAAE,WAAW;UACpBO,eAAe,EAAEnF,SAAS,GAAG,MAAM,GAAG,SAAS;UAC/C2E,KAAK,EAAE,OAAO;UACdE,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBY,MAAM,EAAE1F,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7CqF,QAAQ,EAAE,MAAM;UAChBpB,UAAU,EAAE,MAAM;UAClBuB,KAAK,EAAE;QACT,CAAE;QAAA3B,QAAA,EAED7D,SAAS,GAAG,SAAS,GAAG;MAAQ;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpE,EAAA,CAjUIH,UAAU;AAAAwG,EAAA,GAAVxG,UAAU;AAmUhB,eAAeA,UAAU;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}