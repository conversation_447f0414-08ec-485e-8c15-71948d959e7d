{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 加载预设配置\n  useEffect(() => {\n    const loadPresets = async () => {\n      try {\n        const response = await fetch('http://localhost:8001/presets/');\n        if (response.ok) {\n          const data = await response.json();\n          setPresets(data.presets || {});\n        }\n      } catch (err) {\n        console.error('加载预设失败:', err);\n      }\n    };\n    loadPresets();\n  }, []);\n  const handleFileSelect = file => {\n    if (!file) return;\n\n    // 验证文件类型\n    if (!file.type.startsWith('image/')) {\n      setError('请选择图像文件');\n      return;\n    }\n\n    // 验证文件大小 (50MB)\n    if (file.size > 50 * 1024 * 1024) {\n      setError('文件大小不能超过50MB');\n      return;\n    }\n    setSelectedFile(file);\n    setError(null);\n\n    // 创建预览\n    const reader = new FileReader();\n    reader.onload = e => setPreviewUrl(e.target.result);\n    reader.readAsDataURL(file);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择图像文件');\n      return;\n    }\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n\n    // 添加参数\n    Object.entries(params).forEach(([key, value]) => {\n      formData.append(key, value);\n    });\n    try {\n      await onUpload(formData);\n    } catch (err) {\n      setError(err.message);\n    }\n  };\n  const applyPreset = presetName => {\n    if (presets[presetName]) {\n      setParams({\n        ...params,\n        ...presets[presetName]\n      });\n      setSelectedPreset(presetName);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '15px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onDrop: handleDrop,\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      style: {\n        border: `2px dashed ${isDragOver ? '#4a90e2' : '#555'}`,\n        borderRadius: '8px',\n        padding: '20px',\n        textAlign: 'center',\n        backgroundColor: isDragOver ? 'rgba(74, 144, 226, 0.1)' : '#3c3c3c',\n        cursor: 'pointer',\n        marginBottom: '15px',\n        transition: 'all 0.3s ease'\n      },\n      onClick: () => document.getElementById('fileInput').click(),\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        id: \"fileInput\",\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: e => handleFileSelect(e.target.files[0]),\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), previewUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: previewUrl,\n          alt: \"\\u9884\\u89C8\",\n          style: {\n            maxWidth: '100%',\n            maxHeight: '150px',\n            borderRadius: '4px',\n            marginBottom: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px'\n          },\n          children: [selectedFile.name, \" (\", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '48px',\n            marginBottom: '10px'\n          },\n          children: \"\\uD83D\\uDCC1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#e0e0e0',\n            marginBottom: '5px'\n          },\n          children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u56FE\\u50CF\\u5230\\u6B64\\u5904\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#888',\n            fontSize: '12px'\n          },\n          children: \"\\u652F\\u6301 JPG, PNG, WebP \\u683C\\u5F0F\\uFF0C\\u6700\\u5927 50MB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#d32f2f',\n        color: 'white',\n        padding: '8px 12px',\n        borderRadius: '4px',\n        fontSize: '12px',\n        marginBottom: '15px'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          color: '#e0e0e0',\n          fontSize: '13px',\n          marginBottom: '8px',\n          display: 'block'\n        },\n        children: \"\\u9884\\u8BBE\\u914D\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedPreset,\n        onChange: e => applyPreset(e.target.value),\n        style: {\n          width: '100%',\n          padding: '8px',\n          backgroundColor: '#3c3c3c',\n          color: '#e0e0e0',\n          border: '1px solid #555',\n          borderRadius: '4px',\n          fontSize: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"default\",\n          children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), Object.keys(presets).map(preset => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: preset,\n          children: preset\n        }, preset, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          color: '#e0e0e0',\n          fontSize: '13px',\n          marginBottom: '8px',\n          display: 'block'\n        },\n        children: [\"\\u653E\\u5927\\u500D\\u6570: \", params.scale, \"x\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"range\",\n        min: \"2\",\n        max: \"8\",\n        step: \"1\",\n        value: params.scale,\n        onChange: e => setParams({\n          ...params,\n          scale: parseInt(e.target.value)\n        }),\n        style: {\n          width: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          color: '#e0e0e0',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: params.use_realesrgan,\n          onChange: e => setParams({\n            ...params,\n            use_realesrgan: e.target.checked\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), \"\\u4F7F\\u7528 RealESRGAN \\u8D85\\u5206\\u8FA8\\u7387\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: () => setShowAdvanced(!showAdvanced),\n        style: {\n          width: '100%',\n          padding: '8px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '5px'\n        },\n        children: [showAdvanced ? '▼' : '▶', \" \\u9AD8\\u7EA7\\u8BBE\\u7F6E\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), showAdvanced && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px',\n        padding: '10px',\n        backgroundColor: '#2a2a2a',\n        borderRadius: '4px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px',\n            marginBottom: '5px',\n            display: 'block'\n          },\n          children: [\"\\u9510\\u5316: \", params.sharpening.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"2\",\n          step: \"0.1\",\n          value: params.sharpening,\n          onChange: e => setParams({\n            ...params,\n            sharpening: parseFloat(e.target.value)\n          }),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px',\n            marginBottom: '5px',\n            display: 'block'\n          },\n          children: [\"\\u964D\\u566A: \", params.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"10\",\n          step: \"1\",\n          value: params.denoising,\n          onChange: e => setParams({\n            ...params,\n            denoising: parseInt(e.target.value)\n          }),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px',\n            marginBottom: '5px',\n            display: 'block'\n          },\n          children: [\"\\u9971\\u548C\\u5EA6: \", params.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"2\",\n          step: \"0.1\",\n          value: params.saturation,\n          onChange: e => setParams({\n            ...params,\n            saturation: parseFloat(e.target.value)\n          }),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px',\n            marginBottom: '5px',\n            display: 'block'\n          },\n          children: [\"\\u5BF9\\u6BD4\\u5EA6: \", params.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"2\",\n          step: \"0.1\",\n          value: params.contrast,\n          onChange: e => setParams({\n            ...params,\n            contrast: parseFloat(e.target.value)\n          }),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px',\n            marginBottom: '5px',\n            display: 'block'\n          },\n          children: [\"\\u4EAE\\u5EA6: \", params.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"-50\",\n          max: \"50\",\n          step: \"1\",\n          value: params.brightness,\n          onChange: e => setParams({\n            ...params,\n            brightness: parseInt(e.target.value)\n          }),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#e0e0e0',\n            fontSize: '12px',\n            marginBottom: '5px',\n            display: 'block'\n          },\n          children: [\"\\u7F8E\\u989C: \", params.beauty.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"range\",\n          min: \"0\",\n          max: \"1\",\n          step: \"0.1\",\n          value: params.beauty,\n          onChange: e => setParams({\n            ...params,\n            beauty: parseFloat(e.target.value)\n          }),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleSubmit,\n      disabled: !selectedFile || isLoading,\n      style: {\n        width: '100%',\n        padding: '12px',\n        backgroundColor: selectedFile && !isLoading ? '#28a745' : '#6c757d',\n        color: 'white',\n        border: 'none',\n        borderRadius: '4px',\n        cursor: selectedFile && !isLoading ? 'pointer' : 'not-allowed',\n        fontSize: '14px',\n        fontWeight: '500'\n      },\n      children: isLoading ? '处理中...' : '开始增强'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"3+HPKV3b7ObPKTUPYbaABiz2yqA=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "isDragOver", "setIsDragOver", "showAdvanced", "setShowAdvanced", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "loadPresets", "response", "fetch", "ok", "data", "json", "err", "console", "handleFileSelect", "file", "type", "startsWith", "size", "reader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "handleDrop", "preventDefault", "files", "dataTransfer", "length", "handleDragOver", "handleDragLeave", "handleSubmit", "formData", "FormData", "append", "Object", "entries", "for<PERSON>ach", "key", "value", "message", "applyPreset", "presetName", "style", "padding", "children", "onDrop", "onDragOver", "onDragLeave", "border", "borderRadius", "textAlign", "backgroundColor", "cursor", "marginBottom", "transition", "onClick", "document", "getElementById", "click", "id", "accept", "onChange", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "color", "fontSize", "name", "toFixed", "width", "keys", "map", "preset", "min", "max", "step", "parseInt", "alignItems", "gap", "checked", "justifyContent", "parseFloat", "disabled", "fontWeight", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 加载预设配置\n  useEffect(() => {\n    const loadPresets = async () => {\n      try {\n        const response = await fetch('http://localhost:8001/presets/');\n        if (response.ok) {\n          const data = await response.json();\n          setPresets(data.presets || {});\n        }\n      } catch (err) {\n        console.error('加载预设失败:', err);\n      }\n    };\n    loadPresets();\n  }, []);\n\n  const handleFileSelect = (file) => {\n    if (!file) return;\n\n    // 验证文件类型\n    if (!file.type.startsWith('image/')) {\n      setError('请选择图像文件');\n      return;\n    }\n\n    // 验证文件大小 (50MB)\n    if (file.size > 50 * 1024 * 1024) {\n      setError('文件大小不能超过50MB');\n      return;\n    }\n\n    setSelectedFile(file);\n    setError(null);\n\n    // 创建预览\n    const reader = new FileReader();\n    reader.onload = (e) => setPreviewUrl(e.target.result);\n    reader.readAsDataURL(file);\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择图像文件');\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n    \n    // 添加参数\n    Object.entries(params).forEach(([key, value]) => {\n      formData.append(key, value);\n    });\n\n    try {\n      await onUpload(formData);\n    } catch (err) {\n      setError(err.message);\n    }\n  };\n\n  const applyPreset = (presetName) => {\n    if (presets[presetName]) {\n      setParams({ ...params, ...presets[presetName] });\n      setSelectedPreset(presetName);\n    }\n  };\n\n  return (\n    <div style={{ padding: '15px' }}>\n      {/* 文件上传区域 */}\n      <div\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        style={{\n          border: `2px dashed ${isDragOver ? '#4a90e2' : '#555'}`,\n          borderRadius: '8px',\n          padding: '20px',\n          textAlign: 'center',\n          backgroundColor: isDragOver ? 'rgba(74, 144, 226, 0.1)' : '#3c3c3c',\n          cursor: 'pointer',\n          marginBottom: '15px',\n          transition: 'all 0.3s ease'\n        }}\n        onClick={() => document.getElementById('fileInput').click()}\n      >\n        <input\n          id=\"fileInput\"\n          type=\"file\"\n          accept=\"image/*\"\n          onChange={(e) => handleFileSelect(e.target.files[0])}\n          style={{ display: 'none' }}\n        />\n        \n        {previewUrl ? (\n          <div>\n            <img\n              src={previewUrl}\n              alt=\"预览\"\n              style={{\n                maxWidth: '100%',\n                maxHeight: '150px',\n                borderRadius: '4px',\n                marginBottom: '10px'\n              }}\n            />\n            <div style={{ color: '#e0e0e0', fontSize: '12px' }}>\n              {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)\n            </div>\n          </div>\n        ) : (\n          <div>\n            <div style={{ fontSize: '48px', marginBottom: '10px' }}>📁</div>\n            <div style={{ color: '#e0e0e0', marginBottom: '5px' }}>\n              点击或拖拽图像到此处\n            </div>\n            <div style={{ color: '#888', fontSize: '12px' }}>\n              支持 JPG, PNG, WebP 格式，最大 50MB\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <div style={{\n          backgroundColor: '#d32f2f',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          marginBottom: '15px'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {/* 预设选择 */}\n      <div style={{ marginBottom: '15px' }}>\n        <label style={{ color: '#e0e0e0', fontSize: '13px', marginBottom: '8px', display: 'block' }}>\n          预设配置\n        </label>\n        <select\n          value={selectedPreset}\n          onChange={(e) => applyPreset(e.target.value)}\n          style={{\n            width: '100%',\n            padding: '8px',\n            backgroundColor: '#3c3c3c',\n            color: '#e0e0e0',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            fontSize: '12px'\n          }}\n        >\n          <option value=\"default\">默认设置</option>\n          {Object.keys(presets).map(preset => (\n            <option key={preset} value={preset}>{preset}</option>\n          ))}\n        </select>\n      </div>\n\n      {/* 基础参数 */}\n      <div style={{ marginBottom: '15px' }}>\n        <label style={{ color: '#e0e0e0', fontSize: '13px', marginBottom: '8px', display: 'block' }}>\n          放大倍数: {params.scale}x\n        </label>\n        <input\n          type=\"range\"\n          min=\"2\"\n          max=\"8\"\n          step=\"1\"\n          value={params.scale}\n          onChange={(e) => setParams({ ...params, scale: parseInt(e.target.value) })}\n          style={{ width: '100%' }}\n        />\n      </div>\n\n      <div style={{ marginBottom: '15px' }}>\n        <label style={{ color: '#e0e0e0', fontSize: '13px', display: 'flex', alignItems: 'center', gap: '8px' }}>\n          <input\n            type=\"checkbox\"\n            checked={params.use_realesrgan}\n            onChange={(e) => setParams({ ...params, use_realesrgan: e.target.checked })}\n          />\n          使用 RealESRGAN 超分辨率\n        </label>\n      </div>\n\n      {/* 高级设置 */}\n      <div style={{ marginBottom: '15px' }}>\n        <button\n          type=\"button\"\n          onClick={() => setShowAdvanced(!showAdvanced)}\n          style={{\n            width: '100%',\n            padding: '8px',\n            backgroundColor: '#4a90e2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            gap: '5px'\n          }}\n        >\n          {showAdvanced ? '▼' : '▶'} 高级设置\n        </button>\n      </div>\n\n      {showAdvanced && (\n        <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>\n          {/* 锐化 */}\n          <div style={{ marginBottom: '12px' }}>\n            <label style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '5px', display: 'block' }}>\n              锐化: {params.sharpening.toFixed(1)}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.1\"\n              value={params.sharpening}\n              onChange={(e) => setParams({ ...params, sharpening: parseFloat(e.target.value) })}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 降噪 */}\n          <div style={{ marginBottom: '12px' }}>\n            <label style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '5px', display: 'block' }}>\n              降噪: {params.denoising}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"10\"\n              step=\"1\"\n              value={params.denoising}\n              onChange={(e) => setParams({ ...params, denoising: parseInt(e.target.value) })}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 饱和度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <label style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '5px', display: 'block' }}>\n              饱和度: {params.saturation.toFixed(1)}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.1\"\n              value={params.saturation}\n              onChange={(e) => setParams({ ...params, saturation: parseFloat(e.target.value) })}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 对比度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <label style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '5px', display: 'block' }}>\n              对比度: {params.contrast.toFixed(1)}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.1\"\n              value={params.contrast}\n              onChange={(e) => setParams({ ...params, contrast: parseFloat(e.target.value) })}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 亮度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <label style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '5px', display: 'block' }}>\n              亮度: {params.brightness}\n            </label>\n            <input\n              type=\"range\"\n              min=\"-50\"\n              max=\"50\"\n              step=\"1\"\n              value={params.brightness}\n              onChange={(e) => setParams({ ...params, brightness: parseInt(e.target.value) })}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 美颜 */}\n          <div style={{ marginBottom: '12px' }}>\n            <label style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '5px', display: 'block' }}>\n              美颜: {params.beauty.toFixed(1)}\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={params.beauty}\n              onChange={(e) => setParams({ ...params, beauty: parseFloat(e.target.value) })}\n              style={{ width: '100%' }}\n            />\n          </div>\n        </div>\n      )}\n\n      {/* 提交按钮 */}\n      <button\n        onClick={handleSubmit}\n        disabled={!selectedFile || isLoading}\n        style={{\n          width: '100%',\n          padding: '12px',\n          backgroundColor: selectedFile && !isLoading ? '#28a745' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: selectedFile && !isLoading ? 'pointer' : 'not-allowed',\n          fontSize: '14px',\n          fontWeight: '500'\n        }}\n      >\n        {isLoading ? '处理中...' : '开始增强'}\n      </button>\n    </div>\n  );\n};\n\nexport default UploadForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC;IACnCwB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA9B,SAAS,CAAC,MAAM;IACd,MAAM+B,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,CAAC;QAC9D,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClCtB,UAAU,CAACqB,IAAI,CAACtB,OAAO,IAAI,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,CAAC,OAAOwB,GAAG,EAAE;QACZC,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAE0B,GAAG,CAAC;MAC/B;IACF,CAAC;IACDN,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC9B,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;;IAEA;IACA,IAAI4B,IAAI,CAACG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MAChC/B,QAAQ,CAAC,cAAc,CAAC;MACxB;IACF;IAEAJ,eAAe,CAACgC,IAAI,CAAC;IACrB5B,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMgC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACC,MAAM,CAACC,MAAM,CAAC;IACrDL,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMW,UAAU,GAAIJ,CAAC,IAAK;IACxBA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBlC,aAAa,CAAC,KAAK,CAAC;IACpB,MAAMmC,KAAK,GAAGN,CAAC,CAACO,YAAY,CAACD,KAAK;IAClC,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACpBhB,gBAAgB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMG,cAAc,GAAIT,CAAC,IAAK;IAC5BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBlC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMuC,eAAe,GAAIV,CAAC,IAAK;IAC7BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBlC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMwC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC7C,YAAY,EAAE;MACjBK,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;IAEA,MAAM+C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtD,YAAY,CAAC;;IAErC;IACAuD,MAAM,CAACC,OAAO,CAAC1C,MAAM,CAAC,CAAC2C,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAC/CP,QAAQ,CAACE,MAAM,CAACI,GAAG,EAAEC,KAAK,CAAC;IAC7B,CAAC,CAAC;IAEF,IAAI;MACF,MAAM9D,QAAQ,CAACuD,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZzB,QAAQ,CAACyB,GAAG,CAAC8B,OAAO,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,WAAW,GAAIC,UAAU,IAAK;IAClC,IAAIxD,OAAO,CAACwD,UAAU,CAAC,EAAE;MACvB/C,SAAS,CAAC;QAAE,GAAGD,MAAM;QAAE,GAAGR,OAAO,CAACwD,UAAU;MAAE,CAAC,CAAC;MAChDrD,iBAAiB,CAACqD,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,oBACEnE,OAAA;IAAKoE,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE9BtE,OAAA;MACEuE,MAAM,EAAEtB,UAAW;MACnBuB,UAAU,EAAElB,cAAe;MAC3BmB,WAAW,EAAElB,eAAgB;MAC7Ba,KAAK,EAAE;QACLM,MAAM,EAAE,cAAc3D,UAAU,GAAG,SAAS,GAAG,MAAM,EAAE;QACvD4D,YAAY,EAAE,KAAK;QACnBN,OAAO,EAAE,MAAM;QACfO,SAAS,EAAE,QAAQ;QACnBC,eAAe,EAAE9D,UAAU,GAAG,yBAAyB,GAAG,SAAS;QACnE+D,MAAM,EAAE,SAAS;QACjBC,YAAY,EAAE,MAAM;QACpBC,UAAU,EAAE;MACd,CAAE;MACFC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC,CAACC,KAAK,CAAC,CAAE;MAAAd,QAAA,gBAE5DtE,OAAA;QACEqF,EAAE,EAAC,WAAW;QACd9C,IAAI,EAAC,MAAM;QACX+C,MAAM,EAAC,SAAS;QAChBC,QAAQ,EAAG1C,CAAC,IAAKR,gBAAgB,CAACQ,CAAC,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAE;QACrDiB,KAAK,EAAE;UAAEoB,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAEDrF,UAAU,gBACTP,OAAA;QAAAsE,QAAA,gBACEtE,OAAA;UACE6F,GAAG,EAAEtF,UAAW;UAChBuF,GAAG,EAAC,cAAI;UACR1B,KAAK,EAAE;YACL2B,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,OAAO;YAClBrB,YAAY,EAAE,KAAK;YACnBI,YAAY,EAAE;UAChB;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF5F,OAAA;UAAKoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA5B,QAAA,GAChDjE,YAAY,CAAC8F,IAAI,EAAC,IAAE,EAAC,CAAC9F,YAAY,CAACoC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE2D,OAAO,CAAC,CAAC,CAAC,EAAC,MACrE;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN5F,OAAA;QAAAsE,QAAA,gBACEtE,OAAA;UAAKoE,KAAK,EAAE;YAAE8B,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChE5F,OAAA;UAAKoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAElB,YAAY,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAEvD;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN5F,OAAA;UAAKoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA5B,QAAA,EAAC;QAEjD;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELnF,KAAK,iBACJT,OAAA;MAAKoE,KAAK,EAAE;QACVS,eAAe,EAAE,SAAS;QAC1BoB,KAAK,EAAE,OAAO;QACd5B,OAAO,EAAE,UAAU;QACnBM,YAAY,EAAE,KAAK;QACnBuB,QAAQ,EAAE,MAAM;QAChBnB,YAAY,EAAE;MAChB,CAAE;MAAAT,QAAA,EACC7D;IAAK;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5F,OAAA;MAAKoE,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAT,QAAA,gBACnCtE,OAAA;QAAOoE,KAAK,EAAE;UAAE6B,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,MAAM;UAAEnB,YAAY,EAAE,KAAK;UAAES,OAAO,EAAE;QAAQ,CAAE;QAAAlB,QAAA,EAAC;MAE7F;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5F,OAAA;QACEgE,KAAK,EAAEnD,cAAe;QACtB0E,QAAQ,EAAG1C,CAAC,IAAKqB,WAAW,CAACrB,CAAC,CAACC,MAAM,CAACkB,KAAK,CAAE;QAC7CI,KAAK,EAAE;UACLiC,KAAK,EAAE,MAAM;UACbhC,OAAO,EAAE,KAAK;UACdQ,eAAe,EAAE,SAAS;UAC1BoB,KAAK,EAAE,SAAS;UAChBvB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBuB,QAAQ,EAAE;QACZ,CAAE;QAAA5B,QAAA,gBAEFtE,OAAA;UAAQgE,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAI;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACpChC,MAAM,CAAC0C,IAAI,CAAC3F,OAAO,CAAC,CAAC4F,GAAG,CAACC,MAAM,iBAC9BxG,OAAA;UAAqBgE,KAAK,EAAEwC,MAAO;UAAAlC,QAAA,EAAEkC;QAAM,GAA9BA,MAAM;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiC,CACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN5F,OAAA;MAAKoE,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAT,QAAA,gBACnCtE,OAAA;QAAOoE,KAAK,EAAE;UAAE6B,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,MAAM;UAAEnB,YAAY,EAAE,KAAK;UAAES,OAAO,EAAE;QAAQ,CAAE;QAAAlB,QAAA,GAAC,4BACrF,EAACnD,MAAM,CAACE,KAAK,EAAC,GACtB;MAAA;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5F,OAAA;QACEuC,IAAI,EAAC,OAAO;QACZkE,GAAG,EAAC,GAAG;QACPC,GAAG,EAAC,GAAG;QACPC,IAAI,EAAC,GAAG;QACR3C,KAAK,EAAE7C,MAAM,CAACE,KAAM;QACpBkE,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;UAAE,GAAGD,MAAM;UAAEE,KAAK,EAAEuF,QAAQ,CAAC/D,CAAC,CAACC,MAAM,CAACkB,KAAK;QAAE,CAAC,CAAE;QAC3EI,KAAK,EAAE;UAAEiC,KAAK,EAAE;QAAO;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN5F,OAAA;MAAKoE,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAT,QAAA,eACnCtE,OAAA;QAAOoE,KAAK,EAAE;UAAE6B,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,MAAM;UAAEV,OAAO,EAAE,MAAM;UAAEqB,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAxC,QAAA,gBACtGtE,OAAA;UACEuC,IAAI,EAAC,UAAU;UACfwE,OAAO,EAAE5F,MAAM,CAACG,cAAe;UAC/BiE,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEG,cAAc,EAAEuB,CAAC,CAACC,MAAM,CAACiE;UAAQ,CAAC;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,oDAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN5F,OAAA;MAAKoE,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAT,QAAA,eACnCtE,OAAA;QACEuC,IAAI,EAAC,QAAQ;QACb0C,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,CAACD,YAAY,CAAE;QAC9CmD,KAAK,EAAE;UACLiC,KAAK,EAAE,MAAM;UACbhC,OAAO,EAAE,KAAK;UACdQ,eAAe,EAAE,SAAS;UAC1BoB,KAAK,EAAE,OAAO;UACdvB,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBG,MAAM,EAAE,SAAS;UACjBoB,QAAQ,EAAE,MAAM;UAChBV,OAAO,EAAE,MAAM;UACfqB,UAAU,EAAE,QAAQ;UACpBG,cAAc,EAAE,QAAQ;UACxBF,GAAG,EAAE;QACP,CAAE;QAAAxC,QAAA,GAEDrD,YAAY,GAAG,GAAG,GAAG,GAAG,EAAC,2BAC5B;MAAA;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL3E,YAAY,iBACXjB,OAAA;MAAKoE,KAAK,EAAE;QAAEW,YAAY,EAAE,MAAM;QAAEV,OAAO,EAAE,MAAM;QAAEQ,eAAe,EAAE,SAAS;QAAEF,YAAY,EAAE;MAAM,CAAE;MAAAL,QAAA,gBAErGtE,OAAA;QAAKoE,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnCtE,OAAA;UAAOoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAES,OAAO,EAAE;UAAQ,CAAE;UAAAlB,QAAA,GAAC,gBACvF,EAACnD,MAAM,CAACI,UAAU,CAAC6E,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACR5F,OAAA;UACEuC,IAAI,EAAC,OAAO;UACZkE,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,GAAG;UACPC,IAAI,EAAC,KAAK;UACV3C,KAAK,EAAE7C,MAAM,CAACI,UAAW;UACzBgE,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEI,UAAU,EAAE0F,UAAU,CAACpE,CAAC,CAACC,MAAM,CAACkB,KAAK;UAAE,CAAC,CAAE;UAClFI,KAAK,EAAE;YAAEiC,KAAK,EAAE;UAAO;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5F,OAAA;QAAKoE,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnCtE,OAAA;UAAOoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAES,OAAO,EAAE;UAAQ,CAAE;UAAAlB,QAAA,GAAC,gBACvF,EAACnD,MAAM,CAACK,SAAS;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACR5F,OAAA;UACEuC,IAAI,EAAC,OAAO;UACZkE,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,IAAI;UACRC,IAAI,EAAC,GAAG;UACR3C,KAAK,EAAE7C,MAAM,CAACK,SAAU;UACxB+D,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEK,SAAS,EAAEoF,QAAQ,CAAC/D,CAAC,CAACC,MAAM,CAACkB,KAAK;UAAE,CAAC,CAAE;UAC/EI,KAAK,EAAE;YAAEiC,KAAK,EAAE;UAAO;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5F,OAAA;QAAKoE,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnCtE,OAAA;UAAOoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAES,OAAO,EAAE;UAAQ,CAAE;UAAAlB,QAAA,GAAC,sBACtF,EAACnD,MAAM,CAACM,UAAU,CAAC2E,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACR5F,OAAA;UACEuC,IAAI,EAAC,OAAO;UACZkE,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,GAAG;UACPC,IAAI,EAAC,KAAK;UACV3C,KAAK,EAAE7C,MAAM,CAACM,UAAW;UACzB8D,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEM,UAAU,EAAEwF,UAAU,CAACpE,CAAC,CAACC,MAAM,CAACkB,KAAK;UAAE,CAAC,CAAE;UAClFI,KAAK,EAAE;YAAEiC,KAAK,EAAE;UAAO;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5F,OAAA;QAAKoE,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnCtE,OAAA;UAAOoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAES,OAAO,EAAE;UAAQ,CAAE;UAAAlB,QAAA,GAAC,sBACtF,EAACnD,MAAM,CAACO,QAAQ,CAAC0E,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACR5F,OAAA;UACEuC,IAAI,EAAC,OAAO;UACZkE,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,GAAG;UACPC,IAAI,EAAC,KAAK;UACV3C,KAAK,EAAE7C,MAAM,CAACO,QAAS;UACvB6D,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEO,QAAQ,EAAEuF,UAAU,CAACpE,CAAC,CAACC,MAAM,CAACkB,KAAK;UAAE,CAAC,CAAE;UAChFI,KAAK,EAAE;YAAEiC,KAAK,EAAE;UAAO;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5F,OAAA;QAAKoE,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnCtE,OAAA;UAAOoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAES,OAAO,EAAE;UAAQ,CAAE;UAAAlB,QAAA,GAAC,gBACvF,EAACnD,MAAM,CAACQ,UAAU;QAAA;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACR5F,OAAA;UACEuC,IAAI,EAAC,OAAO;UACZkE,GAAG,EAAC,KAAK;UACTC,GAAG,EAAC,IAAI;UACRC,IAAI,EAAC,GAAG;UACR3C,KAAK,EAAE7C,MAAM,CAACQ,UAAW;UACzB4D,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEQ,UAAU,EAAEiF,QAAQ,CAAC/D,CAAC,CAACC,MAAM,CAACkB,KAAK;UAAE,CAAC,CAAE;UAChFI,KAAK,EAAE;YAAEiC,KAAK,EAAE;UAAO;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5F,OAAA;QAAKoE,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnCtE,OAAA;UAAOoE,KAAK,EAAE;YAAE6B,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAES,OAAO,EAAE;UAAQ,CAAE;UAAAlB,QAAA,GAAC,gBACvF,EAACnD,MAAM,CAACS,MAAM,CAACwE,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACR5F,OAAA;UACEuC,IAAI,EAAC,OAAO;UACZkE,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,GAAG;UACPC,IAAI,EAAC,KAAK;UACV3C,KAAK,EAAE7C,MAAM,CAACS,MAAO;UACrB2D,QAAQ,EAAG1C,CAAC,IAAKzB,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAES,MAAM,EAAEqF,UAAU,CAACpE,CAAC,CAACC,MAAM,CAACkB,KAAK;UAAE,CAAC,CAAE;UAC9EI,KAAK,EAAE;YAAEiC,KAAK,EAAE;UAAO;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5F,OAAA;MACEiF,OAAO,EAAEzB,YAAa;MACtB0D,QAAQ,EAAE,CAAC7G,YAAY,IAAIF,SAAU;MACrCiE,KAAK,EAAE;QACLiC,KAAK,EAAE,MAAM;QACbhC,OAAO,EAAE,MAAM;QACfQ,eAAe,EAAExE,YAAY,IAAI,CAACF,SAAS,GAAG,SAAS,GAAG,SAAS;QACnE8F,KAAK,EAAE,OAAO;QACdvB,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBG,MAAM,EAAEzE,YAAY,IAAI,CAACF,SAAS,GAAG,SAAS,GAAG,aAAa;QAC9D+F,QAAQ,EAAE,MAAM;QAChBiB,UAAU,EAAE;MACd,CAAE;MAAA7C,QAAA,EAEDnE,SAAS,GAAG,QAAQ,GAAG;IAAM;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACxF,EAAA,CApXIH,UAAU;AAAAmH,EAAA,GAAVnH,UAAU;AAsXhB,eAAeA,UAAU;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}