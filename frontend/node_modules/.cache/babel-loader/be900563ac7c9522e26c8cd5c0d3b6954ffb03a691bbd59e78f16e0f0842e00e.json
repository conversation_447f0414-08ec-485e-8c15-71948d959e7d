{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/').then(res => res.json()).then(data => {\n      setPresets(data.presets);\n      if (data.presets.default) {\n        setParams(data.presets.default.params);\n      }\n    }).catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom'); // 切换到自定义模式\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    setError(null);\n    if (file) {\n      // 验证文件类型\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      // 验证文件大小 (限制为10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n      setSelectedFile(file);\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = e => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-input\",\n          style: {\n            display: 'block',\n            marginBottom: '5px',\n            fontWeight: 'bold'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-input\",\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            marginBottom: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'red',\n          marginBottom: '10px',\n          padding: '5px',\n          border: '1px solid red',\n          borderRadius: '3px'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontWeight: 'bold'\n          },\n          children: \"\\u539F\\u59CB\\u56FE\\u50CF\\u9884\\u89C8:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: previewUrl,\n          alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n          style: {\n            maxWidth: '300px',\n            maxHeight: '300px',\n            border: '1px solid #ddd',\n            borderRadius: '5px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '5px',\n          backgroundColor: '#f9f9f9'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginTop: 0,\n            marginBottom: '15px'\n          },\n          children: \"\\u589E\\u5F3A\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '5px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u9884\\u8BBE\\u914D\\u7F6E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedPreset,\n            onChange: e => handlePresetChange(e.target.value),\n            style: {\n              width: '100%',\n              padding: '8px',\n              borderRadius: '3px',\n              border: '1px solid #ccc'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"default\",\n              children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"portrait\",\n              children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"landscape\",\n              children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"vintage\",\n              children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"fast\",\n              children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"custom\",\n              children: \"\\u81EA\\u5B9A\\u4E49\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: showAdvanced,\n              onChange: e => setShowAdvanced(e.target.checked),\n              style: {\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), \"\\u663E\\u793A\\u9AD8\\u7EA7\\u8BBE\\u7F6E\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '5px',\n                fontWeight: 'bold'\n              },\n              children: [\"\\u8D85\\u5206\\u500D\\u6570: \", params.scale, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"2\",\n              max: \"4\",\n              step: \"2\",\n              value: params.scale,\n              onChange: e => handleParamChange('scale', parseInt(e.target.value)),\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: params.use_realesrgan,\n                onChange: e => handleParamChange('use_realesrgan', e.target.checked),\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), \"\\u4F7F\\u7528RealESRGAN\\u6A21\\u578B\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), showAdvanced && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '20px',\n            paddingTop: '15px',\n            borderTop: '1px solid #ddd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginTop: 0,\n              marginBottom: '15px'\n            },\n            children: \"\\u9AD8\\u7EA7\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '5px',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u9510\\u5316\\u5F3A\\u5EA6: \", (params.sharpening * 100).toFixed(0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"1\",\n                step: \"0.05\",\n                value: params.sharpening,\n                onChange: e => handleParamChange('sharpening', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '5px',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u964D\\u566A\\u5F3A\\u5EA6: \", params.denoising]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"30\",\n                step: \"1\",\n                value: params.denoising,\n                onChange: e => handleParamChange('denoising', parseInt(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '5px',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u9971\\u548C\\u5EA6: \", params.saturation.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"2\",\n                step: \"0.1\",\n                value: params.saturation,\n                onChange: e => handleParamChange('saturation', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '5px',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u5BF9\\u6BD4\\u5EA6: \", params.contrast.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"2\",\n                step: \"0.05\",\n                value: params.contrast,\n                onChange: e => handleParamChange('contrast', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '5px',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u4EAE\\u5EA6: \", params.brightness > 0 ? '+' : '', params.brightness]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"-100\",\n                max: \"100\",\n                step: \"5\",\n                value: params.brightness,\n                onChange: e => handleParamChange('brightness', parseInt(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '5px',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u7F8E\\u989C\\u5F3A\\u5EA6: \", (params.beauty * 100).toFixed(0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"range\",\n                min: \"0\",\n                max: \"1\",\n                step: \"0.05\",\n                value: params.beauty,\n                onChange: e => handleParamChange('beauty', parseFloat(e.target.value)),\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: !selectedFile || isLoading,\n        style: {\n          padding: '12px 24px',\n          backgroundColor: isLoading ? '#ccc' : '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          fontSize: '16px',\n          fontWeight: 'bold',\n          width: '100%'\n        },\n        children: isLoading ? '正在增强...' : '开始增强图像'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"NSfU0BSMQA4M8PwsbFT6TBGAtlQ=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "showAdvanced", "setShowAdvanced", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fetch", "then", "res", "json", "data", "default", "catch", "err", "console", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "type", "startsWith", "size", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "style", "marginBottom", "children", "onSubmit", "htmlFor", "display", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "onChange", "accept", "required", "disabled", "color", "padding", "border", "borderRadius", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "backgroundColor", "marginTop", "width", "alignItems", "cursor", "checked", "marginRight", "gridTemplateColumns", "gap", "min", "max", "step", "parseInt", "paddingTop", "borderTop", "toFixed", "parseFloat", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/')\n      .then(res => res.json())\n      .then(data => {\n        setPresets(data.presets);\n        if (data.presets.default) {\n          setParams(data.presets.default.params);\n        }\n      })\n      .catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom'); // 切换到自定义模式\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    setError(null);\n\n    if (file) {\n      // 验证文件类型\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      // 验证文件大小 (限制为10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      setSelectedFile(file);\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  return (\n    <div style={{ marginBottom: '20px' }}>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '15px' }}>\n          <label htmlFor=\"file-input\" style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n            选择图像文件:\n          </label>\n          <input\n            id=\"file-input\"\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{ marginBottom: '10px' }}\n          />\n        </div>\n\n        {error && (\n          <div style={{ color: 'red', marginBottom: '10px', padding: '5px', border: '1px solid red', borderRadius: '3px' }}>\n            {error}\n          </div>\n        )}\n\n        {previewUrl && (\n          <div style={{ marginBottom: '15px' }}>\n            <p style={{ fontWeight: 'bold' }}>原始图像预览:</p>\n            <img\n              src={previewUrl}\n              alt=\"原始图像\"\n              style={{ maxWidth: '300px', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '5px' }}\n            />\n          </div>\n        )}\n\n        {/* 参数配置面板 */}\n        <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px', backgroundColor: '#f9f9f9' }}>\n          <h3 style={{ marginTop: 0, marginBottom: '15px' }}>增强设置</h3>\n\n          {/* 预设选择 */}\n          <div style={{ marginBottom: '15px' }}>\n            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n              预设配置:\n            </label>\n            <select\n              value={selectedPreset}\n              onChange={(e) => handlePresetChange(e.target.value)}\n              style={{ width: '100%', padding: '8px', borderRadius: '3px', border: '1px solid #ccc' }}\n            >\n              <option value=\"default\">默认设置</option>\n              <option value=\"portrait\">人像优化</option>\n              <option value=\"landscape\">风景增强</option>\n              <option value=\"vintage\">复古风格</option>\n              <option value=\"fast\">快速处理</option>\n              <option value=\"custom\">自定义</option>\n            </select>\n          </div>\n\n          {/* 高级设置切换 */}\n          <div style={{ marginBottom: '15px' }}>\n            <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>\n              <input\n                type=\"checkbox\"\n                checked={showAdvanced}\n                onChange={(e) => setShowAdvanced(e.target.checked)}\n                style={{ marginRight: '8px' }}\n              />\n              显示高级设置\n            </label>\n          </div>\n\n          {/* 基础设置 */}\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\n            {/* 超分倍数 */}\n            <div>\n              <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                超分倍数: {params.scale}x\n              </label>\n              <input\n                type=\"range\"\n                min=\"2\"\n                max=\"4\"\n                step=\"2\"\n                value={params.scale}\n                onChange={(e) => handleParamChange('scale', parseInt(e.target.value))}\n                style={{ width: '100%' }}\n              />\n            </div>\n\n            {/* 使用RealESRGAN */}\n            <div>\n              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>\n                <input\n                  type=\"checkbox\"\n                  checked={params.use_realesrgan}\n                  onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}\n                  style={{ marginRight: '8px' }}\n                />\n                使用RealESRGAN模型\n              </label>\n            </div>\n          </div>\n\n          {/* 高级设置 */}\n          {showAdvanced && (\n            <div style={{ marginTop: '20px', paddingTop: '15px', borderTop: '1px solid #ddd' }}>\n              <h4 style={{ marginTop: 0, marginBottom: '15px' }}>高级参数</h4>\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\n\n                {/* 锐化强度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                    锐化强度: {(params.sharpening * 100).toFixed(0)}%\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1\"\n                    step=\"0.05\"\n                    value={params.sharpening}\n                    onChange={(e) => handleParamChange('sharpening', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 降噪强度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                    降噪强度: {params.denoising}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"30\"\n                    step=\"1\"\n                    value={params.denoising}\n                    onChange={(e) => handleParamChange('denoising', parseInt(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 饱和度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                    饱和度: {params.saturation.toFixed(1)}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"2\"\n                    step=\"0.1\"\n                    value={params.saturation}\n                    onChange={(e) => handleParamChange('saturation', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 对比度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                    对比度: {params.contrast.toFixed(1)}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"2\"\n                    step=\"0.05\"\n                    value={params.contrast}\n                    onChange={(e) => handleParamChange('contrast', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 亮度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                    亮度: {params.brightness > 0 ? '+' : ''}{params.brightness}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"-100\"\n                    max=\"100\"\n                    step=\"5\"\n                    value={params.brightness}\n                    onChange={(e) => handleParamChange('brightness', parseInt(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n\n                {/* 美颜强度 */}\n                <div>\n                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n                    美颜强度: {(params.beauty * 100).toFixed(0)}%\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1\"\n                    step=\"0.05\"\n                    value={params.beauty}\n                    onChange={(e) => handleParamChange('beauty', parseFloat(e.target.value))}\n                    style={{ width: '100%' }}\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={!selectedFile || isLoading}\n          style={{\n            padding: '12px 24px',\n            backgroundColor: isLoading ? '#ccc' : '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            fontSize: '16px',\n            fontWeight: 'bold',\n            width: '100%'\n          }}\n        >\n          {isLoading ? '正在增强...' : '开始增强图像'}\n        </button>\n      </form>\n    </div>\n  );\n};\n\nexport default UploadForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC;IACnCsB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA5B,SAAS,CAAC,MAAM;IACd6B,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZnB,UAAU,CAACmB,IAAI,CAACpB,OAAO,CAAC;MACxB,IAAIoB,IAAI,CAACpB,OAAO,CAACqB,OAAO,EAAE;QACxBd,SAAS,CAACa,IAAI,CAACpB,OAAO,CAACqB,OAAO,CAACf,MAAM,CAAC;MACxC;IACF,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAAC1B,KAAK,CAAC,WAAW,EAAEyB,GAAG,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,kBAAkB,GAAIC,SAAS,IAAK;IACxCvB,iBAAiB,CAACuB,SAAS,CAAC;IAC5B,IAAI1B,OAAO,CAAC0B,SAAS,CAAC,EAAE;MACtBnB,SAAS,CAACP,OAAO,CAAC0B,SAAS,CAAC,CAACpB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxCtB,SAAS,CAACuB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACH1B,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC;EAED,MAAM4B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BpC,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAIkC,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCtC,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;;MAEA;MACA,IAAIkC,IAAI,CAACK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCvC,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;MAEAJ,eAAe,CAACsC,IAAI,CAAC;;MAErB;MACA,MAAMM,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIT,CAAC,IAAKnC,aAAa,CAACmC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLtC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAM+C,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACnD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAM+C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtD,YAAY,CAAC;IACrCoD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC5C,MAAM,CAAC,CAAC;IACjDf,QAAQ,CAACuD,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEzD,OAAA;IAAK8D,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,eACnChE,OAAA;MAAMiE,QAAQ,EAAEV,YAAa;MAAAS,QAAA,gBAC3BhE,OAAA;QAAK8D,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnChE,OAAA;UAAOkE,OAAO,EAAC,YAAY;UAACJ,KAAK,EAAE;YAAEK,OAAO,EAAE,OAAO;YAAEJ,YAAY,EAAE,KAAK;YAAEK,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAElG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRxE,OAAA;UACEyE,EAAE,EAAC,YAAY;UACf1B,IAAI,EAAC,MAAM;UACX2B,QAAQ,EAAEhC,gBAAiB;UAC3BiC,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAE1E,SAAU;UACpB2D,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL/D,KAAK,iBACJT,OAAA;QAAK8D,KAAK,EAAE;UAAEgB,KAAK,EAAE,KAAK;UAAEf,YAAY,EAAE,MAAM;UAAEgB,OAAO,EAAE,KAAK;UAAEC,MAAM,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAjB,QAAA,EAC9GvD;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAjE,UAAU,iBACTP,OAAA;QAAK8D,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnChE,OAAA;UAAG8D,KAAK,EAAE;YAAEM,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7CxE,OAAA;UACEkF,GAAG,EAAE3E,UAAW;UAChB4E,GAAG,EAAC,0BAAM;UACVrB,KAAK,EAAE;YAAEsB,QAAQ,EAAE,OAAO;YAAEC,SAAS,EAAE,OAAO;YAAEL,MAAM,EAAE,gBAAgB;YAAEC,YAAY,EAAE;UAAM;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGDxE,OAAA;QAAK8D,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEgB,OAAO,EAAE,MAAM;UAAEC,MAAM,EAAE,gBAAgB;UAAEC,YAAY,EAAE,KAAK;UAAEK,eAAe,EAAE;QAAU,CAAE;QAAAtB,QAAA,gBAC/HhE,OAAA;UAAI8D,KAAK,EAAE;YAAEyB,SAAS,EAAE,CAAC;YAAExB,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG5DxE,OAAA;UAAK8D,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACnChE,OAAA;YAAO8D,KAAK,EAAE;cAAEK,OAAO,EAAE,OAAO;cAAEJ,YAAY,EAAE,KAAK;cAAEK,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxE,OAAA;YACEwC,KAAK,EAAE3B,cAAe;YACtB6D,QAAQ,EAAG/B,CAAC,IAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;YACpDsB,KAAK,EAAE;cAAE0B,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEE,YAAY,EAAE,KAAK;cAAED,MAAM,EAAE;YAAiB,CAAE;YAAAhB,QAAA,gBAExFhE,OAAA;cAAQwC,KAAK,EAAC,SAAS;cAAAwB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCxE,OAAA;cAAQwC,KAAK,EAAC,UAAU;cAAAwB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCxE,OAAA;cAAQwC,KAAK,EAAC,WAAW;cAAAwB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCxE,OAAA;cAAQwC,KAAK,EAAC,SAAS;cAAAwB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCxE,OAAA;cAAQwC,KAAK,EAAC,MAAM;cAAAwB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCxE,OAAA;cAAQwC,KAAK,EAAC,QAAQ;cAAAwB,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNxE,OAAA;UAAK8D,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,eACnChE,OAAA;YAAO8D,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEsB,UAAU,EAAE,QAAQ;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAA1B,QAAA,gBACzEhE,OAAA;cACE+C,IAAI,EAAC,UAAU;cACf4C,OAAO,EAAE5E,YAAa;cACtB2D,QAAQ,EAAG/B,CAAC,IAAK3B,eAAe,CAAC2B,CAAC,CAACE,MAAM,CAAC8C,OAAO,CAAE;cACnD7B,KAAK,EAAE;gBAAE8B,WAAW,EAAE;cAAM;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,wCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNxE,OAAA;UAAK8D,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAE0B,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAA9B,QAAA,gBAExGhE,OAAA;YAAAgE,QAAA,gBACEhE,OAAA;cAAO8D,KAAK,EAAE;gBAAEK,OAAO,EAAE,OAAO;gBAAEJ,YAAY,EAAE,KAAK;gBAAEK,UAAU,EAAE;cAAO,CAAE;cAAAJ,QAAA,GAAC,4BACrE,EAAC/C,MAAM,CAACE,KAAK,EAAC,GACtB;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cACE+C,IAAI,EAAC,OAAO;cACZgD,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,GAAG;cACPC,IAAI,EAAC,GAAG;cACRzD,KAAK,EAAEvB,MAAM,CAACE,KAAM;cACpBuD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,OAAO,EAAE4D,QAAQ,CAACvD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;cACtEsB,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAO;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxE,OAAA;YAAAgE,QAAA,eACEhE,OAAA;cAAO8D,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEsB,UAAU,EAAE,QAAQ;gBAAEC,MAAM,EAAE;cAAU,CAAE;cAAA1B,QAAA,gBACzEhE,OAAA;gBACE+C,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE1E,MAAM,CAACG,cAAe;gBAC/BsD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,gBAAgB,EAAEK,CAAC,CAACE,MAAM,CAAC8C,OAAO,CAAE;gBACvE7B,KAAK,EAAE;kBAAE8B,WAAW,EAAE;gBAAM;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,sCAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLzD,YAAY,iBACXf,OAAA;UAAK8D,KAAK,EAAE;YAAEyB,SAAS,EAAE,MAAM;YAAEY,UAAU,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAiB,CAAE;UAAApC,QAAA,gBACjFhE,OAAA;YAAI8D,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAExB,YAAY,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DxE,OAAA;YAAK8D,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAE0B,mBAAmB,EAAE,sCAAsC;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAA9B,QAAA,gBAGxGhE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,GAAC,4BACrE,EAAC,CAAC/C,MAAM,CAACI,UAAU,GAAG,GAAG,EAAEgF,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9C;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZgD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACXzD,KAAK,EAAEvB,MAAM,CAACI,UAAW;gBACzBqD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC7EsB,KAAK,EAAE;kBAAE0B,KAAK,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,GAAC,4BACrE,EAAC/C,MAAM,CAACK,SAAS;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZgD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRC,IAAI,EAAC,GAAG;gBACRzD,KAAK,EAAEvB,MAAM,CAACK,SAAU;gBACxBoD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,WAAW,EAAE4D,QAAQ,CAACvD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC1EsB,KAAK,EAAE;kBAAE0B,KAAK,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,GAAC,sBACtE,EAAC/C,MAAM,CAACM,UAAU,CAAC8E,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZgD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,KAAK;gBACVzD,KAAK,EAAEvB,MAAM,CAACM,UAAW;gBACzBmD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC7EsB,KAAK,EAAE;kBAAE0B,KAAK,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,GAAC,sBACtE,EAAC/C,MAAM,CAACO,QAAQ,CAAC6E,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZgD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACXzD,KAAK,EAAEvB,MAAM,CAACO,QAAS;gBACvBkD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,UAAU,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC3EsB,KAAK,EAAE;kBAAE0B,KAAK,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,GAAC,gBACvE,EAAC/C,MAAM,CAACQ,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAER,MAAM,CAACQ,UAAU;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZgD,GAAG,EAAC,MAAM;gBACVC,GAAG,EAAC,KAAK;gBACTC,IAAI,EAAC,GAAG;gBACRzD,KAAK,EAAEvB,MAAM,CAACQ,UAAW;gBACzBiD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAE4D,QAAQ,CAACvD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBAC3EsB,KAAK,EAAE;kBAAE0B,KAAK,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAO8D,KAAK,EAAE;kBAAEK,OAAO,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEK,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,GAAC,4BACrE,EAAC,CAAC/C,MAAM,CAACS,MAAM,GAAG,GAAG,EAAE2E,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1C;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA;gBACE+C,IAAI,EAAC,OAAO;gBACZgD,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACXzD,KAAK,EAAEvB,MAAM,CAACS,MAAO;gBACrBgD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,QAAQ,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;gBACzEsB,KAAK,EAAE;kBAAE0B,KAAK,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxE,OAAA;QACE+C,IAAI,EAAC,QAAQ;QACb8B,QAAQ,EAAE,CAACxE,YAAY,IAAIF,SAAU;QACrC2D,KAAK,EAAE;UACLiB,OAAO,EAAE,WAAW;UACpBO,eAAe,EAAEnF,SAAS,GAAG,MAAM,GAAG,SAAS;UAC/C2E,KAAK,EAAE,OAAO;UACdE,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBS,MAAM,EAAEvF,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7CoG,QAAQ,EAAE,MAAM;UAChBnC,UAAU,EAAE,MAAM;UAClBoB,KAAK,EAAE;QACT,CAAE;QAAAxB,QAAA,EAED7D,SAAS,GAAG,SAAS,GAAG;MAAQ;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpE,EAAA,CAhUIH,UAAU;AAAAuG,EAAA,GAAVvG,UAAU;AAkUhB,eAAeA,UAAU;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}