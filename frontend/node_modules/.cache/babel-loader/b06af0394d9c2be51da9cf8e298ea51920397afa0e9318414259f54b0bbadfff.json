{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const handleFileSelect = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n      setError(null);\n    }\n  };\n  const handleUpload = async () => {\n    if (!selectedFile) {\n      setError('请选择图像文件');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/jpeg;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.jpg`;\n      link.click();\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setSelectedFile(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto',\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif',\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#333',\n          marginBottom: '10px',\n          fontSize: '2.5em'\n        },\n        children: \"\\uD83D\\uDE80 AI\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          fontSize: '1.1em',\n          marginBottom: '20px'\n        },\n        children: \"\\u4F7F\\u7528\\u5148\\u8FDB\\u7684AI\\u6280\\u672F\\u63D0\\u5347\\u56FE\\u50CF\\u5206\\u8FA8\\u7387\\u548C\\u8D28\\u91CF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8d7da',\n        color: '#721c24',\n        padding: '15px',\n        borderRadius: '5px',\n        marginBottom: '20px',\n        border: '1px solid #f5c6cb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"\\u9519\\u8BEF:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this), \" \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleReset,\n        style: {\n          marginLeft: '10px',\n          padding: '5px 10px',\n          backgroundColor: '#dc3545',\n          color: 'white',\n          border: 'none',\n          borderRadius: '3px',\n          cursor: 'pointer'\n        },\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 9\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#d1ecf1',\n        color: '#0c5460',\n        padding: '15px',\n        borderRadius: '5px',\n        marginBottom: '20px',\n        border: '1px solid #bee5eb',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: \"\\uD83D\\uDD04 \\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        padding: '30px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '20px',\n          color: '#333'\n        },\n        children: \"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: handleFileSelect,\n        style: {\n          width: '100%',\n          padding: '10px',\n          border: '2px dashed #ddd',\n          borderRadius: '5px',\n          marginBottom: '15px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '15px',\n          color: '#666'\n        },\n        children: [\"\\u5DF2\\u9009\\u62E9: \", selectedFile.name, \" (\", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleUpload,\n        disabled: !selectedFile || isLoading,\n        style: {\n          padding: '12px 24px',\n          backgroundColor: selectedFile && !isLoading ? '#28a745' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: selectedFile && !isLoading ? 'pointer' : 'not-allowed',\n          fontSize: '16px',\n          fontWeight: 'bold'\n        },\n        children: isLoading ? '处理中...' : '开始增强'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        padding: '30px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '20px',\n          color: '#333'\n        },\n        children: \"\\u5904\\u7406\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u6587\\u4EF6\\u540D:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), \" \", result.filename, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 52\n        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u72B6\\u6001:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), \" \", result.message, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 50\n        }, this), result.parameters && /*#__PURE__*/_jsxDEV(\"details\", {\n          style: {\n            marginTop: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              cursor: 'pointer',\n              color: '#007bff'\n            },\n            children: \"\\u67E5\\u770B\\u5904\\u7406\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px',\n              padding: '10px',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              fontSize: '14px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u653E\\u5927\\u500D\\u6570:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), \" \", result.parameters.scale, \"x\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 68\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"AI\\u6A21\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 19\n            }, this), \" \", result.parameters.use_realesrgan ? 'RealESRGAN' : '传统算法', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 100\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9510\\u5316:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this), \" \", result.parameters.sharpening, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 70\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9971\\u548C\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this), \" \", result.parameters.saturation, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 71\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this), \" \", result.parameters.contrast, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 69\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u964D\\u566A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this), \" \", result.parameters.denoising, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 69\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4EAE\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this), \" \", result.parameters.brightness, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 70\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u7F8E\\u989C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this), \" \", result.parameters.beauty]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), result.enhanced_image && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: `data:image/jpeg;base64,${result.enhanced_image}`,\n          alt: \"\\u589E\\u5F3A\\u540E\\u7684\\u56FE\\u50CF\",\n          style: {\n            maxWidth: '100%',\n            maxHeight: '500px',\n            border: '1px solid #ddd',\n            borderRadius: '5px'\n          },\n          onLoad: () => console.log('图像加载成功'),\n          onError: e => {\n            console.error('图像加载失败:', e);\n            setError('图像显示失败，请检查数据格式');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: downloadImage,\n          style: {\n            padding: '10px 20px',\n            backgroundColor: '#17a2b8',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer',\n            fontSize: '16px',\n            marginRight: '10px'\n          },\n          children: \"\\uD83D\\uDCBE \\u4E0B\\u8F7D\\u56FE\\u50CF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleReset,\n          style: {\n            padding: '10px 20px',\n            backgroundColor: '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDD04 \\u5904\\u7406\\u65B0\\u56FE\\u50CF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '15px',\n          color: '#333'\n        },\n        children: \"\\u7CFB\\u7EDF\\u6D4B\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: async () => {\n          try {\n            const response = await fetch('http://localhost:8001/');\n            const data = await response.json();\n            alert('后端连接正常: ' + data.message);\n          } catch (err) {\n            alert('后端连接失败: ' + err.message);\n          }\n        },\n        style: {\n          padding: '8px 16px',\n          backgroundColor: '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          marginRight: '10px'\n        },\n        children: \"\\u6D4B\\u8BD5\\u540E\\u7AEF\\u8FDE\\u63A5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          console.log('前端状态:', {\n            result,\n            isLoading,\n            error,\n            selectedFile: selectedFile === null || selectedFile === void 0 ? void 0 : selectedFile.name\n          });\n          alert('前端状态已输出到控制台');\n        },\n        style: {\n          padding: '8px 16px',\n          backgroundColor: '#28a745',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"\\u68C0\\u67E5\\u524D\\u7AEF\\u72B6\\u6001\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"wWpBrigeK7NIp+NEHCt6OuOW0RM=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "selectedFile", "setSelectedFile", "handleFileSelect", "e", "file", "target", "files", "handleUpload", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "downloadImage", "enhanced_image", "link", "document", "createElement", "href", "download", "Date", "now", "click", "handleReset", "style", "max<PERSON><PERSON><PERSON>", "margin", "padding", "fontFamily", "backgroundColor", "minHeight", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderRadius", "border", "onClick", "marginLeft", "cursor", "boxShadow", "type", "accept", "onChange", "width", "name", "size", "toFixed", "disabled", "fontWeight", "filename", "parameters", "marginTop", "scale", "use_realesrgan", "sharpening", "saturation", "contrast", "denoising", "brightness", "beauty", "src", "alt", "maxHeight", "onLoad", "log", "onError", "marginRight", "alert", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  const handleFileSelect = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n      setError(null);\n    }\n  };\n\n  const handleUpload = async () => {\n    if (!selectedFile) {\n      setError('请选择图像文件');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/jpeg;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.jpg`;\n      link.click();\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setSelectedFile(null);\n  };\n\n  return (\n    <div style={{ \n      maxWidth: '1200px', \n      margin: '0 auto', \n      padding: '20px',\n      fontFamily: 'Arial, sans-serif',\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh'\n    }}>\n      <header style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ \n          color: '#333', \n          marginBottom: '10px',\n          fontSize: '2.5em'\n        }}>\n          🚀 AI图像增强工具\n        </h1>\n        <p style={{ \n          color: '#666', \n          fontSize: '1.1em',\n          marginBottom: '20px'\n        }}>\n          使用先进的AI技术提升图像分辨率和质量\n        </p>\n      </header>\n\n      {/* 错误提示 */}\n      {error && (\n        <div style={{ \n          backgroundColor: '#f8d7da', \n          color: '#721c24', \n          padding: '15px', \n          borderRadius: '5px', \n          marginBottom: '20px',\n          border: '1px solid #f5c6cb'\n        }}>\n          <strong>错误:</strong> {error}\n          <button \n            onClick={handleReset}\n            style={{\n              marginLeft: '10px',\n              padding: '5px 10px',\n              backgroundColor: '#dc3545',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer'\n            }}\n          >\n            重试\n          </button>\n        </div>\n      )}\n\n      {/* 加载提示 */}\n      {isLoading && (\n        <div style={{ \n          backgroundColor: '#d1ecf1', \n          color: '#0c5460', \n          padding: '15px', \n          borderRadius: '5px', \n          marginBottom: '20px',\n          border: '1px solid #bee5eb',\n          textAlign: 'center'\n        }}>\n          <div style={{ marginBottom: '10px' }}>\n            🔄 正在处理图像，请稍候...\n          </div>\n        </div>\n      )}\n\n      {/* 文件上传区域 */}\n      <div style={{\n        backgroundColor: 'white',\n        padding: '30px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        marginBottom: '20px'\n      }}>\n        <h3 style={{ marginBottom: '20px', color: '#333' }}>选择图像文件</h3>\n        \n        <input\n          type=\"file\"\n          accept=\"image/*\"\n          onChange={handleFileSelect}\n          style={{\n            width: '100%',\n            padding: '10px',\n            border: '2px dashed #ddd',\n            borderRadius: '5px',\n            marginBottom: '15px'\n          }}\n        />\n        \n        {selectedFile && (\n          <div style={{ marginBottom: '15px', color: '#666' }}>\n            已选择: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)\n          </div>\n        )}\n        \n        <button\n          onClick={handleUpload}\n          disabled={!selectedFile || isLoading}\n          style={{\n            padding: '12px 24px',\n            backgroundColor: selectedFile && !isLoading ? '#28a745' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: selectedFile && !isLoading ? 'pointer' : 'not-allowed',\n            fontSize: '16px',\n            fontWeight: 'bold'\n          }}\n        >\n          {isLoading ? '处理中...' : '开始增强'}\n        </button>\n      </div>\n\n      {/* 结果显示 */}\n      {result && (\n        <div style={{\n          backgroundColor: 'white',\n          padding: '30px',\n          borderRadius: '10px',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n          marginBottom: '20px'\n        }}>\n          <h3 style={{ marginBottom: '20px', color: '#333' }}>处理结果</h3>\n          \n          <div style={{ marginBottom: '20px' }}>\n            <strong>文件名:</strong> {result.filename}<br/>\n            <strong>状态:</strong> {result.message}<br/>\n            {result.parameters && (\n              <details style={{ marginTop: '10px' }}>\n                <summary style={{ cursor: 'pointer', color: '#007bff' }}>查看处理参数</summary>\n                <div style={{ marginTop: '8px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '14px' }}>\n                  <strong>放大倍数:</strong> {result.parameters.scale}x<br/>\n                  <strong>AI模型:</strong> {result.parameters.use_realesrgan ? 'RealESRGAN' : '传统算法'}<br/>\n                  <strong>锐化:</strong> {result.parameters.sharpening}<br/>\n                  <strong>饱和度:</strong> {result.parameters.saturation}<br/>\n                  <strong>对比度:</strong> {result.parameters.contrast}<br/>\n                  <strong>降噪:</strong> {result.parameters.denoising}<br/>\n                  <strong>亮度:</strong> {result.parameters.brightness}<br/>\n                  <strong>美颜:</strong> {result.parameters.beauty}\n                </div>\n              </details>\n            )}\n          </div>\n          \n          {result.enhanced_image && (\n            <div style={{ textAlign: 'center', marginBottom: '20px' }}>\n              <img \n                src={`data:image/jpeg;base64,${result.enhanced_image}`}\n                alt=\"增强后的图像\"\n                style={{ \n                  maxWidth: '100%', \n                  maxHeight: '500px',\n                  border: '1px solid #ddd',\n                  borderRadius: '5px'\n                }}\n                onLoad={() => console.log('图像加载成功')}\n                onError={(e) => {\n                  console.error('图像加载失败:', e);\n                  setError('图像显示失败，请检查数据格式');\n                }}\n              />\n            </div>\n          )}\n          \n          <div style={{ textAlign: 'center' }}>\n            <button \n              onClick={downloadImage}\n              style={{\n                padding: '10px 20px',\n                backgroundColor: '#17a2b8',\n                color: 'white',\n                border: 'none',\n                borderRadius: '5px',\n                cursor: 'pointer',\n                fontSize: '16px',\n                marginRight: '10px'\n              }}\n            >\n              💾 下载图像\n            </button>\n            \n            <button \n              onClick={handleReset}\n              style={{\n                padding: '10px 20px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '5px',\n                cursor: 'pointer',\n                fontSize: '16px'\n              }}\n            >\n              🔄 处理新图像\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 测试区域 */}\n      <div style={{\n        backgroundColor: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      }}>\n        <h3 style={{ marginBottom: '15px', color: '#333' }}>系统测试</h3>\n        \n        <button\n          onClick={async () => {\n            try {\n              const response = await fetch('http://localhost:8001/');\n              const data = await response.json();\n              alert('后端连接正常: ' + data.message);\n            } catch (err) {\n              alert('后端连接失败: ' + err.message);\n            }\n          }}\n          style={{\n            padding: '8px 16px',\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            marginRight: '10px'\n          }}\n        >\n          测试后端连接\n        </button>\n        \n        <button\n          onClick={() => {\n            console.log('前端状态:', {\n              result,\n              isLoading,\n              error,\n              selectedFile: selectedFile?.name\n            });\n            alert('前端状态已输出到控制台');\n          }}\n          style={{\n            padding: '8px 16px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          }}\n        >\n          检查前端状态\n        </button>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMa,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRH,eAAe,CAACG,IAAI,CAAC;MACrBL,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACP,YAAY,EAAE;MACjBD,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;IAEAF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEV,YAAY,CAAC;IAErC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCtB,SAAS,CAACyB,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACxB,KAAK,CAAC,OAAO,EAAEuB,GAAG,CAAC;MAC3BtB,QAAQ,CAACsB,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR1B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI9B,MAAM,IAAIA,MAAM,CAAC+B,cAAc,EAAE;MACnC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG,0BAA0BnC,MAAM,CAAC+B,cAAc,EAAE;MAC7DC,IAAI,CAACI,QAAQ,GAAG,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC5CN,IAAI,CAACO,KAAK,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBvC,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACEV,OAAA;IAAK4C,KAAK,EAAE;MACVC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,mBAAmB;MAC/BC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,gBACAnD,OAAA;MAAQ4C,KAAK,EAAE;QAAEQ,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC3DnD,OAAA;QAAI4C,KAAK,EAAE;UACTU,KAAK,EAAE,MAAM;UACbD,YAAY,EAAE,MAAM;UACpBE,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3D,OAAA;QAAG4C,KAAK,EAAE;UACRU,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,OAAO;UACjBF,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,EAAC;MAEH;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGRpD,KAAK,iBACJP,OAAA;MAAK4C,KAAK,EAAE;QACVK,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,SAAS;QAChBP,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,KAAK;QACnBP,YAAY,EAAE,MAAM;QACpBQ,MAAM,EAAE;MACV,CAAE;MAAAV,QAAA,gBACAnD,OAAA;QAAAmD,QAAA,EAAQ;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACpD,KAAK,eAC3BP,OAAA;QACE8D,OAAO,EAAEnB,WAAY;QACrBC,KAAK,EAAE;UACLmB,UAAU,EAAE,MAAM;UAClBhB,OAAO,EAAE,UAAU;UACnBE,eAAe,EAAE,SAAS;UAC1BK,KAAK,EAAE,OAAO;UACdO,MAAM,EAAE,MAAM;UACdD,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAGAtD,SAAS,iBACRL,OAAA;MAAK4C,KAAK,EAAE;QACVK,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,SAAS;QAChBP,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,KAAK;QACnBP,YAAY,EAAE,MAAM;QACpBQ,MAAM,EAAE,mBAAmB;QAC3BT,SAAS,EAAE;MACb,CAAE;MAAAD,QAAA,eACAnD,OAAA;QAAK4C,KAAK,EAAE;UAAES,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3D,OAAA;MAAK4C,KAAK,EAAE;QACVK,eAAe,EAAE,OAAO;QACxBF,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBK,SAAS,EAAE,4BAA4B;QACvCZ,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAnD,OAAA;QAAI4C,KAAK,EAAE;UAAES,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAAM;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE/D3D,OAAA;QACEkE,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,SAAS;QAChBC,QAAQ,EAAEzD,gBAAiB;QAC3BiC,KAAK,EAAE;UACLyB,KAAK,EAAE,MAAM;UACbtB,OAAO,EAAE,MAAM;UACfc,MAAM,EAAE,iBAAiB;UACzBD,YAAY,EAAE,KAAK;UACnBP,YAAY,EAAE;QAChB;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEDlD,YAAY,iBACXT,OAAA;QAAK4C,KAAK,EAAE;UAAES,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAH,QAAA,GAAC,sBAC9C,EAAC1C,YAAY,CAAC6D,IAAI,EAAC,IAAE,EAAC,CAAC7D,YAAY,CAAC8D,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,MAC1E;MAAA;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAED3D,OAAA;QACE8D,OAAO,EAAE9C,YAAa;QACtByD,QAAQ,EAAE,CAAChE,YAAY,IAAIJ,SAAU;QACrCuC,KAAK,EAAE;UACLG,OAAO,EAAE,WAAW;UACpBE,eAAe,EAAExC,YAAY,IAAI,CAACJ,SAAS,GAAG,SAAS,GAAG,SAAS;UACnEiD,KAAK,EAAE,OAAO;UACdO,MAAM,EAAE,MAAM;UACdD,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAEvD,YAAY,IAAI,CAACJ,SAAS,GAAG,SAAS,GAAG,aAAa;UAC9DkD,QAAQ,EAAE,MAAM;UAChBmB,UAAU,EAAE;QACd,CAAE;QAAAvB,QAAA,EAED9C,SAAS,GAAG,QAAQ,GAAG;MAAM;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLxD,MAAM,iBACLH,OAAA;MAAK4C,KAAK,EAAE;QACVK,eAAe,EAAE,OAAO;QACxBF,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBK,SAAS,EAAE,4BAA4B;QACvCZ,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAnD,OAAA;QAAI4C,KAAK,EAAE;UAAES,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE7D3D,OAAA;QAAK4C,KAAK,EAAE;UAAES,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACnCnD,OAAA;UAAAmD,QAAA,EAAQ;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACwE,QAAQ,eAAC3E,OAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5C3D,OAAA;UAAAmD,QAAA,EAAQ;QAAG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAAC6B,OAAO,eAAChC,OAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzCxD,MAAM,CAACyE,UAAU,iBAChB5E,OAAA;UAAS4C,KAAK,EAAE;YAAEiC,SAAS,EAAE;UAAO,CAAE;UAAA1B,QAAA,gBACpCnD,OAAA;YAAS4C,KAAK,EAAE;cAAEoB,MAAM,EAAE,SAAS;cAAEV,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACzE3D,OAAA;YAAK4C,KAAK,EAAE;cAAEiC,SAAS,EAAE,KAAK;cAAE9B,OAAO,EAAE,MAAM;cAAEE,eAAe,EAAE,SAAS;cAAEW,YAAY,EAAE,KAAK;cAAEL,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,gBACnHnD,OAAA;cAAAmD,QAAA,EAAQ;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACE,KAAK,EAAC,GAAC,eAAA9E,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtD3D,OAAA;cAAAmD,QAAA,EAAQ;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACG,cAAc,GAAG,YAAY,GAAG,MAAM,eAAC/E,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtF3D,OAAA;cAAAmD,QAAA,EAAQ;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACI,UAAU,eAAChF,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD3D,OAAA;cAAAmD,QAAA,EAAQ;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACK,UAAU,eAACjF,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzD3D,OAAA;cAAAmD,QAAA,EAAQ;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACM,QAAQ,eAAClF,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD3D,OAAA;cAAAmD,QAAA,EAAQ;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACO,SAAS,eAACnF,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD3D,OAAA;cAAAmD,QAAA,EAAQ;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACQ,UAAU,eAACpF,OAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD3D,OAAA;cAAAmD,QAAA,EAAQ;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,MAAM,CAACyE,UAAU,CAACS,MAAM;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELxD,MAAM,CAAC+B,cAAc,iBACpBlC,OAAA;QAAK4C,KAAK,EAAE;UAAEQ,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,eACxDnD,OAAA;UACEsF,GAAG,EAAE,0BAA0BnF,MAAM,CAAC+B,cAAc,EAAG;UACvDqD,GAAG,EAAC,sCAAQ;UACZ3C,KAAK,EAAE;YACLC,QAAQ,EAAE,MAAM;YAChB2C,SAAS,EAAE,OAAO;YAClB3B,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE;UAChB,CAAE;UACF6B,MAAM,EAAEA,CAAA,KAAM1D,OAAO,CAAC2D,GAAG,CAAC,QAAQ,CAAE;UACpCC,OAAO,EAAG/E,CAAC,IAAK;YACdmB,OAAO,CAACxB,KAAK,CAAC,SAAS,EAAEK,CAAC,CAAC;YAC3BJ,QAAQ,CAAC,gBAAgB,CAAC;UAC5B;QAAE;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAED3D,OAAA;QAAK4C,KAAK,EAAE;UAAEQ,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCnD,OAAA;UACE8D,OAAO,EAAE7B,aAAc;UACvBW,KAAK,EAAE;YACLG,OAAO,EAAE,WAAW;YACpBE,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,SAAS;YACjBT,QAAQ,EAAE,MAAM;YAChBqC,WAAW,EAAE;UACf,CAAE;UAAAzC,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACE8D,OAAO,EAAEnB,WAAY;UACrBC,KAAK,EAAE;YACLG,OAAO,EAAE,WAAW;YACpBE,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,SAAS;YACjBT,QAAQ,EAAE;UACZ,CAAE;UAAAJ,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3D,OAAA;MAAK4C,KAAK,EAAE;QACVK,eAAe,EAAE,OAAO;QACxBF,OAAO,EAAE,MAAM;QACfa,YAAY,EAAE,MAAM;QACpBK,SAAS,EAAE;MACb,CAAE;MAAAd,QAAA,gBACAnD,OAAA;QAAI4C,KAAK,EAAE;UAAES,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE7D3D,OAAA;QACE8D,OAAO,EAAE,MAAAA,CAAA,KAAY;UACnB,IAAI;YACF,MAAM1C,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,CAAC;YACtD,MAAMQ,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;YAClCmE,KAAK,CAAC,UAAU,GAAGhE,IAAI,CAACG,OAAO,CAAC;UAClC,CAAC,CAAC,OAAOF,GAAG,EAAE;YACZ+D,KAAK,CAAC,UAAU,GAAG/D,GAAG,CAACE,OAAO,CAAC;UACjC;QACF,CAAE;QACFY,KAAK,EAAE;UACLG,OAAO,EAAE,UAAU;UACnBE,eAAe,EAAE,SAAS;UAC1BK,KAAK,EAAE,OAAO;UACdO,MAAM,EAAE,MAAM;UACdD,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE,SAAS;UACjB4B,WAAW,EAAE;QACf,CAAE;QAAAzC,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET3D,OAAA;QACE8D,OAAO,EAAEA,CAAA,KAAM;UACb/B,OAAO,CAAC2D,GAAG,CAAC,OAAO,EAAE;YACnBvF,MAAM;YACNE,SAAS;YACTE,KAAK;YACLE,YAAY,EAAEA,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6D;UAC9B,CAAC,CAAC;UACFuB,KAAK,CAAC,aAAa,CAAC;QACtB,CAAE;QACFjD,KAAK,EAAE;UACLG,OAAO,EAAE,UAAU;UACnBE,eAAe,EAAE,SAAS;UAC1BK,KAAK,EAAE,OAAO;UACdO,MAAM,EAAE,MAAM;UACdD,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzD,EAAA,CAlUQD,GAAG;AAAA6F,EAAA,GAAH7F,GAAG;AAoUZ,eAAeA,GAAG;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}