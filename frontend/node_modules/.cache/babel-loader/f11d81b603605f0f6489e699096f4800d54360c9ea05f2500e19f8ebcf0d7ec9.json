{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n  const [showParams, setShowParams] = useState(false);\n  const containerRef = useRef(null);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !containerRef.current) return;\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#e0e0e0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '40px',\n        backgroundColor: '#2d2d2d',\n        borderBottom: '1px solid #444',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        gap: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '5px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowParams(!showParams),\n        style: {\n          padding: '4px 8px',\n          backgroundColor: showParams ? '#4a90e2' : '#555',\n          color: 'white',\n          border: 'none',\n          borderRadius: '3px',\n          cursor: 'pointer',\n          fontSize: '11px'\n        },\n        children: \"\\u53C2\\u6570\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '4px 8px',\n          backgroundColor: '#28a745',\n          color: 'white',\n          border: 'none',\n          borderRadius: '3px',\n          cursor: 'pointer',\n          fontSize: '11px'\n        },\n        children: \"\\u4E0B\\u8F7D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), showParams && result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#2d2d2d',\n        borderBottom: '1px solid #444',\n        padding: '10px 15px',\n        fontSize: '11px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: '500',\n          marginBottom: '8px',\n          color: '#fff'\n        },\n        children: \"\\u5904\\u7406\\u53C2\\u6570\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n          gap: '8px',\n          color: '#ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u500D\\u6570: \", result.params_used.scale, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u6A21\\u578B: \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u9510\\u5316: \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u964D\\u566A: \", result.params_used.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u9971\\u548C\\u5EA6: \", result.params_used.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u5BF9\\u6BD4\\u5EA6: \", result.params_used.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u4EAE\\u5EA6: \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u7F8E\\u989C: \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      children: viewMode === 'split' ?\n      /*#__PURE__*/\n      // 分割线对比模式\n      _jsxDEV(\"div\", {\n        ref: containerRef,\n        style: {\n          width: '100%',\n          height: '100%',\n          position: 'relative',\n          cursor: isDragging ? 'ew-resize' : 'default',\n          userSelect: 'none',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            maxWidth: '90%',\n            maxHeight: '90%',\n            display: 'inline-block'\n          },\n          children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '600px',\n              height: '400px',\n              border: '2px dashed #d32f2f',\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#d32f2f',\n              backgroundColor: '#2d2d2d'\n            },\n            children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n            alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n            onLoad: handleImageLoad,\n            onError: handleImageError,\n            style: {\n              maxWidth: '100%',\n              maxHeight: '100%',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              display: 'block',\n              opacity: imageLoaded ? 1 : 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: `${splitPosition}%`,\n              height: '100%',\n              overflow: 'hidden',\n              borderRadius: '4px 0 0 4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: originalImage,\n              alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n              style: {\n                width: `${100 * 100 / splitPosition}%`,\n                height: '100%',\n                objectFit: 'cover',\n                border: '1px solid #555',\n                borderRadius: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: `${splitPosition}%`,\n              width: '2px',\n              height: '100%',\n              backgroundColor: '#4a90e2',\n              cursor: 'ew-resize',\n              transform: 'translateX(-1px)',\n              zIndex: 10\n            },\n            onMouseDown: handleMouseDown,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                width: '16px',\n                height: '30px',\n                backgroundColor: '#4a90e2',\n                borderRadius: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontSize: '10px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u27F7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 17\n          }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              color: '#ccc',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              padding: '10px',\n              borderRadius: '4px'\n            },\n            children: \"\\u52A0\\u8F7D\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '10px',\n            left: '50%',\n            transform: 'translateX(-50%)',\n            backgroundColor: 'rgba(0,0,0,0.8)',\n            color: 'white',\n            padding: '4px 8px',\n            borderRadius: '4px',\n            fontSize: '11px'\n          },\n          children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u56FE | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A | \\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 并排对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '10px',\n          padding: '20px',\n          height: '100%',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontSize: '12px',\n              color: '#ccc'\n            },\n            children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: originalImage,\n            alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '400px',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              objectFit: 'contain'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontSize: '12px',\n              color: '#ccc'\n            },\n            children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '300px',\n              border: '2px dashed #d32f2f',\n              borderRadius: '4px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#d32f2f',\n              backgroundColor: '#2d2d2d'\n            },\n            children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n            alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n            onLoad: handleImageLoad,\n            onError: handleImageError,\n            style: {\n              maxWidth: '100%',\n              maxHeight: '400px',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              objectFit: 'contain',\n              display: imageLoaded ? 'block' : 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 17\n          }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '300px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '1px dashed #555',\n              borderRadius: '4px',\n              color: '#ccc'\n            },\n            children: \"\\u52A0\\u8F7D\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"lJBZc/NMlk4Ltdrwg3A1dmxWzZ0=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "showParams", "setShowParams", "containerRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "e", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleMouseUp", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "padding", "gap", "onClick", "border", "borderRadius", "cursor", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "params_used", "fontWeight", "marginBottom", "gridTemplateColumns", "scale", "use_realesrgan", "sharpening", "toFixed", "denoising", "saturation", "contrast", "brightness", "beauty", "position", "overflow", "ref", "userSelect", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", "src", "Date", "now", "alt", "onLoad", "onError", "opacity", "top", "objectFit", "transform", "zIndex", "onMouseDown", "bottom", "textAlign", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n  const [showParams, setShowParams] = useState(false);\n  const containerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n    \n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#e0e0e0'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2d2d2d',\n        borderBottom: '1px solid #444',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        gap: '10px'\n      }}>\n        <div style={{ display: 'flex', gap: '5px' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '11px'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '11px'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n\n        <div style={{ flex: 1 }}></div>\n\n        <button\n          onClick={() => setShowParams(!showParams)}\n          style={{\n            padding: '4px 8px',\n            backgroundColor: showParams ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          }}\n        >\n          参数\n        </button>\n\n        <button\n          onClick={downloadImage}\n          style={{\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          }}\n        >\n          下载\n        </button>\n      </div>\n\n      {/* 参数信息面板 */}\n      {showParams && result.params_used && (\n        <div style={{\n          backgroundColor: '#2d2d2d',\n          borderBottom: '1px solid #444',\n          padding: '10px 15px',\n          fontSize: '11px'\n        }}>\n          <div style={{ fontWeight: '500', marginBottom: '8px', color: '#fff' }}>处理参数</div>\n          <div style={{ \n            display: 'grid', \n            gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n            gap: '8px',\n            color: '#ccc'\n          }}>\n            <div>倍数: {result.params_used.scale}x</div>\n            <div>模型: {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n            <div>锐化: {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n            <div>降噪: {result.params_used.denoising}</div>\n            <div>饱和度: {result.params_used.saturation.toFixed(1)}</div>\n            <div>对比度: {result.params_used.contrast.toFixed(1)}</div>\n            <div>亮度: {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n            <div>美颜: {(result.params_used.beauty * 100).toFixed(0)}%</div>\n          </div>\n        </div>\n      )}\n\n      {/* 图像显示区域 */}\n      <div style={{ flex: 1, position: 'relative', overflow: 'hidden' }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div \n            ref={containerRef}\n            style={{ \n              width: '100%',\n              height: '100%',\n              position: 'relative',\n              cursor: isDragging ? 'ew-resize' : 'default',\n              userSelect: 'none',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            {originalImage && (\n              <div style={{ \n                position: 'relative', \n                maxWidth: '90%',\n                maxHeight: '90%',\n                display: 'inline-block'\n              }}>\n                {/* 增强图像作为背景 */}\n                {imageError ? (\n                  <div style={{\n                    width: '600px',\n                    height: '400px',\n                    border: '2px dashed #d32f2f',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: '#d32f2f',\n                    backgroundColor: '#2d2d2d'\n                  }}>\n                    增强图像加载失败\n                  </div>\n                ) : (\n                  <img\n                    src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                    alt=\"增强图像\"\n                    onLoad={handleImageLoad}\n                    onError={handleImageError}\n                    style={{\n                      maxWidth: '100%',\n                      maxHeight: '100%',\n                      border: '1px solid #555',\n                      borderRadius: '4px',\n                      display: 'block',\n                      opacity: imageLoaded ? 1 : 0.5\n                    }}\n                  />\n                )}\n                \n                {/* 原始图像覆盖层 */}\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    width: `${splitPosition}%`,\n                    height: '100%',\n                    overflow: 'hidden',\n                    borderRadius: '4px 0 0 4px'\n                  }}\n                >\n                  <img\n                    src={originalImage}\n                    alt=\"原始图像\"\n                    style={{\n                      width: `${100 * 100 / splitPosition}%`,\n                      height: '100%',\n                      objectFit: 'cover',\n                      border: '1px solid #555',\n                      borderRadius: '4px'\n                    }}\n                  />\n                </div>\n                \n                {/* 分割线 */}\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: `${splitPosition}%`,\n                    width: '2px',\n                    height: '100%',\n                    backgroundColor: '#4a90e2',\n                    cursor: 'ew-resize',\n                    transform: 'translateX(-1px)',\n                    zIndex: 10\n                  }}\n                  onMouseDown={handleMouseDown}\n                >\n                  {/* 分割线手柄 */}\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: '50%',\n                      left: '50%',\n                      transform: 'translate(-50%, -50%)',\n                      width: '16px',\n                      height: '30px',\n                      backgroundColor: '#4a90e2',\n                      borderRadius: '8px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '10px',\n                      fontWeight: 'bold'\n                    }}\n                  >\n                    ⟷\n                  </div>\n                </div>\n\n                {!imageLoaded && !imageError && (\n                  <div style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    color: '#ccc',\n                    backgroundColor: 'rgba(0,0,0,0.8)',\n                    padding: '10px',\n                    borderRadius: '4px'\n                  }}>\n                    加载中...\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* 分割位置指示 */}\n            <div style={{\n              position: 'absolute',\n              bottom: '10px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              fontSize: '11px'\n            }}>\n              左侧：原图 | 右侧：增强 | 位置：{splitPosition.toFixed(0)}%\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{ \n            display: 'flex', \n            gap: '10px', \n            padding: '20px',\n            height: '100%',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ flex: 1, textAlign: 'center' }}>\n                <div style={{ marginBottom: '8px', fontSize: '12px', color: '#ccc' }}>原始图像</div>\n                <img\n                  src={originalImage}\n                  alt=\"原始图像\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    border: '1px solid #555',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ flex: 1, textAlign: 'center' }}>\n              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#ccc' }}>增强图像</div>\n              \n              {imageError ? (\n                <div style={{\n                  width: '100%',\n                  height: '300px',\n                  border: '2px dashed #d32f2f',\n                  borderRadius: '4px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#d32f2f',\n                  backgroundColor: '#2d2d2d'\n                }}>\n                  图像加载失败\n                </div>\n              ) : (\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    border: '1px solid #555',\n                    borderRadius: '4px',\n                    objectFit: 'contain',\n                    display: imageLoaded ? 'block' : 'none'\n                  }}\n                />\n              )}\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '300px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #555',\n                  borderRadius: '4px',\n                  color: '#ccc'\n                }}>\n                  加载中...\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMqB,YAAY,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5BZ,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7BX,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,gCAAgCtB,MAAM,CAACuB,YAAY,EAAE;IACjEJ,IAAI,CAACK,QAAQ,GAAG,YAAYxB,MAAM,CAACyB,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,eAAe,GAAIC,CAAC,IAAK;IAC7BrB,aAAa,CAAC,IAAI,CAAC;IACnBqB,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAC,IAAK;IAC7B,IAAI,CAACtB,UAAU,IAAI,CAACM,YAAY,CAACmB,OAAO,EAAE;IAE1C,MAAMC,IAAI,GAAGpB,YAAY,CAACmB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IACzD,MAAMC,CAAC,GAAGN,CAAC,CAACO,OAAO,GAAGH,IAAI,CAACI,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGN,CAAC,GAAGF,IAAI,CAACS,KAAK,GAAI,GAAG,CAAC,CAAC;IACrEpC,gBAAgB,CAACgC,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BnC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAEDd,SAAS,CAAC,MAAM;IACd,IAAIa,UAAU,EAAE;MACdW,QAAQ,CAAC0B,gBAAgB,CAAC,WAAW,EAAEb,eAAe,CAAC;MACvDb,QAAQ,CAAC0B,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MACnD,OAAO,MAAM;QACXzB,QAAQ,CAAC2B,mBAAmB,CAAC,WAAW,EAAEd,eAAe,CAAC;QAC1Db,QAAQ,CAAC2B,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAACpC,UAAU,CAAC,CAAC;EAEhB,MAAMuC,UAAU,GAAGA,CAAA,KAAM;IACvBxC,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEV,OAAA;IAAKmD,KAAK,EAAE;MACVL,KAAK,EAAE,MAAM;MACbM,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEAzD,OAAA;MAAKmD,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAzD,OAAA;QAAKmD,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,gBAC1CzD,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAAC,OAAO,CAAE;UACpCqC,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBL,eAAe,EAAE1C,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,MAAM;YAC1D2C,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EACH;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtE,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAAC,cAAc,CAAE;UAC3CqC,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBL,eAAe,EAAE1C,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,MAAM;YACjE2C,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EACH;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRzD,QAAQ,KAAK,OAAO,iBACnBb,OAAA;UACE8D,OAAO,EAAEZ,UAAW;UACpBC,KAAK,EAAE;YACLS,OAAO,EAAE,SAAS;YAClBL,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EACH;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtE,OAAA;QAAKmD,KAAK,EAAE;UAAEoB,IAAI,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/BtE,OAAA;QACE8D,OAAO,EAAEA,CAAA,KAAM9C,aAAa,CAAC,CAACD,UAAU,CAAE;QAC1CoC,KAAK,EAAE;UACLS,OAAO,EAAE,SAAS;UAClBL,eAAe,EAAExC,UAAU,GAAG,SAAS,GAAG,MAAM;UAChDyC,KAAK,EAAE,OAAO;UACdO,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBC,QAAQ,EAAE;QACZ,CAAE;QAAAT,QAAA,EACH;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETtE,OAAA;QACE8D,OAAO,EAAE1C,aAAc;QACvB+B,KAAK,EAAE;UACLS,OAAO,EAAE,SAAS;UAClBL,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdO,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBC,QAAQ,EAAE;QACZ,CAAE;QAAAT,QAAA,EACH;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLvD,UAAU,IAAIb,MAAM,CAACsE,WAAW,iBAC/BxE,OAAA;MAAKmD,KAAK,EAAE;QACVI,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BE,OAAO,EAAE,WAAW;QACpBM,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBACAzD,OAAA;QAAKmD,KAAK,EAAE;UAAEsB,UAAU,EAAE,KAAK;UAAEC,YAAY,EAAE,KAAK;UAAElB,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAAI;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjFtE,OAAA;QAAKmD,KAAK,EAAE;UACVE,OAAO,EAAE,MAAM;UACfsB,mBAAmB,EAAE,sCAAsC;UAC3Dd,GAAG,EAAE,KAAK;UACVL,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACAzD,OAAA;UAAAyD,QAAA,GAAK,gBAAI,EAACvD,MAAM,CAACsE,WAAW,CAACI,KAAK,EAAC,GAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1CtE,OAAA;UAAAyD,QAAA,GAAK,gBAAI,EAACvD,MAAM,CAACsE,WAAW,CAACK,cAAc,GAAG,YAAY,GAAG,MAAM;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1EtE,OAAA;UAAAyD,QAAA,GAAK,gBAAI,EAAC,CAACvD,MAAM,CAACsE,WAAW,CAACM,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClEtE,OAAA;UAAAyD,QAAA,GAAK,gBAAI,EAACvD,MAAM,CAACsE,WAAW,CAACQ,SAAS;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7CtE,OAAA;UAAAyD,QAAA,GAAK,sBAAK,EAACvD,MAAM,CAACsE,WAAW,CAACS,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DtE,OAAA;UAAAyD,QAAA,GAAK,sBAAK,EAACvD,MAAM,CAACsE,WAAW,CAACU,QAAQ,CAACH,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDtE,OAAA;UAAAyD,QAAA,GAAK,gBAAI,EAACvD,MAAM,CAACsE,WAAW,CAACW,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEjF,MAAM,CAACsE,WAAW,CAACW,UAAU;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5FtE,OAAA;UAAAyD,QAAA,GAAK,gBAAI,EAAC,CAACvD,MAAM,CAACsE,WAAW,CAACY,MAAM,GAAG,GAAG,EAAEL,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtE,OAAA;MAAKmD,KAAK,EAAE;QAAEoB,IAAI,EAAE,CAAC;QAAEc,QAAQ,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAA7B,QAAA,EAC/D5C,QAAQ,KAAK,OAAO;MAAA;MACnB;MACAb,OAAA;QACEuF,GAAG,EAAEtE,YAAa;QAClBkC,KAAK,EAAE;UACLL,KAAK,EAAE,MAAM;UACbM,MAAM,EAAE,MAAM;UACdiC,QAAQ,EAAE,UAAU;UACpBpB,MAAM,EAAEtD,UAAU,GAAG,WAAW,GAAG,SAAS;UAC5C6E,UAAU,EAAE,MAAM;UAClBnC,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpB8B,cAAc,EAAE;QAClB,CAAE;QAAAhC,QAAA,GAEDtD,aAAa,iBACZH,OAAA;UAAKmD,KAAK,EAAE;YACVkC,QAAQ,EAAE,UAAU;YACpBK,QAAQ,EAAE,KAAK;YACfC,SAAS,EAAE,KAAK;YAChBtC,OAAO,EAAE;UACX,CAAE;UAAAI,QAAA,GAEClD,UAAU,gBACTP,OAAA;YAAKmD,KAAK,EAAE;cACVL,KAAK,EAAE,OAAO;cACdM,MAAM,EAAE,OAAO;cACfW,MAAM,EAAE,oBAAoB;cAC5BC,YAAY,EAAE,KAAK;cACnBX,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpB8B,cAAc,EAAE,QAAQ;cACxBjC,KAAK,EAAE,SAAS;cAChBD,eAAe,EAAE;YACnB,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAENtE,OAAA;YACE4F,GAAG,EAAE,gCAAgC1F,MAAM,CAACuB,YAAY,MAAMoE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;YAC3EC,GAAG,EAAC,0BAAM;YACVC,MAAM,EAAE9E,eAAgB;YACxB+E,OAAO,EAAE9E,gBAAiB;YAC1BgC,KAAK,EAAE;cACLuC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,MAAM;cACjB5B,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBX,OAAO,EAAE,OAAO;cAChB6C,OAAO,EAAE7F,WAAW,GAAG,CAAC,GAAG;YAC7B;UAAE;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,eAGDtE,OAAA;YACEmD,KAAK,EAAE;cACLkC,QAAQ,EAAE,UAAU;cACpBc,GAAG,EAAE,CAAC;cACN1D,IAAI,EAAE,CAAC;cACPK,KAAK,EAAE,GAAGrC,aAAa,GAAG;cAC1B2C,MAAM,EAAE,MAAM;cACdkC,QAAQ,EAAE,QAAQ;cAClBtB,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,eAEFzD,OAAA;cACE4F,GAAG,EAAEzF,aAAc;cACnB4F,GAAG,EAAC,0BAAM;cACV5C,KAAK,EAAE;gBACLL,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAGrC,aAAa,GAAG;gBACtC2C,MAAM,EAAE,MAAM;gBACdgD,SAAS,EAAE,OAAO;gBAClBrC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE;cAChB;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtE,OAAA;YACEmD,KAAK,EAAE;cACLkC,QAAQ,EAAE,UAAU;cACpBc,GAAG,EAAE,CAAC;cACN1D,IAAI,EAAE,GAAGhC,aAAa,GAAG;cACzBqC,KAAK,EAAE,KAAK;cACZM,MAAM,EAAE,MAAM;cACdG,eAAe,EAAE,SAAS;cAC1BU,MAAM,EAAE,WAAW;cACnBoC,SAAS,EAAE,kBAAkB;cAC7BC,MAAM,EAAE;YACV,CAAE;YACFC,WAAW,EAAEvE,eAAgB;YAAAyB,QAAA,eAG7BzD,OAAA;cACEmD,KAAK,EAAE;gBACLkC,QAAQ,EAAE,UAAU;gBACpBc,GAAG,EAAE,KAAK;gBACV1D,IAAI,EAAE,KAAK;gBACX4D,SAAS,EAAE,uBAAuB;gBAClCvD,KAAK,EAAE,MAAM;gBACbM,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAE,SAAS;gBAC1BS,YAAY,EAAE,KAAK;gBACnBX,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpB8B,cAAc,EAAE,QAAQ;gBACxBjC,KAAK,EAAE,OAAO;gBACdU,QAAQ,EAAE,MAAM;gBAChBO,UAAU,EAAE;cACd,CAAE;cAAAhB,QAAA,EACH;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL,CAACjE,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;YAAKmD,KAAK,EAAE;cACVkC,QAAQ,EAAE,UAAU;cACpBc,GAAG,EAAE,KAAK;cACV1D,IAAI,EAAE,KAAK;cACX4D,SAAS,EAAE,uBAAuB;cAClC7C,KAAK,EAAE,MAAM;cACbD,eAAe,EAAE,iBAAiB;cAClCK,OAAO,EAAE,MAAM;cACfI,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGDtE,OAAA;UAAKmD,KAAK,EAAE;YACVkC,QAAQ,EAAE,UAAU;YACpBmB,MAAM,EAAE,MAAM;YACd/D,IAAI,EAAE,KAAK;YACX4D,SAAS,EAAE,kBAAkB;YAC7B9C,eAAe,EAAE,iBAAiB;YAClCC,KAAK,EAAE,OAAO;YACdI,OAAO,EAAE,SAAS;YAClBI,YAAY,EAAE,KAAK;YACnBE,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,GAAC,sFACkB,EAAChD,aAAa,CAACsE,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/C;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACAtE,OAAA;QAAKmD,KAAK,EAAE;UACVE,OAAO,EAAE,MAAM;UACfQ,GAAG,EAAE,MAAM;UACXD,OAAO,EAAE,MAAM;UACfR,MAAM,EAAE,MAAM;UACdO,UAAU,EAAE,QAAQ;UACpB8B,cAAc,EAAE;QAClB,CAAE;QAAAhC,QAAA,GAECtD,aAAa,iBACZH,OAAA;UAAKmD,KAAK,EAAE;YAAEoB,IAAI,EAAE,CAAC;YAAEkC,SAAS,EAAE;UAAS,CAAE;UAAAhD,QAAA,gBAC3CzD,OAAA;YAAKmD,KAAK,EAAE;cAAEuB,YAAY,EAAE,KAAK;cAAER,QAAQ,EAAE,MAAM;cAAEV,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChFtE,OAAA;YACE4F,GAAG,EAAEzF,aAAc;YACnB4F,GAAG,EAAC,0BAAM;YACV5C,KAAK,EAAE;cACLuC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClB5B,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBoC,SAAS,EAAE;YACb;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDtE,OAAA;UAAKmD,KAAK,EAAE;YAAEoB,IAAI,EAAE,CAAC;YAAEkC,SAAS,EAAE;UAAS,CAAE;UAAAhD,QAAA,gBAC3CzD,OAAA;YAAKmD,KAAK,EAAE;cAAEuB,YAAY,EAAE,KAAK;cAAER,QAAQ,EAAE,MAAM;cAAEV,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAE/E/D,UAAU,gBACTP,OAAA;YAAKmD,KAAK,EAAE;cACVL,KAAK,EAAE,MAAM;cACbM,MAAM,EAAE,OAAO;cACfW,MAAM,EAAE,oBAAoB;cAC5BC,YAAY,EAAE,KAAK;cACnBX,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpB8B,cAAc,EAAE,QAAQ;cACxBjC,KAAK,EAAE,SAAS;cAChBD,eAAe,EAAE;YACnB,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAENtE,OAAA;YACE4F,GAAG,EAAE,gCAAgC1F,MAAM,CAACuB,YAAY,MAAMoE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;YAC3EC,GAAG,EAAC,0BAAM;YACVC,MAAM,EAAE9E,eAAgB;YACxB+E,OAAO,EAAE9E,gBAAiB;YAC1BgC,KAAK,EAAE;cACLuC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClB5B,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBoC,SAAS,EAAE,SAAS;cACpB/C,OAAO,EAAEhD,WAAW,GAAG,OAAO,GAAG;YACnC;UAAE;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,EAEA,CAACjE,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;YAAKmD,KAAK,EAAE;cACVL,KAAK,EAAE,MAAM;cACbM,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpB8B,cAAc,EAAE,QAAQ;cACxB1B,MAAM,EAAE,iBAAiB;cACzBC,YAAY,EAAE,KAAK;cACnBR,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CApaIH,UAAU;AAAAyG,EAAA,GAAVzG,UAAU;AAsahB,eAAeA,UAAU;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}