{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const handleUpload = async formData => {\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = e => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [{\n      label: '打开图像 (Ctrl+O)',\n      action: () => {\n        var _fileInputRef$current;\n        return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n      }\n    }, {\n      label: '保存结果 (Ctrl+S)',\n      action: () => downloadImage(),\n      disabled: !result\n    }, {\n      label: '重置 (Ctrl+R)',\n      action: () => handleReset()\n    }],\n    edit: [{\n      label: '撤销',\n      action: () => {},\n      disabled: true\n    }, {\n      label: '重做',\n      action: () => {},\n      disabled: true\n    }],\n    view: [{\n      label: '分屏对比',\n      action: () => setViewMode('split'),\n      checked: viewMode === 'split'\n    }, {\n      label: '原图',\n      action: () => setViewMode('original'),\n      checked: viewMode === 'original'\n    }, {\n      label: '增强图',\n      action: () => setViewMode('enhanced'),\n      checked: viewMode === 'enhanced'\n    }, {\n      label: '---'\n    }, {\n      label: '放大 (Ctrl+=)',\n      action: () => setZoomLevel(prev => Math.min(prev + 25, 400))\n    }, {\n      label: '缩小 (Ctrl+-)',\n      action: () => setZoomLevel(prev => Math.max(prev - 25, 25))\n    }, {\n      label: '实际大小 (Ctrl+0)',\n      action: () => setZoomLevel(100)\n    }],\n    tools: [{\n      label: '顺时针旋转',\n      action: () => setRotation(prev => (prev + 90) % 360)\n    }, {\n      label: '逆时针旋转',\n      action: () => setRotation(prev => (prev - 90 + 360) % 360)\n    }, {\n      label: '重置旋转',\n      action: () => setRotation(0)\n    }, {\n      label: '---'\n    }, {\n      label: '批量处理',\n      action: () => {},\n      disabled: true\n    }],\n    help: [{\n      label: '快捷键',\n      action: () => showShortcuts()\n    }, {\n      label: '关于',\n      action: () => showAbout()\n    }]\n  };\n  const handleMenuClick = menuKey => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n  const showShortcuts = () => {\n    alert(`快捷键说明:\n\n文件操作:\n• Ctrl+O: 打开图像\n• Ctrl+S: 保存结果\n• Ctrl+R: 重置\n\n视图控制:\n• Ctrl+=: 放大\n• Ctrl+-: 缩小\n• Ctrl+0: 实际大小`);\n  };\n  const showAbout = () => {\n    alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          fontSize: '13px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u89C6\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u5DE5\\u5177\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u5E2E\\u52A9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83C\\uDD95 \\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83D\\uDCC1 \\u6253\\u5F00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => result && downloadImage(),\n            disabled: !result,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: result ? '#17a2b8' : '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: result ? 'pointer' : 'not-allowed',\n              fontSize: '12px'\n            },\n            children: \"\\uD83D\\uDCBE \\u4FDD\\u5B58\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(prev => Math.max(prev - 25, 25)),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDD0D-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#ccc',\n                fontSize: '11px',\n                minWidth: '50px',\n                textAlign: 'center'\n              },\n              children: [zoomLevel, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(prev => Math.min(prev + 25, 400)),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDD0D+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(100),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDCD0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setRotation(prev => (prev - 90 + 360) % 360),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\u21BA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#ccc',\n                fontSize: '11px',\n                minWidth: '30px',\n                textAlign: 'center'\n              },\n              children: [rotation, \"\\xB0\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setRotation(prev => (prev + 90) % 360),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\u21BB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('split'),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: viewMode === 'split' ? '#4a90e2' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDD00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('original'),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: viewMode === 'original' ? '#4a90e2' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDDBC\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('enhanced'),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ccc',\n              fontSize: '12px'\n            },\n            children: isLoading ? '处理中...' : result ? '已处理' : '等待上传图像'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          style: {\n            display: 'none'\n          },\n          onChange: e => {\n            const file = e.target.files[0];\n            if (file) {\n              // 这里可以直接触发上传或者显示在UploadForm中\n              console.log('选择了文件:', file.name);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [\"\\u9519\\u8BEF: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '8px',\n              textAlign: 'center',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '30px',\n                height: '30px',\n                border: '3px solid #333',\n                borderTop: '3px solid #4a90e2',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite',\n                margin: '0 auto'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this), result ? /*#__PURE__*/_jsxDEV(ResultView, {\n            result: result,\n            originalImage: originalImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u5C31\\u7EEA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"RealESRGAN v2.1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"1TlV2uPXZUja6R8rp8rFORARBYU=\");\n_c = App;\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "handleUpload", "formData", "file", "get", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "useEffect", "handleKeyDown", "ctrl<PERSON>ey", "key", "preventDefault", "fileInputRef", "current", "click", "handleReset", "downloadImage", "setZoomLevel", "prev", "Math", "min", "max", "document", "addEventListener", "removeEventListener", "menuItems", "label", "action", "_fileInputRef$current", "disabled", "edit", "view", "setViewMode", "checked", "viewMode", "tools", "setRotation", "help", "showShortcuts", "showAbout", "handleMenuClick", "<PERSON><PERSON>ey", "setShowMenu", "showMenu", "enhanced_image", "link", "createElement", "href", "download", "Date", "now", "alert", "setImageInfo", "setProcessingTime", "style", "height", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "borderBottom", "alignItems", "padding", "color", "gap", "marginRight", "width", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "textAlign", "fontSize", "fontWeight", "cursor", "position", "onClick", "border", "_fileInputRef$current2", "min<PERSON><PERSON><PERSON>", "zoomLevel", "rotation", "ref", "type", "accept", "onChange", "files", "log", "name", "justifyContent", "top", "left", "right", "zIndex", "transform", "marginBottom", "borderTop", "animation", "margin", "borderLeft", "onUpload", "jsx", "_c", "globalStyles", "styleElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n\n  const handleUpload = async (formData) => {\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [\n      { label: '打开图像 (Ctrl+O)', action: () => fileInputRef.current?.click() },\n      { label: '保存结果 (Ctrl+S)', action: () => downloadImage(), disabled: !result },\n      { label: '重置 (Ctrl+R)', action: () => handleReset() }\n    ],\n    edit: [\n      { label: '撤销', action: () => {}, disabled: true },\n      { label: '重做', action: () => {}, disabled: true }\n    ],\n    view: [\n      { label: '分屏对比', action: () => setViewMode('split'), checked: viewMode === 'split' },\n      { label: '原图', action: () => setViewMode('original'), checked: viewMode === 'original' },\n      { label: '增强图', action: () => setViewMode('enhanced'), checked: viewMode === 'enhanced' },\n      { label: '---' },\n      { label: '放大 (Ctrl+=)', action: () => setZoomLevel(prev => Math.min(prev + 25, 400)) },\n      { label: '缩小 (Ctrl+-)', action: () => setZoomLevel(prev => Math.max(prev - 25, 25)) },\n      { label: '实际大小 (Ctrl+0)', action: () => setZoomLevel(100) }\n    ],\n    tools: [\n      { label: '顺时针旋转', action: () => setRotation(prev => (prev + 90) % 360) },\n      { label: '逆时针旋转', action: () => setRotation(prev => (prev - 90 + 360) % 360) },\n      { label: '重置旋转', action: () => setRotation(0) },\n      { label: '---' },\n      { label: '批量处理', action: () => {}, disabled: true }\n    ],\n    help: [\n      { label: '快捷键', action: () => showShortcuts() },\n      { label: '关于', action: () => showAbout() }\n    ]\n  };\n\n  const handleMenuClick = (menuKey) => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n\n  const showShortcuts = () => {\n    alert(`快捷键说明:\n\n文件操作:\n• Ctrl+O: 打开图像\n• Ctrl+S: 保存结果\n• Ctrl+R: 重置\n\n视图控制:\n• Ctrl+=: 放大\n• Ctrl+-: 缩小\n• Ctrl+0: 实际大小`);\n  };\n\n  const showAbout = () => {\n    alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>文件</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>编辑</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>视图</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>工具</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>帮助</span>\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            {/* 文件操作 */}\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🆕 新建\n            </button>\n\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              📁 打开\n            </button>\n\n            <button\n              onClick={() => result && downloadImage()}\n              disabled={!result}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: result ? '#17a2b8' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: result ? 'pointer' : 'not-allowed',\n                fontSize: '12px'\n              }}\n            >\n              💾 保存\n            </button>\n\n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n\n            {/* 视图控制 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>\n              <button\n                onClick={() => setZoomLevel(prev => Math.max(prev - 25, 25))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                🔍-\n              </button>\n              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '50px', textAlign: 'center' }}>\n                {zoomLevel}%\n              </span>\n              <button\n                onClick={() => setZoomLevel(prev => Math.min(prev + 25, 400))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                🔍+\n              </button>\n              <button\n                onClick={() => setZoomLevel(100)}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                📐\n              </button>\n            </div>\n\n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n\n            {/* 旋转控制 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>\n              <button\n                onClick={() => setRotation(prev => (prev - 90 + 360) % 360)}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                ↺\n              </button>\n              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '30px', textAlign: 'center' }}>\n                {rotation}°\n              </span>\n              <button\n                onClick={() => setRotation(prev => (prev + 90) % 360)}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                ↻\n              </button>\n            </div>\n\n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n\n            {/* 视图模式 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>\n              <button\n                onClick={() => setViewMode('split')}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: viewMode === 'split' ? '#4a90e2' : '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                🔀\n              </button>\n              <button\n                onClick={() => setViewMode('original')}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: viewMode === 'original' ? '#4a90e2' : '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                🖼️\n              </button>\n              <button\n                onClick={() => setViewMode('enhanced')}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                ✨\n              </button>\n            </div>\n\n            <div style={{ flex: 1 }}></div>\n\n            {/* 状态信息 */}\n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {isLoading ? '处理中...' : result ? '已处理' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 隐藏的文件输入 */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            style={{ display: 'none' }}\n            onChange={(e) => {\n              const file = e.target.files[0];\n              if (file) {\n                // 这里可以直接触发上传或者显示在UploadForm中\n                console.log('选择了文件:', file.name);\n              }\n            }}\n          />\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <ResultView result={result} originalImage={originalImage} />\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>就绪</span>\n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n\n      {/* CSS动画 */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMe,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvCN,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMK,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKR,gBAAgB,CAACQ,CAAC,CAACC,MAAM,CAAChB,MAAM,CAAC;MACxDY,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCvB,SAAS,CAAC0B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,OAAO,EAAEwB,GAAG,CAAC;MAC3BvB,QAAQ,CAACuB,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA4B,SAAS,CAAC,MAAM;IACd,MAAMC,aAAa,GAAIjB,CAAC,IAAK;MAC3B;MACA,IAAIA,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClB,IAAIC,YAAY,CAACC,OAAO,EAAE;UACxBD,YAAY,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9B;MACF;MACA;MACA,IAAIvB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBI,WAAW,CAAC,CAAC;MACf;MACA;MACA,IAAIxB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,IAAIlC,MAAM,EAAE;QACxCe,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBK,aAAa,CAAC,CAAC;MACjB;MACA;MACA,IAAIzB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBM,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;MAChD;MACA;MACA,IAAI3B,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBM,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;MAC/C;MACA;MACA,IAAI3B,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBM,YAAY,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;IAEDK,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEf,aAAa,CAAC;IACnD,OAAO,MAAMc,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEhB,aAAa,CAAC;EACrE,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMiD,SAAS,GAAG;IAChBvC,IAAI,EAAE,CACJ;MAAEwC,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA;QAAA,IAAAC,qBAAA;QAAA,QAAAA,qBAAA,GAAMhB,YAAY,CAACC,OAAO,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,KAAK,CAAC,CAAC;MAAA;IAAC,CAAC,EACvE;MAAEY,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA,KAAMX,aAAa,CAAC,CAAC;MAAEa,QAAQ,EAAE,CAACrD;IAAO,CAAC,EAC5E;MAAEkD,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMZ,WAAW,CAAC;IAAE,CAAC,CACtD;IACDe,IAAI,EAAE,CACJ;MAAEJ,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,EACjD;MAAEH,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,CAClD;IACDE,IAAI,EAAE,CACJ;MAAEL,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAMK,WAAW,CAAC,OAAO,CAAC;MAAEC,OAAO,EAAEC,QAAQ,KAAK;IAAQ,CAAC,EACpF;MAAER,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAMK,WAAW,CAAC,UAAU,CAAC;MAAEC,OAAO,EAAEC,QAAQ,KAAK;IAAW,CAAC,EACxF;MAAER,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEA,CAAA,KAAMK,WAAW,CAAC,UAAU,CAAC;MAAEC,OAAO,EAAEC,QAAQ,KAAK;IAAW,CAAC,EACzF;MAAER,KAAK,EAAE;IAAM,CAAC,EAChB;MAAEA,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMV,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC;IAAE,CAAC,EACtF;MAAEQ,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMV,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC;IAAE,CAAC,EACrF;MAAEQ,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA,KAAMV,YAAY,CAAC,GAAG;IAAE,CAAC,CAC5D;IACDkB,KAAK,EAAE,CACL;MAAET,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAEA,CAAA,KAAMS,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,IAAI,GAAG;IAAE,CAAC,EACxE;MAAEQ,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAEA,CAAA,KAAMS,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG;IAAE,CAAC,EAC9E;MAAEQ,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAMS,WAAW,CAAC,CAAC;IAAE,CAAC,EAC/C;MAAEV,KAAK,EAAE;IAAM,CAAC,EAChB;MAAEA,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,CACpD;IACDQ,IAAI,EAAE,CACJ;MAAEX,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEA,CAAA,KAAMW,aAAa,CAAC;IAAE,CAAC,EAC/C;MAAEZ,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAMY,SAAS,CAAC;IAAE,CAAC;EAE9C,CAAC;EAED,MAAMC,eAAe,GAAIC,OAAO,IAAK;IACnCC,WAAW,CAACC,QAAQ,KAAKF,OAAO,GAAG,IAAI,GAAGA,OAAO,CAAC;EACpD,CAAC;EAED,MAAMzB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIxC,MAAM,IAAIA,MAAM,CAACoE,cAAc,EAAE;MACnC,MAAMC,IAAI,GAAGvB,QAAQ,CAACwB,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,yBAAyBvE,MAAM,CAACoE,cAAc,EAAE;MAC5DC,IAAI,CAACG,QAAQ,GAAG,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC5CL,IAAI,CAAC/B,KAAK,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1Ba,KAAK,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,CAAC;EACd,CAAC;EAED,MAAMZ,SAAS,GAAGA,CAAA,KAAM;IACtBY,KAAK,CAAC,2CAA2C,CAAC;EACpD,CAAC;EAED,MAAMpC,WAAW,GAAGA,CAAA,KAAM;IACxBtC,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;IACtBqE,YAAY,CAAC,IAAI,CAAC;IAClBC,iBAAiB,CAAC,IAAI,CAAC;IACvBpC,YAAY,CAAC,GAAG,CAAC;IACjBe,WAAW,CAAC,OAAO,CAAC;IACpBI,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACE/D,OAAA;IAAKiF,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAxF,OAAA;MAAKiF,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BM,YAAY,EAAE,gBAAgB;QAC9BJ,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAJ,QAAA,gBAEAxF,OAAA;QAAKiF,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC/DxF,OAAA;UAAKiF,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGpG,OAAA;UAAKiF,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGpG,OAAA;UAAKiF,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNpG,OAAA;QAAKiF,KAAK,EAAE;UACVoB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBZ,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNpG,OAAA;QAAKiF,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAC7DxF,OAAA;UAAMiF,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpG,OAAA;UAAMiF,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpG,OAAA;UAAMiF,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpG,OAAA;UAAMiF,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpG,OAAA;UAAMiF,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpG,OAAA;MAAKiF,KAAK,EAAE;QACVoB,IAAI,EAAE,CAAC;QACPhB,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEAxF,OAAA;QAAKiF,KAAK,EAAE;UACVoB,IAAI,EAAE,CAAC;UACPlB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBoB,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,gBAEAxF,OAAA;UAAKiF,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBE,GAAG,EAAE;UACP,CAAE;UAAAL,QAAA,gBAEAxF,OAAA;YACE2G,OAAO,EAAEjE,WAAY;YACrBuC,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBR,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBS,MAAM,EAAE,SAAS;cACjBF,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETpG,OAAA;YACE2G,OAAO,EAAEA,CAAA;cAAA,IAAAE,sBAAA;cAAA,QAAAA,sBAAA,GAAMtE,YAAY,CAACC,OAAO,cAAAqE,sBAAA,uBAApBA,sBAAA,CAAsBpE,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CwC,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBR,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBS,MAAM,EAAE,SAAS;cACjBF,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETpG,OAAA;YACE2G,OAAO,EAAEA,CAAA,KAAMxG,MAAM,IAAIwC,aAAa,CAAC,CAAE;YACzCa,QAAQ,EAAE,CAACrD,MAAO;YAClB8E,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBR,eAAe,EAAEhF,MAAM,GAAG,SAAS,GAAG,SAAS;cAC/CyF,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBS,MAAM,EAAEtG,MAAM,GAAG,SAAS,GAAG,aAAa;cAC1CoG,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETpG,OAAA;YAAKiF,KAAK,EAAE;cAAEc,KAAK,EAAE,KAAK;cAAEb,MAAM,EAAE,MAAM;cAAEC,eAAe,EAAE;YAAO;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG7EpG,OAAA;YAAKiF,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEK,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAM,CAAE;YAAAL,QAAA,gBAChExF,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAE;cAC7DoC,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA;cAAMiF,KAAK,EAAE;gBAAEW,KAAK,EAAE,MAAM;gBAAEW,QAAQ,EAAE,MAAM;gBAAEO,QAAQ,EAAE,MAAM;gBAAER,SAAS,EAAE;cAAS,CAAE;cAAAd,QAAA,GACrFuB,SAAS,EAAC,GACb;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpG,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAE;cAC9DoC,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,GAAG,CAAE;cACjCqC,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpG,OAAA;YAAKiF,KAAK,EAAE;cAAEc,KAAK,EAAE,KAAK;cAAEb,MAAM,EAAE,MAAM;cAAEC,eAAe,EAAE;YAAO;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG7EpG,OAAA;YAAKiF,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEK,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAM,CAAE;YAAAL,QAAA,gBAChExF,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAM5C,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,CAAE;cAC5DoC,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA;cAAMiF,KAAK,EAAE;gBAAEW,KAAK,EAAE,MAAM;gBAAEW,QAAQ,EAAE,MAAM;gBAAEO,QAAQ,EAAE,MAAM;gBAAER,SAAS,EAAE;cAAS,CAAE;cAAAd,QAAA,GACrFwB,QAAQ,EAAC,MACZ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpG,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAM5C,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,IAAI,GAAG,CAAE;cACtDoC,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpG,OAAA;YAAKiF,KAAK,EAAE;cAAEc,KAAK,EAAE,KAAK;cAAEb,MAAM,EAAE,MAAM;cAAEC,eAAe,EAAE;YAAO;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG7EpG,OAAA;YAAKiF,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEK,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAM,CAAE;YAAAL,QAAA,gBAChExF,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAAC,OAAO,CAAE;cACpCsB,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAEtB,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;gBAC7D+B,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAAC,UAAU,CAAE;cACvCsB,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAEtB,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;gBAChE+B,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAMhD,WAAW,CAAC,UAAU,CAAE;cACvCsB,KAAK,EAAE;gBACLU,OAAO,EAAE,SAAS;gBAClBR,eAAe,EAAEtB,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;gBAChE+B,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAE,SAAS;gBACjBF,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpG,OAAA;YAAKiF,KAAK,EAAE;cAAEoB,IAAI,EAAE;YAAE;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG/BpG,OAAA;YAAMiF,KAAK,EAAE;cAAEW,KAAK,EAAE,MAAM;cAAEW,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAC9CnF,SAAS,GAAG,QAAQ,GAAGF,MAAM,GAAG,KAAK,GAAG;UAAQ;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNpG,OAAA;UACEiH,GAAG,EAAE1E,YAAa;UAClB2E,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,SAAS;UAChBlC,KAAK,EAAE;YAAEI,OAAO,EAAE;UAAO,CAAE;UAC3B+B,QAAQ,EAAGlG,CAAC,IAAK;YACf,MAAML,IAAI,GAAGK,CAAC,CAACC,MAAM,CAACkG,KAAK,CAAC,CAAC,CAAC;YAC9B,IAAIxG,IAAI,EAAE;cACR;cACAmB,OAAO,CAACsF,GAAG,CAAC,QAAQ,EAAEzG,IAAI,CAAC0G,IAAI,CAAC;YAClC;UACF;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGFpG,OAAA;UAAKiF,KAAK,EAAE;YACVoB,IAAI,EAAE,CAAC;YACPhB,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpB8B,cAAc,EAAE,QAAQ;YACxB7B,OAAO,EAAE,MAAM;YACfe,QAAQ,EAAE;UACZ,CAAE;UAAAlB,QAAA,GACCjF,KAAK,iBACJP,OAAA;YAAKiF,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBe,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZC,KAAK,EAAE,MAAM;cACbxC,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,MAAM;cAChBqB,MAAM,EAAE;YACV,CAAE;YAAApC,QAAA,GAAC,gBACG,EAACjF,KAAK;UAAA;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,EAEA/F,SAAS,iBACRL,OAAA;YAAKiF,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBe,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXG,SAAS,EAAE,uBAAuB;cAClC1C,eAAe,EAAE,iBAAiB;cAClCS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBM,SAAS,EAAE,QAAQ;cACnBsB,MAAM,EAAE;YACV,CAAE;YAAApC,QAAA,gBACAxF,OAAA;cAAKiF,KAAK,EAAE;gBAAE6C,YAAY,EAAE;cAAO,CAAE;cAAAtC,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDpG,OAAA;cAAKiF,KAAK,EAAE;gBACVc,KAAK,EAAE,MAAM;gBACbb,MAAM,EAAE,MAAM;gBACd0B,MAAM,EAAE,gBAAgB;gBACxBmB,SAAS,EAAE,mBAAmB;gBAC9B/B,YAAY,EAAE,KAAK;gBACnBgC,SAAS,EAAE,yBAAyB;gBACpCC,MAAM,EAAE;cACV;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAjG,MAAM,gBACLH,OAAA,CAACF,UAAU;YAACK,MAAM,EAAEA,MAAO;YAACM,aAAa,EAAEA;UAAc;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5DpG,OAAA;YAAKiF,KAAK,EAAE;cACVqB,SAAS,EAAE,QAAQ;cACnBV,KAAK,EAAE,MAAM;cACbW,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,gBACAxF,OAAA;cAAKiF,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,MAAM;gBAAEuB,YAAY,EAAE;cAAO,CAAE;cAAAtC,QAAA,EAAC;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEpG,OAAA;cAAAwF,QAAA,EAAK;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpG,OAAA;QAAKiF,KAAK,EAAE;UACVc,KAAK,EAAE,OAAO;UACdZ,eAAe,EAAE,SAAS;UAC1B+C,UAAU,EAAE,gBAAgB;UAC5B7C,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEAxF,OAAA;UAAKiF,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNpG,OAAA;UAAKiF,KAAK,EAAE;YACVoB,IAAI,EAAE,CAAC;YACPd,QAAQ,EAAE,MAAM;YAChBI,OAAO,EAAE;UACX,CAAE;UAAAH,QAAA,eACAxF,OAAA,CAACH,UAAU;YAACsI,QAAQ,EAAExH,YAAa;YAACN,SAAS,EAAEA;UAAU;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpG,OAAA;MAAKiF,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,MAAM;QACvB4C,SAAS,EAAE,gBAAgB;QAC3B1C,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbW,QAAQ,EAAE;MACZ,CAAE;MAAAf,QAAA,gBACAxF,OAAA;QAAAwF,QAAA,EAAM;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACfpG,OAAA;QAAKiF,KAAK,EAAE;UAAEoB,IAAI,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BpG,OAAA;QAAAwF,QAAA,EAAM;MAAe;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNpG,OAAA;MAAOoI,GAAG;MAAA5C,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAClG,EAAA,CA9iBQD,GAAG;AAAAoI,EAAA,GAAHpI,GAAG;AAgjBZ,eAAeA,GAAG;;AAElB;AACA,MAAMqI,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOrF,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMsF,YAAY,GAAGtF,QAAQ,CAACwB,aAAa,CAAC,OAAO,CAAC;EACpD8D,YAAY,CAACC,WAAW,GAAGF,YAAY;EACvCrF,QAAQ,CAACwF,IAAI,CAACC,WAAW,CAACH,YAAY,CAAC;AACzC;AAAC,IAAAF,EAAA;AAAAM,YAAA,CAAAN,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}