{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n  const [showParams, setShowParams] = useState(false);\n  const containerRef = useRef(null);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n  const handleImageError = e => {\n    console.error('图像加载失败:', e);\n    console.error('图像源:', e.target.src);\n    if (result && result.enhanced_image) {\n      console.error('Base64数据长度:', result.enhanced_image.length);\n      console.error('Base64数据前100字符:', result.enhanced_image.substring(0, 100));\n    }\n    setImageError(true);\n  };\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n  const handleMouseDown = e => {\n    if (viewMode === 'split') {\n      setIsDragging(true);\n      e.preventDefault();\n    }\n  };\n  const handleMouseMove = e => {\n    if (isDragging && containerRef.current && viewMode === 'split') {\n      const rect = containerRef.current.getBoundingClientRect();\n      const newPosition = (e.clientX - rect.left) / rect.width * 100;\n      setSplitPosition(Math.max(0, Math.min(100, newPosition)));\n    }\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n  if (!result) {\n    return null;\n  }\n  if (imageError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        color: '#d32f2f',\n        fontSize: '16px',\n        padding: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '48px',\n          marginBottom: '20px'\n        },\n        children: \"\\u274C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => window.location.reload(),\n        style: {\n          marginTop: '15px',\n          padding: '8px 16px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '10px',\n        right: '10px',\n        zIndex: 10,\n        display: 'flex',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setViewMode('split'),\n        style: {\n          padding: '6px 12px',\n          backgroundColor: viewMode === 'split' ? '#4a90e2' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '12px'\n        },\n        children: \"\\u5206\\u5C4F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setViewMode('original'),\n        style: {\n          padding: '6px 12px',\n          backgroundColor: viewMode === 'original' ? '#4a90e2' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '12px'\n        },\n        children: \"\\u539F\\u56FE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setViewMode('enhanced'),\n        style: {\n          padding: '6px 12px',\n          backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '12px'\n        },\n        children: \"\\u589E\\u5F3A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#28a745',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '12px'\n        },\n        children: \"\\u4E0B\\u8F7D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowParams(!showParams),\n        style: {\n          padding: '6px 12px',\n          backgroundColor: showParams ? '#4a90e2' : '#6c757d',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '12px'\n        },\n        children: \"\\u53C2\\u6570\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), showParams && result.parameters && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '50px',\n        right: '10px',\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '15px',\n        borderRadius: '8px',\n        fontSize: '12px',\n        zIndex: 10,\n        minWidth: '200px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 10px 0',\n          color: '#4a90e2'\n        },\n        children: \"\\u5904\\u7406\\u53C2\\u6570\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), Object.entries(result.parameters).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '5px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [key, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 15\n        }, this), \" \", typeof value === 'boolean' ? value ? '是' : '否' : value]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        position: 'relative',\n        overflow: 'hidden',\n        cursor: viewMode === 'split' && isDragging ? 'col-resize' : 'default'\n      },\n      children: [viewMode === 'split' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            left: 0,\n            top: 0,\n            width: `${splitPosition}%`,\n            height: '100%',\n            overflow: 'hidden'\n          },\n          children: [originalImage && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: originalImage,\n            alt: \"\\u539F\\u56FE\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain'\n            },\n            onLoad: handleImageLoad,\n            onError: handleImageError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '10px',\n              left: '10px',\n              backgroundColor: 'rgba(0,0,0,0.7)',\n              color: 'white',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              fontSize: '12px'\n            },\n            children: \"\\u539F\\u56FE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            left: `${splitPosition}%`,\n            top: 0,\n            width: `${100 - splitPosition}%`,\n            height: '100%',\n            overflow: 'hidden'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${result.enhanced_image}`,\n            alt: \"\\u589E\\u5F3A\\u56FE\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain'\n            },\n            onLoad: handleImageLoad,\n            onError: handleImageError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '10px',\n              right: '10px',\n              backgroundColor: 'rgba(0,0,0,0.7)',\n              color: 'white',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              fontSize: '12px'\n            },\n            children: \"\\u589E\\u5F3A\\u56FE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            left: `${splitPosition}%`,\n            top: 0,\n            width: '2px',\n            height: '100%',\n            backgroundColor: '#4a90e2',\n            cursor: 'col-resize',\n            transform: 'translateX(-1px)'\n          },\n          onMouseDown: handleMouseDown,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              width: '20px',\n              height: '40px',\n              backgroundColor: '#4a90e2',\n              borderRadius: '10px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '12px'\n            },\n            children: \"\\u27F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), viewMode === 'original' && originalImage && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: originalImage,\n        alt: \"\\u539F\\u56FE\",\n        style: {\n          width: '100%',\n          height: '100%',\n          objectFit: 'contain'\n        },\n        onLoad: handleImageLoad,\n        onError: handleImageError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this), viewMode === 'enhanced' && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: `data:image/png;base64,${result.enhanced_image}`,\n        alt: \"\\u589E\\u5F3A\\u56FE\",\n        style: {\n          width: '100%',\n          height: '100%',\n          objectFit: 'contain'\n        },\n        onLoad: handleImageLoad,\n        onError: handleImageError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), !imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)',\n        color: '#ccc',\n        fontSize: '14px'\n      },\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"lJBZc/NMlk4Ltdrwg3A1dmxWzZ0=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "showParams", "setShowParams", "containerRef", "handleImageLoad", "handleImageError", "e", "console", "error", "target", "src", "enhanced_image", "length", "substring", "downloadImage", "link", "document", "createElement", "href", "download", "Date", "now", "click", "handleMouseDown", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "newPosition", "clientX", "left", "width", "Math", "max", "min", "handleMouseUp", "addEventListener", "removeEventListener", "style", "textAlign", "color", "fontSize", "padding", "children", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "marginTop", "backgroundColor", "border", "borderRadius", "cursor", "height", "position", "top", "right", "zIndex", "display", "gap", "parameters", "min<PERSON><PERSON><PERSON>", "margin", "Object", "entries", "map", "key", "value", "ref", "overflow", "alt", "objectFit", "onLoad", "onError", "bottom", "transform", "onMouseDown", "alignItems", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n  const [showParams, setShowParams] = useState(false);\n  const containerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n\n  const handleImageError = (e) => {\n    console.error('图像加载失败:', e);\n    console.error('图像源:', e.target.src);\n    if (result && result.enhanced_image) {\n      console.error('Base64数据长度:', result.enhanced_image.length);\n      console.error('Base64数据前100字符:', result.enhanced_image.substring(0, 100));\n    }\n    setImageError(true);\n  };\n\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n\n  const handleMouseDown = (e) => {\n    if (viewMode === 'split') {\n      setIsDragging(true);\n      e.preventDefault();\n    }\n  };\n\n  const handleMouseMove = (e) => {\n    if (isDragging && containerRef.current && viewMode === 'split') {\n      const rect = containerRef.current.getBoundingClientRect();\n      const newPosition = ((e.clientX - rect.left) / rect.width) * 100;\n      setSplitPosition(Math.max(0, Math.min(100, newPosition)));\n    }\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  if (!result) {\n    return null;\n  }\n\n  if (imageError) {\n    return (\n      <div style={{\n        textAlign: 'center',\n        color: '#d32f2f',\n        fontSize: '16px',\n        padding: '20px'\n      }}>\n        <div style={{ fontSize: '48px', marginBottom: '20px' }}>❌</div>\n        <div>图像加载失败</div>\n        <button\n          onClick={() => window.location.reload()}\n          style={{\n            marginTop: '15px',\n            padding: '8px 16px',\n            backgroundColor: '#4a90e2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          }}\n        >\n          重新加载\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ width: '100%', height: '100%', position: 'relative' }}>\n      {/* 工具栏 */}\n      <div style={{\n        position: 'absolute',\n        top: '10px',\n        right: '10px',\n        zIndex: 10,\n        display: 'flex',\n        gap: '8px'\n      }}>\n        <button\n          onClick={() => setViewMode('split')}\n          style={{\n            padding: '6px 12px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          }}\n        >\n          分屏\n        </button>\n        <button\n          onClick={() => setViewMode('original')}\n          style={{\n            padding: '6px 12px',\n            backgroundColor: viewMode === 'original' ? '#4a90e2' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          }}\n        >\n          原图\n        </button>\n        <button\n          onClick={() => setViewMode('enhanced')}\n          style={{\n            padding: '6px 12px',\n            backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          }}\n        >\n          增强\n        </button>\n        <button\n          onClick={downloadImage}\n          style={{\n            padding: '6px 12px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          }}\n        >\n          下载\n        </button>\n        <button\n          onClick={() => setShowParams(!showParams)}\n          style={{\n            padding: '6px 12px',\n            backgroundColor: showParams ? '#4a90e2' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          }}\n        >\n          参数\n        </button>\n      </div>\n\n      {/* 参数信息面板 */}\n      {showParams && result.parameters && (\n        <div style={{\n          position: 'absolute',\n          top: '50px',\n          right: '10px',\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          color: 'white',\n          padding: '15px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          zIndex: 10,\n          minWidth: '200px'\n        }}>\n          <h4 style={{ margin: '0 0 10px 0', color: '#4a90e2' }}>处理参数</h4>\n          {Object.entries(result.parameters).map(([key, value]) => (\n            <div key={key} style={{ marginBottom: '5px' }}>\n              <strong>{key}:</strong> {typeof value === 'boolean' ? (value ? '是' : '否') : value}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* 图像显示区域 */}\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          position: 'relative',\n          overflow: 'hidden',\n          cursor: viewMode === 'split' && isDragging ? 'col-resize' : 'default'\n        }}\n      >\n        {viewMode === 'split' && (\n          <>\n            {/* 原图 */}\n            <div style={{\n              position: 'absolute',\n              left: 0,\n              top: 0,\n              width: `${splitPosition}%`,\n              height: '100%',\n              overflow: 'hidden'\n            }}>\n              {originalImage && (\n                <img\n                  src={originalImage}\n                  alt=\"原图\"\n                  style={{\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'contain'\n                  }}\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                />\n              )}\n              <div style={{\n                position: 'absolute',\n                bottom: '10px',\n                left: '10px',\n                backgroundColor: 'rgba(0,0,0,0.7)',\n                color: 'white',\n                padding: '4px 8px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              }}>\n                原图\n              </div>\n            </div>\n\n            {/* 增强图 */}\n            <div style={{\n              position: 'absolute',\n              left: `${splitPosition}%`,\n              top: 0,\n              width: `${100 - splitPosition}%`,\n              height: '100%',\n              overflow: 'hidden'\n            }}>\n              <img\n                src={`data:image/png;base64,${result.enhanced_image}`}\n                alt=\"增强图\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'contain'\n                }}\n                onLoad={handleImageLoad}\n                onError={handleImageError}\n              />\n              <div style={{\n                position: 'absolute',\n                bottom: '10px',\n                right: '10px',\n                backgroundColor: 'rgba(0,0,0,0.7)',\n                color: 'white',\n                padding: '4px 8px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              }}>\n                增强图\n              </div>\n            </div>\n\n            {/* 分割线 */}\n            <div\n              style={{\n                position: 'absolute',\n                left: `${splitPosition}%`,\n                top: 0,\n                width: '2px',\n                height: '100%',\n                backgroundColor: '#4a90e2',\n                cursor: 'col-resize',\n                transform: 'translateX(-1px)'\n              }}\n              onMouseDown={handleMouseDown}\n            >\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                width: '20px',\n                height: '40px',\n                backgroundColor: '#4a90e2',\n                borderRadius: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontSize: '12px'\n              }}>\n                ⟷\n              </div>\n            </div>\n          </>\n        )}\n\n        {viewMode === 'original' && originalImage && (\n          <img\n            src={originalImage}\n            alt=\"原图\"\n            style={{\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain'\n            }}\n            onLoad={handleImageLoad}\n            onError={handleImageError}\n          />\n        )}\n\n        {viewMode === 'enhanced' && (\n          <img\n            src={`data:image/png;base64,${result.enhanced_image}`}\n            alt=\"增强图\"\n            style={{\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain'\n            }}\n            onLoad={handleImageLoad}\n            onError={handleImageError}\n          />\n        )}\n      </div>\n\n      {/* 加载指示器 */}\n      {!imageLoaded && (\n        <div style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#ccc',\n          fontSize: '14px'\n        }}>\n          加载中...\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMuB,YAAY,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMuB,eAAe,GAAGA,CAAA,KAAM;IAC5BZ,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMa,gBAAgB,GAAIC,CAAC,IAAK;IAC9BC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEF,CAAC,CAAC;IAC3BC,OAAO,CAACC,KAAK,CAAC,MAAM,EAAEF,CAAC,CAACG,MAAM,CAACC,GAAG,CAAC;IACnC,IAAItB,MAAM,IAAIA,MAAM,CAACuB,cAAc,EAAE;MACnCJ,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEpB,MAAM,CAACuB,cAAc,CAACC,MAAM,CAAC;MAC1DL,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAEpB,MAAM,CAACuB,cAAc,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC3E;IACAnB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI1B,MAAM,IAAIA,MAAM,CAACuB,cAAc,EAAE;MACnC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG,yBAAyB9B,MAAM,CAACuB,cAAc,EAAE;MAC5DI,IAAI,CAACI,QAAQ,GAAG,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC5CN,IAAI,CAACO,KAAK,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMC,eAAe,GAAIjB,CAAC,IAAK;IAC7B,IAAIP,QAAQ,KAAK,OAAO,EAAE;MACxBD,aAAa,CAAC,IAAI,CAAC;MACnBQ,CAAC,CAACkB,cAAc,CAAC,CAAC;IACpB;EACF,CAAC;EAED,MAAMC,eAAe,GAAInB,CAAC,IAAK;IAC7B,IAAIT,UAAU,IAAIM,YAAY,CAACuB,OAAO,IAAI3B,QAAQ,KAAK,OAAO,EAAE;MAC9D,MAAM4B,IAAI,GAAGxB,YAAY,CAACuB,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,WAAW,GAAI,CAACvB,CAAC,CAACwB,OAAO,GAAGH,IAAI,CAACI,IAAI,IAAIJ,IAAI,CAACK,KAAK,GAAI,GAAG;MAChEpC,gBAAgB,CAACqC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEN,WAAW,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1BtC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACdmB,QAAQ,CAACqB,gBAAgB,CAAC,WAAW,EAAEZ,eAAe,CAAC;MACvDT,QAAQ,CAACqB,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MACnD,OAAO,MAAM;QACXpB,QAAQ,CAACsB,mBAAmB,CAAC,WAAW,EAAEb,eAAe,CAAC;QAC1DT,QAAQ,CAACsB,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACT,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,IAAIK,UAAU,EAAE;IACd,oBACET,OAAA;MAAKuD,KAAK,EAAE;QACVC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE;MACX,CAAE;MAAAC,QAAA,gBACA5D,OAAA;QAAKuD,KAAK,EAAE;UAAEG,QAAQ,EAAE,MAAM;UAAEG,YAAY,EAAE;QAAO,CAAE;QAAAD,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/DjE,OAAA;QAAA4D,QAAA,EAAK;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjBjE,OAAA;QACEkE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCd,KAAK,EAAE;UACLe,SAAS,EAAE,MAAM;UACjBX,OAAO,EAAE,UAAU;UACnBY,eAAe,EAAE,SAAS;UAC1Bd,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAd,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjE,OAAA;IAAKuD,KAAK,EAAE;MAAEP,KAAK,EAAE,MAAM;MAAE2B,MAAM,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAW,CAAE;IAAAhB,QAAA,gBAElE5D,OAAA;MAAKuD,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,GAAG,EAAE;MACP,CAAE;MAAArB,QAAA,gBACA5D,OAAA;QACEkE,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,OAAO,CAAE;QACpCuC,KAAK,EAAE;UACLI,OAAO,EAAE,UAAU;UACnBY,eAAe,EAAExD,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;UAC7D0C,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBhB,QAAQ,EAAE;QACZ,CAAE;QAAAE,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA;QACEkE,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,UAAU,CAAE;QACvCuC,KAAK,EAAE;UACLI,OAAO,EAAE,UAAU;UACnBY,eAAe,EAAExD,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;UAChE0C,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBhB,QAAQ,EAAE;QACZ,CAAE;QAAAE,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA;QACEkE,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,UAAU,CAAE;QACvCuC,KAAK,EAAE;UACLI,OAAO,EAAE,UAAU;UACnBY,eAAe,EAAExD,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;UAChE0C,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBhB,QAAQ,EAAE;QACZ,CAAE;QAAAE,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA;QACEkE,OAAO,EAAEpC,aAAc;QACvByB,KAAK,EAAE;UACLI,OAAO,EAAE,UAAU;UACnBY,eAAe,EAAE,SAAS;UAC1Bd,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBhB,QAAQ,EAAE;QACZ,CAAE;QAAAE,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA;QACEkE,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAACD,UAAU,CAAE;QAC1CsC,KAAK,EAAE;UACLI,OAAO,EAAE,UAAU;UACnBY,eAAe,EAAEtD,UAAU,GAAG,SAAS,GAAG,SAAS;UACnDwC,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBhB,QAAQ,EAAE;QACZ,CAAE;QAAAE,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLhD,UAAU,IAAIb,MAAM,CAAC8E,UAAU,iBAC9BlF,OAAA;MAAKuD,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbP,eAAe,EAAE,iBAAiB;QAClCd,KAAK,EAAE,OAAO;QACdE,OAAO,EAAE,MAAM;QACfc,YAAY,EAAE,KAAK;QACnBf,QAAQ,EAAE,MAAM;QAChBqB,MAAM,EAAE,EAAE;QACVI,QAAQ,EAAE;MACZ,CAAE;MAAAvB,QAAA,gBACA5D,OAAA;QAAIuD,KAAK,EAAE;UAAE6B,MAAM,EAAE,YAAY;UAAE3B,KAAK,EAAE;QAAU,CAAE;QAAAG,QAAA,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/DoB,MAAM,CAACC,OAAO,CAAClF,MAAM,CAAC8E,UAAU,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBAClDzF,OAAA;QAAeuD,KAAK,EAAE;UAAEM,YAAY,EAAE;QAAM,CAAE;QAAAD,QAAA,gBAC5C5D,OAAA;UAAA4D,QAAA,GAAS4B,GAAG,EAAC,GAAC;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC,OAAOwB,KAAK,KAAK,SAAS,GAAIA,KAAK,GAAG,GAAG,GAAG,GAAG,GAAIA,KAAK;MAAA,GADzED,GAAG;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDjE,OAAA;MACE0F,GAAG,EAAEvE,YAAa;MAClBoC,KAAK,EAAE;QACLP,KAAK,EAAE,MAAM;QACb2B,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,UAAU;QACpBe,QAAQ,EAAE,QAAQ;QAClBjB,MAAM,EAAE3D,QAAQ,KAAK,OAAO,IAAIF,UAAU,GAAG,YAAY,GAAG;MAC9D,CAAE;MAAA+C,QAAA,GAED7C,QAAQ,KAAK,OAAO,iBACnBf,OAAA,CAAAE,SAAA;QAAA0D,QAAA,gBAEE5D,OAAA;UAAKuD,KAAK,EAAE;YACVqB,QAAQ,EAAE,UAAU;YACpB7B,IAAI,EAAE,CAAC;YACP8B,GAAG,EAAE,CAAC;YACN7B,KAAK,EAAE,GAAGrC,aAAa,GAAG;YAC1BgE,MAAM,EAAE,MAAM;YACdgB,QAAQ,EAAE;UACZ,CAAE;UAAA/B,QAAA,GACCvD,aAAa,iBACZL,OAAA;YACE0B,GAAG,EAAErB,aAAc;YACnBuF,GAAG,EAAC,cAAI;YACRrC,KAAK,EAAE;cACLP,KAAK,EAAE,MAAM;cACb2B,MAAM,EAAE,MAAM;cACdkB,SAAS,EAAE;YACb,CAAE;YACFC,MAAM,EAAE1E,eAAgB;YACxB2E,OAAO,EAAE1E;UAAiB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACF,eACDjE,OAAA;YAAKuD,KAAK,EAAE;cACVqB,QAAQ,EAAE,UAAU;cACpBoB,MAAM,EAAE,MAAM;cACdjD,IAAI,EAAE,MAAM;cACZwB,eAAe,EAAE,iBAAiB;cAClCd,KAAK,EAAE,OAAO;cACdE,OAAO,EAAE,SAAS;cAClBc,YAAY,EAAE,KAAK;cACnBf,QAAQ,EAAE;YACZ,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjE,OAAA;UAAKuD,KAAK,EAAE;YACVqB,QAAQ,EAAE,UAAU;YACpB7B,IAAI,EAAE,GAAGpC,aAAa,GAAG;YACzBkE,GAAG,EAAE,CAAC;YACN7B,KAAK,EAAE,GAAG,GAAG,GAAGrC,aAAa,GAAG;YAChCgE,MAAM,EAAE,MAAM;YACdgB,QAAQ,EAAE;UACZ,CAAE;UAAA/B,QAAA,gBACA5D,OAAA;YACE0B,GAAG,EAAE,yBAAyBtB,MAAM,CAACuB,cAAc,EAAG;YACtDiE,GAAG,EAAC,oBAAK;YACTrC,KAAK,EAAE;cACLP,KAAK,EAAE,MAAM;cACb2B,MAAM,EAAE,MAAM;cACdkB,SAAS,EAAE;YACb,CAAE;YACFC,MAAM,EAAE1E,eAAgB;YACxB2E,OAAO,EAAE1E;UAAiB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACFjE,OAAA;YAAKuD,KAAK,EAAE;cACVqB,QAAQ,EAAE,UAAU;cACpBoB,MAAM,EAAE,MAAM;cACdlB,KAAK,EAAE,MAAM;cACbP,eAAe,EAAE,iBAAiB;cAClCd,KAAK,EAAE,OAAO;cACdE,OAAO,EAAE,SAAS;cAClBc,YAAY,EAAE,KAAK;cACnBf,QAAQ,EAAE;YACZ,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjE,OAAA;UACEuD,KAAK,EAAE;YACLqB,QAAQ,EAAE,UAAU;YACpB7B,IAAI,EAAE,GAAGpC,aAAa,GAAG;YACzBkE,GAAG,EAAE,CAAC;YACN7B,KAAK,EAAE,KAAK;YACZ2B,MAAM,EAAE,MAAM;YACdJ,eAAe,EAAE,SAAS;YAC1BG,MAAM,EAAE,YAAY;YACpBuB,SAAS,EAAE;UACb,CAAE;UACFC,WAAW,EAAE3D,eAAgB;UAAAqB,QAAA,eAE7B5D,OAAA;YAAKuD,KAAK,EAAE;cACVqB,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,KAAK;cACV9B,IAAI,EAAE,KAAK;cACXkD,SAAS,EAAE,uBAAuB;cAClCjD,KAAK,EAAE,MAAM;cACb2B,MAAM,EAAE,MAAM;cACdJ,eAAe,EAAE,SAAS;cAC1BE,YAAY,EAAE,MAAM;cACpBO,OAAO,EAAE,MAAM;cACfmB,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxB3C,KAAK,EAAE,OAAO;cACdC,QAAQ,EAAE;YACZ,CAAE;YAAAE,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH,EAEAlD,QAAQ,KAAK,UAAU,IAAIV,aAAa,iBACvCL,OAAA;QACE0B,GAAG,EAAErB,aAAc;QACnBuF,GAAG,EAAC,cAAI;QACRrC,KAAK,EAAE;UACLP,KAAK,EAAE,MAAM;UACb2B,MAAM,EAAE,MAAM;UACdkB,SAAS,EAAE;QACb,CAAE;QACFC,MAAM,EAAE1E,eAAgB;QACxB2E,OAAO,EAAE1E;MAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACF,EAEAlD,QAAQ,KAAK,UAAU,iBACtBf,OAAA;QACE0B,GAAG,EAAE,yBAAyBtB,MAAM,CAACuB,cAAc,EAAG;QACtDiE,GAAG,EAAC,oBAAK;QACTrC,KAAK,EAAE;UACLP,KAAK,EAAE,MAAM;UACb2B,MAAM,EAAE,MAAM;UACdkB,SAAS,EAAE;QACb,CAAE;QACFC,MAAM,EAAE1E,eAAgB;QACxB2E,OAAO,EAAE1E;MAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAC1D,WAAW,iBACXP,OAAA;MAAKuD,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,KAAK;QACV9B,IAAI,EAAE,KAAK;QACXkD,SAAS,EAAE,uBAAuB;QAClCxC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAE,QAAA,EAAC;IAEH;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA1WIH,UAAU;AAAAkG,EAAA,GAAVlG,UAAU;AA4WhB,eAAeA,UAAU;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}