{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import UploadForm from'./UploadForm';import ResultView from'./ResultView';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function App(){const[result,setResult]=useState(null);const[isLoading,setIsLoading]=useState(false);const[error,setError]=useState(null);const[originalImage,setOriginalImage]=useState(null);const[showMenu,setShowMenu]=useState(null);const[imageInfo,setImageInfo]=useState(null);const[processingTime,setProcessingTime]=useState(null);const[zoomLevel,setZoomLevel]=useState(100);const fileInputRef=useRef(null);// 快捷键支持\nuseEffect(()=>{const handleKeyDown=e=>{// Ctrl+O 打开文件\nif(e.ctrlKey&&e.key==='o'){e.preventDefault();if(fileInputRef.current){fileInputRef.current.click();}}// Ctrl+R 重置\nif(e.ctrlKey&&e.key==='r'){e.preventDefault();handleReset();}// Ctrl+S 保存（如果有结果）\nif(e.ctrlKey&&e.key==='s'&&result){e.preventDefault();downloadImage();}// Ctrl+= 放大\nif(e.ctrlKey&&e.key==='='){e.preventDefault();setZoomLevel(prev=>Math.min(prev+25,400));}// Ctrl+- 缩小\nif(e.ctrlKey&&e.key==='-'){e.preventDefault();setZoomLevel(prev=>Math.max(prev-25,25));}// Ctrl+0 重置缩放\nif(e.ctrlKey&&e.key==='0'){e.preventDefault();setZoomLevel(100);}};document.addEventListener('keydown',handleKeyDown);return()=>document.removeEventListener('keydown',handleKeyDown);},[result]);// 菜单功能\nconst menuItems={file:[{label:'打开图像 (Ctrl+O)',action:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();}},{label:'保存结果 (Ctrl+S)',action:downloadImage,disabled:!result},{label:'重置 (Ctrl+R)',action:handleReset}],edit:[{label:'撤销',action:()=>{},disabled:true},{label:'重做',action:()=>{},disabled:true}],view:[{label:'放大 (Ctrl+=)',action:()=>setZoomLevel(prev=>Math.min(prev+25,400))},{label:'缩小 (Ctrl+-)',action:()=>setZoomLevel(prev=>Math.max(prev-25,25))},{label:'实际大小 (Ctrl+0)',action:()=>setZoomLevel(100)}],tools:[{label:'批量处理',action:()=>{},disabled:true},{label:'设置',action:()=>{},disabled:true}],help:[{label:'快捷键',action:()=>alert('快捷键:\\nCtrl+O: 打开文件\\nCtrl+S: 保存结果\\nCtrl+R: 重置\\nCtrl+=: 放大\\nCtrl+-: 缩小\\nCtrl+0: 实际大小')},{label:'关于',action:()=>alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术')}]};const handleUpload=async formData=>{const startTime=Date.now();setIsLoading(true);setError(null);// 保存原始图像用于对比和获取图像信息\nconst file=formData.get('file');if(file){const reader=new FileReader();reader.onload=e=>{setOriginalImage(e.target.result);// 获取图像信息\nconst img=new Image();img.onload=()=>{setImageInfo({name:file.name,size:(file.size/1024/1024).toFixed(2)+' MB',dimensions:`${img.width} × ${img.height}`,type:file.type});};img.src=e.target.result;};reader.readAsDataURL(file);}try{const response=await fetch('http://localhost:8001/enhance/',{method:'POST',body:formData});if(!response.ok){const errorData=await response.json();throw new Error(errorData.detail||'处理失败');}const data=await response.json();const endTime=Date.now();setProcessingTime(((endTime-startTime)/1000).toFixed(1));setResult(data);}catch(err){console.error('上传失败:',err);setError(err.message||'网络错误，请检查后端服务是否正常运行');}finally{setIsLoading(false);}};const handleReset=()=>{setResult(null);setError(null);setOriginalImage(null);setImageInfo(null);setProcessingTime(null);setZoomLevel(100);};const downloadImage=()=>{if(result){const link=document.createElement('a');link.href=`http://localhost:8001/result/${result.enhanced_url}`;link.download=`enhanced_${result.filename}`;document.body.appendChild(link);link.click();document.body.removeChild(link);}};const handleMenuClick=menuKey=>{setShowMenu(showMenu===menuKey?null:menuKey);};const handleMenuItemClick=action=>{action();setShowMenu(null);};return/*#__PURE__*/_jsxs(\"div\",{style:{height:'100vh',backgroundColor:'#2b2b2b',fontFamily:'-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',display:'flex',flexDirection:'column',overflow:'hidden'},children:[/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\"image/*\",style:{display:'none'},onChange:e=>{const file=e.target.files[0];if(file){const formData=new FormData();formData.append('file',file);formData.append('params',JSON.stringify({scale:4,use_realesrgan:true,sharpening:0.0,denoising:0,saturation:1.0,contrast:1.0,brightness:0,beauty:0.0}));handleUpload(formData);}}}),/*#__PURE__*/_jsxs(\"div\",{style:{height:'60px',backgroundColor:'#3c3c3c',borderBottom:'1px solid #555',display:'flex',alignItems:'center',padding:'0 20px',color:'#fff',position:'relative'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px',marginRight:'20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:'#ff5f57'}}),/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:'#ffbd2e'}}),/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:'#28ca42'}})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,textAlign:'center',fontSize:'14px',fontWeight:'500',color:'#e0e0e0'},children:\"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',gap:'20px',fontSize:'13px'},children:Object.keys(menuItems).map(menuKey=>/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(\"span\",{style:{cursor:'pointer',padding:'5px 10px',borderRadius:'4px',backgroundColor:showMenu===menuKey?'#4a90e2':'transparent'},onClick:()=>handleMenuClick(menuKey),children:menuKey==='file'?'文件':menuKey==='edit'?'编辑':menuKey==='view'?'视图':menuKey==='tools'?'工具':'帮助'}),showMenu===menuKey&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'100%',left:0,backgroundColor:'#3c3c3c',border:'1px solid #555',borderRadius:'4px',minWidth:'150px',zIndex:1000,boxShadow:'0 4px 8px rgba(0,0,0,0.3)'},children:menuItems[menuKey].map((item,index)=>/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 12px',cursor:item.disabled?'not-allowed':'pointer',color:item.disabled?'#888':'#e0e0e0',fontSize:'12px',borderBottom:index<menuItems[menuKey].length-1?'1px solid #555':'none'},onClick:()=>!item.disabled&&handleMenuItemClick(item.action),onMouseEnter:e=>{if(!item.disabled){e.target.style.backgroundColor='#4a90e2';}},onMouseLeave:e=>{e.target.style.backgroundColor='transparent';},children:item.label},index))})]},menuKey))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',overflow:'hidden'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,backgroundColor:'#1e1e1e',display:'flex',flexDirection:'column',position:'relative'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{height:'50px',backgroundColor:'#333',borderBottom:'1px solid #555',display:'flex',alignItems:'center',padding:'0 15px',gap:'10px'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleReset,style:{padding:'6px 12px',backgroundColor:'#4a90e2',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'12px'},children:\"\\u65B0\\u5EFA\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var _fileInputRef$current2;return(_fileInputRef$current2=fileInputRef.current)===null||_fileInputRef$current2===void 0?void 0:_fileInputRef$current2.click();},style:{padding:'6px 12px',backgroundColor:'#28a745',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'12px'},children:\"\\u6253\\u5F00\"}),result&&/*#__PURE__*/_jsx(\"button\",{onClick:downloadImage,style:{padding:'6px 12px',backgroundColor:'#17a2b8',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'12px'},children:\"\\u4FDD\\u5B58\"}),/*#__PURE__*/_jsx(\"div\",{style:{width:'1px',height:'20px',backgroundColor:'#555'}}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'5px'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setZoomLevel(prev=>Math.max(prev-25,25)),style:{padding:'4px 8px',backgroundColor:'#6c757d',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"-\"}),/*#__PURE__*/_jsxs(\"span\",{style:{color:'#ccc',fontSize:'11px',minWidth:'40px',textAlign:'center'},children:[zoomLevel,\"%\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setZoomLevel(prev=>Math.min(prev+25,400)),style:{padding:'4px 8px',backgroundColor:'#6c757d',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"+\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1}}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#ccc',fontSize:'12px'},children:result?'已处理':isLoading?'处理中...':'等待上传图像'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',alignItems:'center',justifyContent:'center',padding:'20px',position:'relative'},children:[error&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'20px',left:'20px',right:'20px',backgroundColor:'#d32f2f',color:'white',padding:'12px',borderRadius:'4px',fontSize:'14px',zIndex:10},children:[\"\\u9519\\u8BEF: \",error]}),isLoading&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',backgroundColor:'rgba(0,0,0,0.8)',color:'white',padding:'20px',borderRadius:'8px',textAlign:'center',zIndex:10},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'10px'},children:\"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"}),/*#__PURE__*/_jsx(\"div\",{style:{width:'30px',height:'30px',border:'3px solid #333',borderTop:'3px solid #4a90e2',borderRadius:'50%',animation:'spin 1s linear infinite',margin:'0 auto'}})]}),result?/*#__PURE__*/_jsx(\"div\",{style:{transform:`scale(${zoomLevel/100})`,transformOrigin:'center'},children:/*#__PURE__*/_jsx(ResultView,{result:result,originalImage:originalImage})}):/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',color:'#888',fontSize:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'48px',marginBottom:'20px'},children:\"\\uD83D\\uDCF7\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',marginTop:'10px',color:'#666'},children:\"\\u6216\\u6309 Ctrl+O \\u6253\\u5F00\\u6587\\u4EF6\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{width:'320px',backgroundColor:'#2d2d2d',borderLeft:'1px solid #555',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(\"div\",{style:{height:'50px',backgroundColor:'#333',borderBottom:'1px solid #555',display:'flex',alignItems:'center',padding:'0 15px',color:'#e0e0e0',fontSize:'14px',fontWeight:'500'},children:\"\\u53C2\\u6570\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,overflow:'auto',padding:'0'},children:/*#__PURE__*/_jsx(UploadForm,{onUpload:handleUpload,isLoading:isLoading})})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{height:'30px',backgroundColor:'#333',borderTop:'1px solid #555',display:'flex',alignItems:'center',padding:'0 15px',color:'#ccc',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"span\",{children:isLoading?'处理中...':'就绪'}),imageInfo&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'1px',height:'16px',backgroundColor:'#555',margin:'0 10px'}}),/*#__PURE__*/_jsx(\"span\",{children:imageInfo.name}),/*#__PURE__*/_jsx(\"div\",{style:{width:'1px',height:'16px',backgroundColor:'#555',margin:'0 10px'}}),/*#__PURE__*/_jsx(\"span\",{children:imageInfo.dimensions}),/*#__PURE__*/_jsx(\"div\",{style:{width:'1px',height:'16px',backgroundColor:'#555',margin:'0 10px'}}),/*#__PURE__*/_jsx(\"span\",{children:imageInfo.size})]}),processingTime&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'1px',height:'16px',backgroundColor:'#555',margin:'0 10px'}}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5904\\u7406\\u65F6\\u95F4: \",processingTime,\"s\"]})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1}}),/*#__PURE__*/_jsx(\"span\",{children:\"RealESRGAN v2.1\"})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,global:true,children:`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "UploadForm", "ResultView", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "App", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "showMenu", "setShowMenu", "imageInfo", "setImageInfo", "processingTime", "setProcessingTime", "zoomLevel", "setZoomLevel", "fileInputRef", "handleKeyDown", "e", "ctrl<PERSON>ey", "key", "preventDefault", "current", "click", "handleReset", "downloadImage", "prev", "Math", "min", "max", "document", "addEventListener", "removeEventListener", "menuItems", "file", "label", "action", "_fileInputRef$current", "disabled", "edit", "view", "tools", "help", "alert", "handleUpload", "formData", "startTime", "Date", "now", "get", "reader", "FileReader", "onload", "target", "img", "Image", "name", "size", "toFixed", "dimensions", "width", "height", "type", "src", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "endTime", "err", "console", "message", "link", "createElement", "href", "enhanced_url", "download", "filename", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handleMenuClick", "<PERSON><PERSON>ey", "handleMenuItemClick", "style", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "ref", "accept", "onChange", "files", "FormData", "append", "JSON", "stringify", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "borderBottom", "alignItems", "padding", "color", "position", "gap", "marginRight", "borderRadius", "flex", "textAlign", "fontSize", "fontWeight", "Object", "keys", "map", "cursor", "onClick", "top", "left", "border", "min<PERSON><PERSON><PERSON>", "zIndex", "boxShadow", "item", "index", "length", "onMouseEnter", "onMouseLeave", "_fileInputRef$current2", "justifyContent", "right", "transform", "marginBottom", "borderTop", "animation", "margin", "transform<PERSON><PERSON>in", "marginTop", "borderLeft", "onUpload", "global"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [\n      { label: '打开图像 (Ctrl+O)', action: () => fileInputRef.current?.click() },\n      { label: '保存结果 (Ctrl+S)', action: downloadImage, disabled: !result },\n      { label: '重置 (Ctrl+R)', action: handleReset }\n    ],\n    edit: [\n      { label: '撤销', action: () => {}, disabled: true },\n      { label: '重做', action: () => {}, disabled: true }\n    ],\n    view: [\n      { label: '放大 (Ctrl+=)', action: () => setZoomLevel(prev => Math.min(prev + 25, 400)) },\n      { label: '缩小 (Ctrl+-)', action: () => setZoomLevel(prev => Math.max(prev - 25, 25)) },\n      { label: '实际大小 (Ctrl+0)', action: () => setZoomLevel(100) }\n    ],\n    tools: [\n      { label: '批量处理', action: () => {}, disabled: true },\n      { label: '设置', action: () => {}, disabled: true }\n    ],\n    help: [\n      { label: '快捷键', action: () => alert('快捷键:\\nCtrl+O: 打开文件\\nCtrl+S: 保存结果\\nCtrl+R: 重置\\nCtrl+=: 放大\\nCtrl+-: 缩小\\nCtrl+0: 实际大小') },\n      { label: '关于', action: () => alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术') }\n    ]\n  };\n\n  const handleUpload = async (formData) => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比和获取图像信息\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setOriginalImage(e.target.result);\n        // 获取图像信息\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n  };\n\n  const downloadImage = () => {\n    if (result) {\n      const link = document.createElement('a');\n      link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n      link.download = `enhanced_${result.filename}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleMenuClick = (menuKey) => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n\n  const handleMenuItemClick = (action) => {\n    action();\n    setShowMenu(null);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 隐藏的文件输入 */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/*\"\n        style={{ display: 'none' }}\n        onChange={(e) => {\n          const file = e.target.files[0];\n          if (file) {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('params', JSON.stringify({\n              scale: 4,\n              use_realesrgan: true,\n              sharpening: 0.0,\n              denoising: 0,\n              saturation: 1.0,\n              contrast: 1.0,\n              brightness: 0,\n              beauty: 0.0\n            }));\n            handleUpload(formData);\n          }\n        }}\n      />\n\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff',\n        position: 'relative'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          {Object.keys(menuItems).map(menuKey => (\n            <div key={menuKey} style={{ position: 'relative' }}>\n              <span \n                style={{ \n                  cursor: 'pointer', \n                  padding: '5px 10px', \n                  borderRadius: '4px',\n                  backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n                }}\n                onClick={() => handleMenuClick(menuKey)}\n              >\n                {menuKey === 'file' ? '文件' : \n                 menuKey === 'edit' ? '编辑' :\n                 menuKey === 'view' ? '视图' :\n                 menuKey === 'tools' ? '工具' : '帮助'}\n              </span>\n              \n              {/* 下拉菜单 */}\n              {showMenu === menuKey && (\n                <div style={{\n                  position: 'absolute',\n                  top: '100%',\n                  left: 0,\n                  backgroundColor: '#3c3c3c',\n                  border: '1px solid #555',\n                  borderRadius: '4px',\n                  minWidth: '150px',\n                  zIndex: 1000,\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.3)'\n                }}>\n                  {menuItems[menuKey].map((item, index) => (\n                    <div\n                      key={index}\n                      style={{\n                        padding: '8px 12px',\n                        cursor: item.disabled ? 'not-allowed' : 'pointer',\n                        color: item.disabled ? '#888' : '#e0e0e0',\n                        fontSize: '12px',\n                        borderBottom: index < menuItems[menuKey].length - 1 ? '1px solid #555' : 'none'\n                      }}\n                      onClick={() => !item.disabled && handleMenuItemClick(item.action)}\n                      onMouseEnter={(e) => {\n                        if (!item.disabled) {\n                          e.target.style.backgroundColor = '#4a90e2';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.backgroundColor = 'transparent';\n                      }}\n                    >\n                      {item.label}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              新建\n            </button>\n            \n            <button\n              onClick={() => fileInputRef.current?.click()}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              打开\n            </button>\n            \n            {result && (\n              <button\n                onClick={downloadImage}\n                style={{\n                  padding: '6px 12px',\n                  backgroundColor: '#17a2b8',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                  fontSize: '12px'\n                }}\n              >\n                保存\n              </button>\n            )}\n            \n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n            \n            {/* 缩放控制 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>\n              <button\n                onClick={() => setZoomLevel(prev => Math.max(prev - 25, 25))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                -\n              </button>\n              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '40px', textAlign: 'center' }}>\n                {zoomLevel}%\n              </span>\n              <button\n                onClick={() => setZoomLevel(prev => Math.min(prev + 25, 400))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                +\n              </button>\n            </div>\n            \n            <div style={{ flex: 1 }}></div>\n            \n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {result ? '已处理' : isLoading ? '处理中...' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'center' }}>\n                <ResultView result={result} originalImage={originalImage} />\n              </div>\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n                <div style={{ fontSize: '14px', marginTop: '10px', color: '#666' }}>\n                  或按 Ctrl+O 打开文件\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>{isLoading ? '处理中...' : '就绪'}</span>\n        \n        {imageInfo && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>{imageInfo.name}</span>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>{imageInfo.dimensions}</span>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>{imageInfo.size}</span>\n          </>\n        )}\n        \n        {processingTime && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>处理时间: {processingTime}s</span>\n          </>\n        )}\n        \n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n\n      {/* CSS样式 */}\n      <style jsx global>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,MAAO,CAAAC,UAAU,KAAM,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtC,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgB,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACkB,aAAa,CAAEC,gBAAgB,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACoB,QAAQ,CAAEC,WAAW,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACwB,cAAc,CAAEC,iBAAiB,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,GAAG,CAAC,CAC/C,KAAM,CAAA4B,YAAY,CAAG1B,MAAM,CAAC,IAAI,CAAC,CAEjC;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4B,aAAa,CAAIC,CAAC,EAAK,CAC3B;AACA,GAAIA,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,GAAG,GAAK,GAAG,CAAE,CAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClB,GAAIL,YAAY,CAACM,OAAO,CAAE,CACxBN,YAAY,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC,CAC9B,CACF,CACA;AACA,GAAIL,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,GAAG,GAAK,GAAG,CAAE,CAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBG,WAAW,CAAC,CAAC,CACf,CACA;AACA,GAAIN,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,GAAG,GAAK,GAAG,EAAIpB,MAAM,CAAE,CACxCkB,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBI,aAAa,CAAC,CAAC,CACjB,CACA;AACA,GAAIP,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,GAAG,GAAK,GAAG,CAAE,CAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBN,YAAY,CAACW,IAAI,EAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,CAAG,EAAE,CAAE,GAAG,CAAC,CAAC,CAChD,CACA;AACA,GAAIR,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,GAAG,GAAK,GAAG,CAAE,CAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBN,YAAY,CAACW,IAAI,EAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,CAAG,EAAE,CAAE,EAAE,CAAC,CAAC,CAC/C,CACA;AACA,GAAIR,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,GAAG,GAAK,GAAG,CAAE,CAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBN,YAAY,CAAC,GAAG,CAAC,CACnB,CACF,CAAC,CAEDe,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEd,aAAa,CAAC,CACnD,MAAO,IAAMa,QAAQ,CAACE,mBAAmB,CAAC,SAAS,CAAEf,aAAa,CAAC,CACrE,CAAC,CAAE,CAACjB,MAAM,CAAC,CAAC,CAEZ;AACA,KAAM,CAAAiC,SAAS,CAAG,CAChBC,IAAI,CAAE,CACJ,CAAEC,KAAK,CAAE,eAAe,CAAEC,MAAM,CAAEA,CAAA,QAAAC,qBAAA,QAAAA,qBAAA,CAAMrB,YAAY,CAACM,OAAO,UAAAe,qBAAA,iBAApBA,qBAAA,CAAsBd,KAAK,CAAC,CAAC,EAAC,CAAC,CACvE,CAAEY,KAAK,CAAE,eAAe,CAAEC,MAAM,CAAEX,aAAa,CAAEa,QAAQ,CAAE,CAACtC,MAAO,CAAC,CACpE,CAAEmC,KAAK,CAAE,aAAa,CAAEC,MAAM,CAAEZ,WAAY,CAAC,CAC9C,CACDe,IAAI,CAAE,CACJ,CAAEJ,KAAK,CAAE,IAAI,CAAEC,MAAM,CAAEA,CAAA,GAAM,CAAC,CAAC,CAAEE,QAAQ,CAAE,IAAK,CAAC,CACjD,CAAEH,KAAK,CAAE,IAAI,CAAEC,MAAM,CAAEA,CAAA,GAAM,CAAC,CAAC,CAAEE,QAAQ,CAAE,IAAK,CAAC,CAClD,CACDE,IAAI,CAAE,CACJ,CAAEL,KAAK,CAAE,aAAa,CAAEC,MAAM,CAAEA,CAAA,GAAMrB,YAAY,CAACW,IAAI,EAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,CAAG,EAAE,CAAE,GAAG,CAAC,CAAE,CAAC,CACtF,CAAES,KAAK,CAAE,aAAa,CAAEC,MAAM,CAAEA,CAAA,GAAMrB,YAAY,CAACW,IAAI,EAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,CAAG,EAAE,CAAE,EAAE,CAAC,CAAE,CAAC,CACrF,CAAES,KAAK,CAAE,eAAe,CAAEC,MAAM,CAAEA,CAAA,GAAMrB,YAAY,CAAC,GAAG,CAAE,CAAC,CAC5D,CACD0B,KAAK,CAAE,CACL,CAAEN,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAEA,CAAA,GAAM,CAAC,CAAC,CAAEE,QAAQ,CAAE,IAAK,CAAC,CACnD,CAAEH,KAAK,CAAE,IAAI,CAAEC,MAAM,CAAEA,CAAA,GAAM,CAAC,CAAC,CAAEE,QAAQ,CAAE,IAAK,CAAC,CAClD,CACDI,IAAI,CAAE,CACJ,CAAEP,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAEA,CAAA,GAAMO,KAAK,CAAC,oFAAoF,CAAE,CAAC,CAC3H,CAAER,KAAK,CAAE,IAAI,CAAEC,MAAM,CAAEA,CAAA,GAAMO,KAAK,CAAC,+BAA+B,CAAE,CAAC,CAEzE,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,QAAQ,EAAK,CACvC,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAC5B7C,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAA6B,IAAI,CAAGW,QAAQ,CAACI,GAAG,CAAC,MAAM,CAAC,CACjC,GAAIf,IAAI,CAAE,CACR,KAAM,CAAAgB,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIlC,CAAC,EAAK,CACrBX,gBAAgB,CAACW,CAAC,CAACmC,MAAM,CAACrD,MAAM,CAAC,CACjC;AACA,KAAM,CAAAsD,GAAG,CAAG,GAAI,CAAAC,KAAK,CAAC,CAAC,CACvBD,GAAG,CAACF,MAAM,CAAG,IAAM,CACjBzC,YAAY,CAAC,CACX6C,IAAI,CAAEtB,IAAI,CAACsB,IAAI,CACfC,IAAI,CAAE,CAACvB,IAAI,CAACuB,IAAI,CAAG,IAAI,CAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAClDC,UAAU,CAAE,GAAGL,GAAG,CAACM,KAAK,MAAMN,GAAG,CAACO,MAAM,EAAE,CAC1CC,IAAI,CAAE5B,IAAI,CAAC4B,IACb,CAAC,CAAC,CACJ,CAAC,CACDR,GAAG,CAACS,GAAG,CAAG7C,CAAC,CAACmC,MAAM,CAACrD,MAAM,CAC3B,CAAC,CACDkD,MAAM,CAACc,aAAa,CAAC9B,IAAI,CAAC,CAC5B,CAEA,GAAI,CACF,KAAM,CAAA+B,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gCAAgC,CAAE,CAC7DC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEvB,QACR,CAAC,CAAC,CAEF,GAAI,CAACoB,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAC,KAAK,CAACF,SAAS,CAACG,MAAM,EAAI,MAAM,CAAC,CAC7C,CAEA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAT,QAAQ,CAACM,IAAI,CAAC,CAAC,CAClC,KAAM,CAAAI,OAAO,CAAG5B,IAAI,CAACC,GAAG,CAAC,CAAC,CAC1BnC,iBAAiB,CAAC,CAAC,CAAC8D,OAAO,CAAG7B,SAAS,EAAI,IAAI,EAAEY,OAAO,CAAC,CAAC,CAAC,CAAC,CAC5DzD,SAAS,CAACyE,IAAI,CAAC,CACjB,CAAE,MAAOE,GAAG,CAAE,CACZC,OAAO,CAACzE,KAAK,CAAC,OAAO,CAAEwE,GAAG,CAAC,CAC3BvE,QAAQ,CAACuE,GAAG,CAACE,OAAO,EAAI,oBAAoB,CAAC,CAC/C,CAAC,OAAS,CACR3E,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAqB,WAAW,CAAGA,CAAA,GAAM,CACxBvB,SAAS,CAAC,IAAI,CAAC,CACfI,QAAQ,CAAC,IAAI,CAAC,CACdE,gBAAgB,CAAC,IAAI,CAAC,CACtBI,YAAY,CAAC,IAAI,CAAC,CAClBE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,YAAY,CAAC,GAAG,CAAC,CACnB,CAAC,CAED,KAAM,CAAAU,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIzB,MAAM,CAAE,CACV,KAAM,CAAA+E,IAAI,CAAGjD,QAAQ,CAACkD,aAAa,CAAC,GAAG,CAAC,CACxCD,IAAI,CAACE,IAAI,CAAG,gCAAgCjF,MAAM,CAACkF,YAAY,EAAE,CACjEH,IAAI,CAACI,QAAQ,CAAG,YAAYnF,MAAM,CAACoF,QAAQ,EAAE,CAC7CtD,QAAQ,CAACsC,IAAI,CAACiB,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACxD,KAAK,CAAC,CAAC,CACZO,QAAQ,CAACsC,IAAI,CAACkB,WAAW,CAACP,IAAI,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAQ,eAAe,CAAIC,OAAO,EAAK,CACnC/E,WAAW,CAACD,QAAQ,GAAKgF,OAAO,CAAG,IAAI,CAAGA,OAAO,CAAC,CACpD,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIrD,MAAM,EAAK,CACtCA,MAAM,CAAC,CAAC,CACR3B,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED,mBACEb,KAAA,QAAK8F,KAAK,CAAE,CACV7B,MAAM,CAAE,OAAO,CACf8B,eAAe,CAAE,SAAS,CAC1BC,UAAU,CAAE,mEAAmE,CAC/EC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,QAAQ,CAAE,QACZ,CAAE,CAAAC,QAAA,eAEAtG,IAAA,UACEuG,GAAG,CAAEjF,YAAa,CAClB8C,IAAI,CAAC,MAAM,CACXoC,MAAM,CAAC,SAAS,CAChBR,KAAK,CAAE,CAAEG,OAAO,CAAE,MAAO,CAAE,CAC3BM,QAAQ,CAAGjF,CAAC,EAAK,CACf,KAAM,CAAAgB,IAAI,CAAGhB,CAAC,CAACmC,MAAM,CAAC+C,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIlE,IAAI,CAAE,CACR,KAAM,CAAAW,QAAQ,CAAG,GAAI,CAAAwD,QAAQ,CAAC,CAAC,CAC/BxD,QAAQ,CAACyD,MAAM,CAAC,MAAM,CAAEpE,IAAI,CAAC,CAC7BW,QAAQ,CAACyD,MAAM,CAAC,QAAQ,CAAEC,IAAI,CAACC,SAAS,CAAC,CACvCC,KAAK,CAAE,CAAC,CACRC,cAAc,CAAE,IAAI,CACpBC,UAAU,CAAE,GAAG,CACfC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,GAAG,CACbC,UAAU,CAAE,CAAC,CACbC,MAAM,CAAE,GACV,CAAC,CAAC,CAAC,CACHpE,YAAY,CAACC,QAAQ,CAAC,CACxB,CACF,CAAE,CACH,CAAC,cAGFjD,KAAA,QAAK8F,KAAK,CAAE,CACV7B,MAAM,CAAE,MAAM,CACd8B,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,gBAAgB,CAC9BpB,OAAO,CAAE,MAAM,CACfqB,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,QAAQ,CACjBC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,UACZ,CAAE,CAAArB,QAAA,eAEApG,KAAA,QAAK8F,KAAK,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEyB,GAAG,CAAE,KAAK,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAvB,QAAA,eAC/DtG,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAE2D,YAAY,CAAE,KAAK,CAAE7B,eAAe,CAAE,SAAU,CAAE,CAAM,CAAC,cACtGjG,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAE2D,YAAY,CAAE,KAAK,CAAE7B,eAAe,CAAE,SAAU,CAAE,CAAM,CAAC,cACtGjG,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAE2D,YAAY,CAAE,KAAK,CAAE7B,eAAe,CAAE,SAAU,CAAE,CAAM,CAAC,EACnG,CAAC,cAGNjG,IAAA,QAAKgG,KAAK,CAAE,CACV+B,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,QAAQ,CACnBC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBR,KAAK,CAAE,SACT,CAAE,CAAApB,QAAA,CAAC,sCAEH,CAAK,CAAC,cAGNtG,IAAA,QAAKgG,KAAK,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEyB,GAAG,CAAE,MAAM,CAAEK,QAAQ,CAAE,MAAO,CAAE,CAAA3B,QAAA,CAC5D6B,MAAM,CAACC,IAAI,CAAC7F,SAAS,CAAC,CAAC8F,GAAG,CAACvC,OAAO,eACjC5F,KAAA,QAAmB8F,KAAK,CAAE,CAAE2B,QAAQ,CAAE,UAAW,CAAE,CAAArB,QAAA,eACjDtG,IAAA,SACEgG,KAAK,CAAE,CACLsC,MAAM,CAAE,SAAS,CACjBb,OAAO,CAAE,UAAU,CACnBK,YAAY,CAAE,KAAK,CACnB7B,eAAe,CAAEnF,QAAQ,GAAKgF,OAAO,CAAG,SAAS,CAAG,aACtD,CAAE,CACFyC,OAAO,CAAEA,CAAA,GAAM1C,eAAe,CAACC,OAAO,CAAE,CAAAQ,QAAA,CAEvCR,OAAO,GAAK,MAAM,CAAG,IAAI,CACzBA,OAAO,GAAK,MAAM,CAAG,IAAI,CACzBA,OAAO,GAAK,MAAM,CAAG,IAAI,CACzBA,OAAO,GAAK,OAAO,CAAG,IAAI,CAAG,IAAI,CAC9B,CAAC,CAGNhF,QAAQ,GAAKgF,OAAO,eACnB9F,IAAA,QAAKgG,KAAK,CAAE,CACV2B,QAAQ,CAAE,UAAU,CACpBa,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPxC,eAAe,CAAE,SAAS,CAC1ByC,MAAM,CAAE,gBAAgB,CACxBZ,YAAY,CAAE,KAAK,CACnBa,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,IAAI,CACZC,SAAS,CAAE,2BACb,CAAE,CAAAvC,QAAA,CACC/D,SAAS,CAACuD,OAAO,CAAC,CAACuC,GAAG,CAAC,CAACS,IAAI,CAAEC,KAAK,gBAClC/I,IAAA,QAEEgG,KAAK,CAAE,CACLyB,OAAO,CAAE,UAAU,CACnBa,MAAM,CAAEQ,IAAI,CAAClG,QAAQ,CAAG,aAAa,CAAG,SAAS,CACjD8E,KAAK,CAAEoB,IAAI,CAAClG,QAAQ,CAAG,MAAM,CAAG,SAAS,CACzCqF,QAAQ,CAAE,MAAM,CAChBV,YAAY,CAAEwB,KAAK,CAAGxG,SAAS,CAACuD,OAAO,CAAC,CAACkD,MAAM,CAAG,CAAC,CAAG,gBAAgB,CAAG,MAC3E,CAAE,CACFT,OAAO,CAAEA,CAAA,GAAM,CAACO,IAAI,CAAClG,QAAQ,EAAImD,mBAAmB,CAAC+C,IAAI,CAACpG,MAAM,CAAE,CAClEuG,YAAY,CAAGzH,CAAC,EAAK,CACnB,GAAI,CAACsH,IAAI,CAAClG,QAAQ,CAAE,CAClBpB,CAAC,CAACmC,MAAM,CAACqC,KAAK,CAACC,eAAe,CAAG,SAAS,CAC5C,CACF,CAAE,CACFiD,YAAY,CAAG1H,CAAC,EAAK,CACnBA,CAAC,CAACmC,MAAM,CAACqC,KAAK,CAACC,eAAe,CAAG,aAAa,CAChD,CAAE,CAAAK,QAAA,CAEDwC,IAAI,CAACrG,KAAK,EAlBNsG,KAmBF,CACN,CAAC,CACC,CACN,GArDOjD,OAsDL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGN5F,KAAA,QAAK8F,KAAK,CAAE,CACV+B,IAAI,CAAE,CAAC,CACP5B,OAAO,CAAE,MAAM,CACfE,QAAQ,CAAE,QACZ,CAAE,CAAAC,QAAA,eAEApG,KAAA,QAAK8F,KAAK,CAAE,CACV+B,IAAI,CAAE,CAAC,CACP9B,eAAe,CAAE,SAAS,CAC1BE,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBuB,QAAQ,CAAE,UACZ,CAAE,CAAArB,QAAA,eAEApG,KAAA,QAAK8F,KAAK,CAAE,CACV7B,MAAM,CAAE,MAAM,CACd8B,eAAe,CAAE,MAAM,CACvBsB,YAAY,CAAE,gBAAgB,CAC9BpB,OAAO,CAAE,MAAM,CACfqB,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,QAAQ,CACjBG,GAAG,CAAE,MACP,CAAE,CAAAtB,QAAA,eACAtG,IAAA,WACEuI,OAAO,CAAEzG,WAAY,CACrBkE,KAAK,CAAE,CACLyB,OAAO,CAAE,UAAU,CACnBxB,eAAe,CAAE,SAAS,CAC1ByB,KAAK,CAAE,OAAO,CACdgB,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAAK,CACnBQ,MAAM,CAAE,SAAS,CACjBL,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,CACH,cAED,CAAQ,CAAC,cAETtG,IAAA,WACEuI,OAAO,CAAEA,CAAA,QAAAY,sBAAA,QAAAA,sBAAA,CAAM7H,YAAY,CAACM,OAAO,UAAAuH,sBAAA,iBAApBA,sBAAA,CAAsBtH,KAAK,CAAC,CAAC,EAAC,CAC7CmE,KAAK,CAAE,CACLyB,OAAO,CAAE,UAAU,CACnBxB,eAAe,CAAE,SAAS,CAC1ByB,KAAK,CAAE,OAAO,CACdgB,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAAK,CACnBQ,MAAM,CAAE,SAAS,CACjBL,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,CACH,cAED,CAAQ,CAAC,CAERhG,MAAM,eACLN,IAAA,WACEuI,OAAO,CAAExG,aAAc,CACvBiE,KAAK,CAAE,CACLyB,OAAO,CAAE,UAAU,CACnBxB,eAAe,CAAE,SAAS,CAC1ByB,KAAK,CAAE,OAAO,CACdgB,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAAK,CACnBQ,MAAM,CAAE,SAAS,CACjBL,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,CACH,cAED,CAAQ,CACT,cAEDtG,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAE8B,eAAe,CAAE,MAAO,CAAE,CAAM,CAAC,cAG7E/F,KAAA,QAAK8F,KAAK,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEqB,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,KAAM,CAAE,CAAAtB,QAAA,eAChEtG,IAAA,WACEuI,OAAO,CAAEA,CAAA,GAAMlH,YAAY,CAACW,IAAI,EAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,CAAG,EAAE,CAAE,EAAE,CAAC,CAAE,CAC7DgE,KAAK,CAAE,CACLyB,OAAO,CAAE,SAAS,CAClBxB,eAAe,CAAE,SAAS,CAC1ByB,KAAK,CAAE,OAAO,CACdgB,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAAK,CACnBQ,MAAM,CAAE,SAAS,CACjBL,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,CACH,GAED,CAAQ,CAAC,cACTpG,KAAA,SAAM8F,KAAK,CAAE,CAAE0B,KAAK,CAAE,MAAM,CAAEO,QAAQ,CAAE,MAAM,CAAEU,QAAQ,CAAE,MAAM,CAAEX,SAAS,CAAE,QAAS,CAAE,CAAA1B,QAAA,EACrFlF,SAAS,CAAC,GACb,EAAM,CAAC,cACPpB,IAAA,WACEuI,OAAO,CAAEA,CAAA,GAAMlH,YAAY,CAACW,IAAI,EAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,CAAG,EAAE,CAAE,GAAG,CAAC,CAAE,CAC9DgE,KAAK,CAAE,CACLyB,OAAO,CAAE,SAAS,CAClBxB,eAAe,CAAE,SAAS,CAC1ByB,KAAK,CAAE,OAAO,CACdgB,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAAK,CACnBQ,MAAM,CAAE,SAAS,CACjBL,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,CACH,GAED,CAAQ,CAAC,EACN,CAAC,cAENtG,IAAA,QAAKgG,KAAK,CAAE,CAAE+B,IAAI,CAAE,CAAE,CAAE,CAAM,CAAC,cAE/B/H,IAAA,SAAMgG,KAAK,CAAE,CAAE0B,KAAK,CAAE,MAAM,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAA3B,QAAA,CAC9ChG,MAAM,CAAG,KAAK,CAAGE,SAAS,CAAG,QAAQ,CAAG,QAAQ,CAC7C,CAAC,EACJ,CAAC,cAGNN,KAAA,QAAK8F,KAAK,CAAE,CACV+B,IAAI,CAAE,CAAC,CACP5B,OAAO,CAAE,MAAM,CACfqB,UAAU,CAAE,QAAQ,CACpB4B,cAAc,CAAE,QAAQ,CACxB3B,OAAO,CAAE,MAAM,CACfE,QAAQ,CAAE,UACZ,CAAE,CAAArB,QAAA,EACC5F,KAAK,eACJR,KAAA,QAAK8F,KAAK,CAAE,CACV2B,QAAQ,CAAE,UAAU,CACpBa,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,MAAM,CACZY,KAAK,CAAE,MAAM,CACbpD,eAAe,CAAE,SAAS,CAC1ByB,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,MAAM,CACfK,YAAY,CAAE,KAAK,CACnBG,QAAQ,CAAE,MAAM,CAChBW,MAAM,CAAE,EACV,CAAE,CAAAtC,QAAA,EAAC,gBACG,CAAC5F,KAAK,EACP,CACN,CAEAF,SAAS,eACRN,KAAA,QAAK8F,KAAK,CAAE,CACV2B,QAAQ,CAAE,UAAU,CACpBa,GAAG,CAAE,KAAK,CACVC,IAAI,CAAE,KAAK,CACXa,SAAS,CAAE,uBAAuB,CAClCrD,eAAe,CAAE,iBAAiB,CAClCyB,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,MAAM,CACfK,YAAY,CAAE,KAAK,CACnBE,SAAS,CAAE,QAAQ,CACnBY,MAAM,CAAE,EACV,CAAE,CAAAtC,QAAA,eACAtG,IAAA,QAAKgG,KAAK,CAAE,CAAEuD,YAAY,CAAE,MAAO,CAAE,CAAAjD,QAAA,CAAC,yCAAS,CAAK,CAAC,cACrDtG,IAAA,QAAKgG,KAAK,CAAE,CACV9B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACduE,MAAM,CAAE,gBAAgB,CACxBc,SAAS,CAAE,mBAAmB,CAC9B1B,YAAY,CAAE,KAAK,CACnB2B,SAAS,CAAE,yBAAyB,CACpCC,MAAM,CAAE,QACV,CAAE,CAAM,CAAC,EACN,CACN,CAEApJ,MAAM,cACLN,IAAA,QAAKgG,KAAK,CAAE,CAAEsD,SAAS,CAAE,SAASlI,SAAS,CAAG,GAAG,GAAG,CAAEuI,eAAe,CAAE,QAAS,CAAE,CAAArD,QAAA,cAChFtG,IAAA,CAACF,UAAU,EAACQ,MAAM,CAAEA,MAAO,CAACM,aAAa,CAAEA,aAAc,CAAE,CAAC,CACzD,CAAC,cAENV,KAAA,QAAK8F,KAAK,CAAE,CACVgC,SAAS,CAAE,QAAQ,CACnBN,KAAK,CAAE,MAAM,CACbO,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,eACAtG,IAAA,QAAKgG,KAAK,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAjD,QAAA,CAAC,cAAE,CAAK,CAAC,cAChEtG,IAAA,QAAAsG,QAAA,CAAK,sFAAc,CAAK,CAAC,cACzBtG,IAAA,QAAKgG,KAAK,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAE2B,SAAS,CAAE,MAAM,CAAElC,KAAK,CAAE,MAAO,CAAE,CAAApB,QAAA,CAAC,8CAEpE,CAAK,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,cAGNpG,KAAA,QAAK8F,KAAK,CAAE,CACV9B,KAAK,CAAE,OAAO,CACd+B,eAAe,CAAE,SAAS,CAC1B4D,UAAU,CAAE,gBAAgB,CAC5B1D,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAE,QAAA,eAEAtG,IAAA,QAAKgG,KAAK,CAAE,CACV7B,MAAM,CAAE,MAAM,CACd8B,eAAe,CAAE,MAAM,CACvBsB,YAAY,CAAE,gBAAgB,CAC9BpB,OAAO,CAAE,MAAM,CACfqB,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,QAAQ,CACjBC,KAAK,CAAE,SAAS,CAChBO,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAA5B,QAAA,CAAC,0BAEH,CAAK,CAAC,cAGNtG,IAAA,QAAKgG,KAAK,CAAE,CACV+B,IAAI,CAAE,CAAC,CACP1B,QAAQ,CAAE,MAAM,CAChBoB,OAAO,CAAE,GACX,CAAE,CAAAnB,QAAA,cACAtG,IAAA,CAACH,UAAU,EAACiK,QAAQ,CAAE5G,YAAa,CAAC1C,SAAS,CAAEA,SAAU,CAAE,CAAC,CACzD,CAAC,EACH,CAAC,EACH,CAAC,cAGNN,KAAA,QAAK8F,KAAK,CAAE,CACV7B,MAAM,CAAE,MAAM,CACd8B,eAAe,CAAE,MAAM,CACvBuD,SAAS,CAAE,gBAAgB,CAC3BrD,OAAO,CAAE,MAAM,CACfqB,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,QAAQ,CACjBC,KAAK,CAAE,MAAM,CACbO,QAAQ,CAAE,MACZ,CAAE,CAAA3B,QAAA,eACAtG,IAAA,SAAAsG,QAAA,CAAO9F,SAAS,CAAG,QAAQ,CAAG,IAAI,CAAO,CAAC,CAEzCQ,SAAS,eACRd,KAAA,CAAAE,SAAA,EAAAkG,QAAA,eACEtG,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAE8B,eAAe,CAAE,MAAM,CAAEyD,MAAM,CAAE,QAAS,CAAE,CAAM,CAAC,cAC/F1J,IAAA,SAAAsG,QAAA,CAAOtF,SAAS,CAAC8C,IAAI,CAAO,CAAC,cAC7B9D,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAE8B,eAAe,CAAE,MAAM,CAAEyD,MAAM,CAAE,QAAS,CAAE,CAAM,CAAC,cAC/F1J,IAAA,SAAAsG,QAAA,CAAOtF,SAAS,CAACiD,UAAU,CAAO,CAAC,cACnCjE,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAE8B,eAAe,CAAE,MAAM,CAAEyD,MAAM,CAAE,QAAS,CAAE,CAAM,CAAC,cAC/F1J,IAAA,SAAAsG,QAAA,CAAOtF,SAAS,CAAC+C,IAAI,CAAO,CAAC,EAC7B,CACH,CAEA7C,cAAc,eACbhB,KAAA,CAAAE,SAAA,EAAAkG,QAAA,eACEtG,IAAA,QAAKgG,KAAK,CAAE,CAAE9B,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAE8B,eAAe,CAAE,MAAM,CAAEyD,MAAM,CAAE,QAAS,CAAE,CAAM,CAAC,cAC/FxJ,KAAA,SAAAoG,QAAA,EAAM,4BAAM,CAACpF,cAAc,CAAC,GAAC,EAAM,CAAC,EACpC,CACH,cAEDlB,IAAA,QAAKgG,KAAK,CAAE,CAAE+B,IAAI,CAAE,CAAE,CAAE,CAAM,CAAC,cAC/B/H,IAAA,SAAAsG,QAAA,CAAM,iBAAe,CAAM,CAAC,EACzB,CAAC,cAGNtG,IAAA,UAAOD,GAAG,MAACgK,MAAM,MAAAzD,QAAA,CAAE;AACzB;AACA;AACA;AACA;AACA,OAAO,CAAQ,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAjG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}