{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ResultView=_ref=>{let{result,originalImage}=_ref;const[imageLoaded,setImageLoaded]=useState(false);const[imageError,setImageError]=useState(false);const[splitPosition,setSplitPosition]=useState(50);const[isDragging,setIsDragging]=useState(false);const[viewMode,setViewMode]=useState('split');const[showParams,setShowParams]=useState(false);const containerRef=useRef(null);const handleImageLoad=()=>{setImageLoaded(true);};const handleImageError=()=>{setImageError(true);};const downloadImage=()=>{const link=document.createElement('a');link.href=`http://localhost:8001/result/${result.enhanced_url}`;link.download=`enhanced_${result.filename}`;document.body.appendChild(link);link.click();document.body.removeChild(link);};// 分割线拖拽处理\nconst handleMouseDown=e=>{setIsDragging(true);e.preventDefault();};const handleMouseMove=e=>{if(!isDragging||!containerRef.current)return;const rect=containerRef.current.getBoundingClientRect();const x=e.clientX-rect.left;const percentage=Math.max(0,Math.min(100,x/rect.width*100));setSplitPosition(percentage);};const handleMouseUp=()=>{setIsDragging(false);};useEffect(()=>{if(isDragging){document.addEventListener('mousemove',handleMouseMove);document.addEventListener('mouseup',handleMouseUp);return()=>{document.removeEventListener('mousemove',handleMouseMove);document.removeEventListener('mouseup',handleMouseUp);};}},[isDragging]);const resetSplit=()=>{setSplitPosition(50);};return/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%',height:'100%',display:'flex',flexDirection:'column',backgroundColor:'#1e1e1e',color:'#e0e0e0'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{height:'40px',backgroundColor:'#2d2d2d',borderBottom:'1px solid #444',display:'flex',alignItems:'center',padding:'0 15px',gap:'10px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'5px'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('split'),style:{padding:'4px 8px',backgroundColor:viewMode==='split'?'#4a90e2':'#555',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"\\u5206\\u5272\\u5BF9\\u6BD4\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('side-by-side'),style:{padding:'4px 8px',backgroundColor:viewMode==='side-by-side'?'#4a90e2':'#555',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"\\u5E76\\u6392\\u5BF9\\u6BD4\"}),viewMode==='split'&&/*#__PURE__*/_jsx(\"button\",{onClick:resetSplit,style:{padding:'4px 8px',backgroundColor:'#28a745',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"\\u91CD\\u7F6E\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1}}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowParams(!showParams),style:{padding:'4px 8px',backgroundColor:showParams?'#4a90e2':'#555',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"\\u53C2\\u6570\"}),/*#__PURE__*/_jsx(\"button\",{onClick:downloadImage,style:{padding:'4px 8px',backgroundColor:'#28a745',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'11px'},children:\"\\u4E0B\\u8F7D\"})]}),showParams&&result.params_used&&/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'#2d2d2d',borderBottom:'1px solid #444',padding:'10px 15px',fontSize:'11px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',marginBottom:'8px',color:'#fff'},children:\"\\u5904\\u7406\\u53C2\\u6570\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(120px, 1fr))',gap:'8px',color:'#ccc'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u500D\\u6570: \",result.params_used.scale,\"x\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u6A21\\u578B: \",result.params_used.use_realesrgan?'RealESRGAN':'简单插值']}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u9510\\u5316: \",(result.params_used.sharpening*100).toFixed(0),\"%\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u964D\\u566A: \",result.params_used.denoising]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u9971\\u548C\\u5EA6: \",result.params_used.saturation.toFixed(1)]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u5BF9\\u6BD4\\u5EA6: \",result.params_used.contrast.toFixed(1)]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u4EAE\\u5EA6: \",result.params_used.brightness>0?'+':'',result.params_used.brightness]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u7F8E\\u989C: \",(result.params_used.beauty*100).toFixed(0),\"%\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,position:'relative',overflow:'hidden'},children:viewMode==='split'?/*#__PURE__*/// 分割线对比模式\n_jsxs(\"div\",{ref:containerRef,style:{width:'100%',height:'100%',position:'relative',cursor:isDragging?'ew-resize':'default',userSelect:'none',display:'flex',alignItems:'center',justifyContent:'center'},children:[originalImage&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',maxWidth:'90%',maxHeight:'90%',display:'inline-block'},children:[imageError?/*#__PURE__*/_jsx(\"div\",{style:{width:'600px',height:'400px',border:'2px dashed #d32f2f',borderRadius:'8px',display:'flex',alignItems:'center',justifyContent:'center',color:'#d32f2f',backgroundColor:'#2d2d2d'},children:\"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"}):/*#__PURE__*/_jsx(\"img\",{src:`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,alt:\"\\u589E\\u5F3A\\u56FE\\u50CF\",onLoad:handleImageLoad,onError:handleImageError,style:{maxWidth:'100%',maxHeight:'100%',border:'1px solid #555',borderRadius:'4px',display:'block',opacity:imageLoaded?1:0.5}}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,width:`${splitPosition}%`,height:'100%',overflow:'hidden',borderRadius:'4px 0 0 4px'},children:/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"\\u539F\\u59CB\\u56FE\\u50CF\",style:{width:`${100*100/splitPosition}%`,height:'100%',objectFit:'cover',border:'1px solid #555',borderRadius:'4px'}})}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:`${splitPosition}%`,width:'2px',height:'100%',backgroundColor:'#4a90e2',cursor:'ew-resize',transform:'translateX(-1px)',zIndex:10},onMouseDown:handleMouseDown,children:/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',width:'16px',height:'30px',backgroundColor:'#4a90e2',borderRadius:'8px',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'10px',fontWeight:'bold'},children:\"\\u27F7\"})}),!imageLoaded&&!imageError&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',color:'#ccc',backgroundColor:'rgba(0,0,0,0.8)',padding:'10px',borderRadius:'4px'},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',bottom:'10px',left:'50%',transform:'translateX(-50%)',backgroundColor:'rgba(0,0,0,0.8)',color:'white',padding:'4px 8px',borderRadius:'4px',fontSize:'11px'},children:[\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u56FE | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A | \\u4F4D\\u7F6E\\uFF1A\",splitPosition.toFixed(0),\"%\"]})]}):/*#__PURE__*/// 并排对比模式\n_jsxs(\"div\",{style:{display:'flex',gap:'10px',padding:'20px',height:'100%',alignItems:'center',justifyContent:'center'},children:[originalImage&&/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'8px',fontSize:'12px',color:'#ccc'},children:\"\\u539F\\u59CB\\u56FE\\u50CF\"}),/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"\\u539F\\u59CB\\u56FE\\u50CF\",style:{maxWidth:'100%',maxHeight:'400px',border:'1px solid #555',borderRadius:'4px',objectFit:'contain'}})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'8px',fontSize:'12px',color:'#ccc'},children:\"\\u589E\\u5F3A\\u56FE\\u50CF\"}),imageError?/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'300px',border:'2px dashed #d32f2f',borderRadius:'4px',display:'flex',alignItems:'center',justifyContent:'center',color:'#d32f2f',backgroundColor:'#2d2d2d'},children:\"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"}):/*#__PURE__*/_jsx(\"img\",{src:`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,alt:\"\\u589E\\u5F3A\\u56FE\\u50CF\",onLoad:handleImageLoad,onError:handleImageError,style:{maxWidth:'100%',maxHeight:'400px',border:'1px solid #555',borderRadius:'4px',objectFit:'contain',display:imageLoaded?'block':'none'}}),!imageLoaded&&!imageError&&/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'300px',display:'flex',alignItems:'center',justifyContent:'center',border:'1px dashed #555',borderRadius:'4px',color:'#ccc'},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})]})})]});};export default ResultView;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "ResultView", "_ref", "result", "originalImage", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "showParams", "setShowParams", "containerRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "e", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleMouseUp", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "padding", "gap", "onClick", "border", "borderRadius", "cursor", "fontSize", "flex", "params_used", "fontWeight", "marginBottom", "gridTemplateColumns", "scale", "use_realesrgan", "sharpening", "toFixed", "denoising", "saturation", "contrast", "brightness", "beauty", "position", "overflow", "ref", "userSelect", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", "src", "Date", "now", "alt", "onLoad", "onError", "opacity", "top", "objectFit", "transform", "zIndex", "onMouseDown", "bottom", "textAlign"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n  const [showParams, setShowParams] = useState(false);\n  const containerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n    \n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#e0e0e0'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2d2d2d',\n        borderBottom: '1px solid #444',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        gap: '10px'\n      }}>\n        <div style={{ display: 'flex', gap: '5px' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '11px'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '11px'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n\n        <div style={{ flex: 1 }}></div>\n\n        <button\n          onClick={() => setShowParams(!showParams)}\n          style={{\n            padding: '4px 8px',\n            backgroundColor: showParams ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          }}\n        >\n          参数\n        </button>\n\n        <button\n          onClick={downloadImage}\n          style={{\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px'\n          }}\n        >\n          下载\n        </button>\n      </div>\n\n      {/* 参数信息面板 */}\n      {showParams && result.params_used && (\n        <div style={{\n          backgroundColor: '#2d2d2d',\n          borderBottom: '1px solid #444',\n          padding: '10px 15px',\n          fontSize: '11px'\n        }}>\n          <div style={{ fontWeight: '500', marginBottom: '8px', color: '#fff' }}>处理参数</div>\n          <div style={{ \n            display: 'grid', \n            gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n            gap: '8px',\n            color: '#ccc'\n          }}>\n            <div>倍数: {result.params_used.scale}x</div>\n            <div>模型: {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n            <div>锐化: {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n            <div>降噪: {result.params_used.denoising}</div>\n            <div>饱和度: {result.params_used.saturation.toFixed(1)}</div>\n            <div>对比度: {result.params_used.contrast.toFixed(1)}</div>\n            <div>亮度: {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n            <div>美颜: {(result.params_used.beauty * 100).toFixed(0)}%</div>\n          </div>\n        </div>\n      )}\n\n      {/* 图像显示区域 */}\n      <div style={{ flex: 1, position: 'relative', overflow: 'hidden' }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div \n            ref={containerRef}\n            style={{ \n              width: '100%',\n              height: '100%',\n              position: 'relative',\n              cursor: isDragging ? 'ew-resize' : 'default',\n              userSelect: 'none',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            {originalImage && (\n              <div style={{ \n                position: 'relative', \n                maxWidth: '90%',\n                maxHeight: '90%',\n                display: 'inline-block'\n              }}>\n                {/* 增强图像作为背景 */}\n                {imageError ? (\n                  <div style={{\n                    width: '600px',\n                    height: '400px',\n                    border: '2px dashed #d32f2f',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: '#d32f2f',\n                    backgroundColor: '#2d2d2d'\n                  }}>\n                    增强图像加载失败\n                  </div>\n                ) : (\n                  <img\n                    src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                    alt=\"增强图像\"\n                    onLoad={handleImageLoad}\n                    onError={handleImageError}\n                    style={{\n                      maxWidth: '100%',\n                      maxHeight: '100%',\n                      border: '1px solid #555',\n                      borderRadius: '4px',\n                      display: 'block',\n                      opacity: imageLoaded ? 1 : 0.5\n                    }}\n                  />\n                )}\n                \n                {/* 原始图像覆盖层 */}\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    width: `${splitPosition}%`,\n                    height: '100%',\n                    overflow: 'hidden',\n                    borderRadius: '4px 0 0 4px'\n                  }}\n                >\n                  <img\n                    src={originalImage}\n                    alt=\"原始图像\"\n                    style={{\n                      width: `${100 * 100 / splitPosition}%`,\n                      height: '100%',\n                      objectFit: 'cover',\n                      border: '1px solid #555',\n                      borderRadius: '4px'\n                    }}\n                  />\n                </div>\n                \n                {/* 分割线 */}\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: `${splitPosition}%`,\n                    width: '2px',\n                    height: '100%',\n                    backgroundColor: '#4a90e2',\n                    cursor: 'ew-resize',\n                    transform: 'translateX(-1px)',\n                    zIndex: 10\n                  }}\n                  onMouseDown={handleMouseDown}\n                >\n                  {/* 分割线手柄 */}\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: '50%',\n                      left: '50%',\n                      transform: 'translate(-50%, -50%)',\n                      width: '16px',\n                      height: '30px',\n                      backgroundColor: '#4a90e2',\n                      borderRadius: '8px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '10px',\n                      fontWeight: 'bold'\n                    }}\n                  >\n                    ⟷\n                  </div>\n                </div>\n\n                {!imageLoaded && !imageError && (\n                  <div style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    color: '#ccc',\n                    backgroundColor: 'rgba(0,0,0,0.8)',\n                    padding: '10px',\n                    borderRadius: '4px'\n                  }}>\n                    加载中...\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* 分割位置指示 */}\n            <div style={{\n              position: 'absolute',\n              bottom: '10px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              fontSize: '11px'\n            }}>\n              左侧：原图 | 右侧：增强 | 位置：{splitPosition.toFixed(0)}%\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{ \n            display: 'flex', \n            gap: '10px', \n            padding: '20px',\n            height: '100%',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ flex: 1, textAlign: 'center' }}>\n                <div style={{ marginBottom: '8px', fontSize: '12px', color: '#ccc' }}>原始图像</div>\n                <img\n                  src={originalImage}\n                  alt=\"原始图像\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    border: '1px solid #555',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ flex: 1, textAlign: 'center' }}>\n              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#ccc' }}>增强图像</div>\n              \n              {imageError ? (\n                <div style={{\n                  width: '100%',\n                  height: '300px',\n                  border: '2px dashed #d32f2f',\n                  borderRadius: '4px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#d32f2f',\n                  backgroundColor: '#2d2d2d'\n                }}>\n                  图像加载失败\n                </div>\n              ) : (\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    border: '1px solid #555',\n                    borderRadius: '4px',\n                    objectFit: 'contain',\n                    display: imageLoaded ? 'block' : 'none'\n                  }}\n                />\n              )}\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '300px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #555',\n                  borderRadius: '4px',\n                  color: '#ccc'\n                }}>\n                  加载中...\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAA+B,IAA9B,CAAEC,MAAM,CAAEC,aAAc,CAAC,CAAAF,IAAA,CAC3C,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACe,aAAa,CAAEC,gBAAgB,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiB,UAAU,CAAEC,aAAa,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,OAAO,CAAC,CACjD,KAAM,CAACqB,UAAU,CAAEC,aAAa,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAAuB,YAAY,CAAGtB,MAAM,CAAC,IAAI,CAAC,CAEjC,KAAM,CAAAuB,eAAe,CAAGA,CAAA,GAAM,CAC5BZ,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAAa,gBAAgB,CAAGA,CAAA,GAAM,CAC7BX,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAAY,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAG,gCAAgCrB,MAAM,CAACsB,YAAY,EAAE,CACjEJ,IAAI,CAACK,QAAQ,CAAG,YAAYvB,MAAM,CAACwB,QAAQ,EAAE,CAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC,CAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC,CACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAW,eAAe,CAAIC,CAAC,EAAK,CAC7BrB,aAAa,CAAC,IAAI,CAAC,CACnBqB,CAAC,CAACC,cAAc,CAAC,CAAC,CACpB,CAAC,CAED,KAAM,CAAAC,eAAe,CAAIF,CAAC,EAAK,CAC7B,GAAI,CAACtB,UAAU,EAAI,CAACM,YAAY,CAACmB,OAAO,CAAE,OAE1C,KAAM,CAAAC,IAAI,CAAGpB,YAAY,CAACmB,OAAO,CAACE,qBAAqB,CAAC,CAAC,CACzD,KAAM,CAAAC,CAAC,CAAGN,CAAC,CAACO,OAAO,CAAGH,IAAI,CAACI,IAAI,CAC/B,KAAM,CAAAC,UAAU,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAAC,GAAG,CAAGN,CAAC,CAAGF,IAAI,CAACS,KAAK,CAAI,GAAG,CAAC,CAAC,CACrEpC,gBAAgB,CAACgC,UAAU,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAK,aAAa,CAAGA,CAAA,GAAM,CAC1BnC,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAEDhB,SAAS,CAAC,IAAM,CACd,GAAIe,UAAU,CAAE,CACdW,QAAQ,CAAC0B,gBAAgB,CAAC,WAAW,CAAEb,eAAe,CAAC,CACvDb,QAAQ,CAAC0B,gBAAgB,CAAC,SAAS,CAAED,aAAa,CAAC,CACnD,MAAO,IAAM,CACXzB,QAAQ,CAAC2B,mBAAmB,CAAC,WAAW,CAAEd,eAAe,CAAC,CAC1Db,QAAQ,CAAC2B,mBAAmB,CAAC,SAAS,CAAEF,aAAa,CAAC,CACxD,CAAC,CACH,CACF,CAAC,CAAE,CAACpC,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAuC,UAAU,CAAGA,CAAA,GAAM,CACvBxC,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,mBACEV,KAAA,QAAKmD,KAAK,CAAE,CACVL,KAAK,CAAE,MAAM,CACbM,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,SACT,CAAE,CAAAC,QAAA,eAEAzD,KAAA,QAAKmD,KAAK,CAAE,CACVC,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,SAAS,CAC1BG,YAAY,CAAE,gBAAgB,CAC9BL,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,QAAQ,CACjBC,GAAG,CAAE,MACP,CAAE,CAAAJ,QAAA,eACAzD,KAAA,QAAKmD,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEQ,GAAG,CAAE,KAAM,CAAE,CAAAJ,QAAA,eAC1C3D,IAAA,WACEgE,OAAO,CAAEA,CAAA,GAAMhD,WAAW,CAAC,OAAO,CAAE,CACpCqC,KAAK,CAAE,CACLS,OAAO,CAAE,SAAS,CAClBL,eAAe,CAAE1C,QAAQ,GAAK,OAAO,CAAG,SAAS,CAAG,MAAM,CAC1D2C,KAAK,CAAE,OAAO,CACdO,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,CACH,0BAED,CAAQ,CAAC,cACT3D,IAAA,WACEgE,OAAO,CAAEA,CAAA,GAAMhD,WAAW,CAAC,cAAc,CAAE,CAC3CqC,KAAK,CAAE,CACLS,OAAO,CAAE,SAAS,CAClBL,eAAe,CAAE1C,QAAQ,GAAK,cAAc,CAAG,SAAS,CAAG,MAAM,CACjE2C,KAAK,CAAE,OAAO,CACdO,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,CACH,0BAED,CAAQ,CAAC,CACR5C,QAAQ,GAAK,OAAO,eACnBf,IAAA,WACEgE,OAAO,CAAEZ,UAAW,CACpBC,KAAK,CAAE,CACLS,OAAO,CAAE,SAAS,CAClBL,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdO,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,CACH,cAED,CAAQ,CACT,EACE,CAAC,cAEN3D,IAAA,QAAKqD,KAAK,CAAE,CAAEgB,IAAI,CAAE,CAAE,CAAE,CAAM,CAAC,cAE/BrE,IAAA,WACEgE,OAAO,CAAEA,CAAA,GAAM9C,aAAa,CAAC,CAACD,UAAU,CAAE,CAC1CoC,KAAK,CAAE,CACLS,OAAO,CAAE,SAAS,CAClBL,eAAe,CAAExC,UAAU,CAAG,SAAS,CAAG,MAAM,CAChDyC,KAAK,CAAE,OAAO,CACdO,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,CACH,cAED,CAAQ,CAAC,cAET3D,IAAA,WACEgE,OAAO,CAAE1C,aAAc,CACvB+B,KAAK,CAAE,CACLS,OAAO,CAAE,SAAS,CAClBL,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdO,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,CACH,cAED,CAAQ,CAAC,EACN,CAAC,CAGL1C,UAAU,EAAIZ,MAAM,CAACiE,WAAW,eAC/BpE,KAAA,QAAKmD,KAAK,CAAE,CACVI,eAAe,CAAE,SAAS,CAC1BG,YAAY,CAAE,gBAAgB,CAC9BE,OAAO,CAAE,WAAW,CACpBM,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,eACA3D,IAAA,QAAKqD,KAAK,CAAE,CAAEkB,UAAU,CAAE,KAAK,CAAEC,YAAY,CAAE,KAAK,CAAEd,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,0BAAI,CAAK,CAAC,cACjFzD,KAAA,QAAKmD,KAAK,CAAE,CACVE,OAAO,CAAE,MAAM,CACfkB,mBAAmB,CAAE,sCAAsC,CAC3DV,GAAG,CAAE,KAAK,CACVL,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,eACAzD,KAAA,QAAAyD,QAAA,EAAK,gBAAI,CAACtD,MAAM,CAACiE,WAAW,CAACI,KAAK,CAAC,GAAC,EAAK,CAAC,cAC1CxE,KAAA,QAAAyD,QAAA,EAAK,gBAAI,CAACtD,MAAM,CAACiE,WAAW,CAACK,cAAc,CAAG,YAAY,CAAG,MAAM,EAAM,CAAC,cAC1EzE,KAAA,QAAAyD,QAAA,EAAK,gBAAI,CAAC,CAACtD,MAAM,CAACiE,WAAW,CAACM,UAAU,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,cAClE3E,KAAA,QAAAyD,QAAA,EAAK,gBAAI,CAACtD,MAAM,CAACiE,WAAW,CAACQ,SAAS,EAAM,CAAC,cAC7C5E,KAAA,QAAAyD,QAAA,EAAK,sBAAK,CAACtD,MAAM,CAACiE,WAAW,CAACS,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cAC1D3E,KAAA,QAAAyD,QAAA,EAAK,sBAAK,CAACtD,MAAM,CAACiE,WAAW,CAACU,QAAQ,CAACH,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cACxD3E,KAAA,QAAAyD,QAAA,EAAK,gBAAI,CAACtD,MAAM,CAACiE,WAAW,CAACW,UAAU,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAE5E,MAAM,CAACiE,WAAW,CAACW,UAAU,EAAM,CAAC,cAC5F/E,KAAA,QAAAyD,QAAA,EAAK,gBAAI,CAAC,CAACtD,MAAM,CAACiE,WAAW,CAACY,MAAM,CAAG,GAAG,EAAEL,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,EAC3D,CAAC,EACH,CACN,cAGD7E,IAAA,QAAKqD,KAAK,CAAE,CAAEgB,IAAI,CAAE,CAAC,CAAEc,QAAQ,CAAE,UAAU,CAAEC,QAAQ,CAAE,QAAS,CAAE,CAAAzB,QAAA,CAC/D5C,QAAQ,GAAK,OAAO,cACnB;AACAb,KAAA,QACEmF,GAAG,CAAElE,YAAa,CAClBkC,KAAK,CAAE,CACLL,KAAK,CAAE,MAAM,CACbM,MAAM,CAAE,MAAM,CACd6B,QAAQ,CAAE,UAAU,CACpBhB,MAAM,CAAEtD,UAAU,CAAG,WAAW,CAAG,SAAS,CAC5CyE,UAAU,CAAE,MAAM,CAClB/B,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpB0B,cAAc,CAAE,QAClB,CAAE,CAAA5B,QAAA,EAEDrD,aAAa,eACZJ,KAAA,QAAKmD,KAAK,CAAE,CACV8B,QAAQ,CAAE,UAAU,CACpBK,QAAQ,CAAE,KAAK,CACfC,SAAS,CAAE,KAAK,CAChBlC,OAAO,CAAE,cACX,CAAE,CAAAI,QAAA,EAEClD,UAAU,cACTT,IAAA,QAAKqD,KAAK,CAAE,CACVL,KAAK,CAAE,OAAO,CACdM,MAAM,CAAE,OAAO,CACfW,MAAM,CAAE,oBAAoB,CAC5BC,YAAY,CAAE,KAAK,CACnBX,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpB0B,cAAc,CAAE,QAAQ,CACxB7B,KAAK,CAAE,SAAS,CAChBD,eAAe,CAAE,SACnB,CAAE,CAAAE,QAAA,CAAC,kDAEH,CAAK,CAAC,cAEN3D,IAAA,QACE0F,GAAG,CAAE,gCAAgCrF,MAAM,CAACsB,YAAY,MAAMgE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CAC3EC,GAAG,CAAC,0BAAM,CACVC,MAAM,CAAE1E,eAAgB,CACxB2E,OAAO,CAAE1E,gBAAiB,CAC1BgC,KAAK,CAAE,CACLmC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,MAAM,CACjBxB,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBX,OAAO,CAAE,OAAO,CAChByC,OAAO,CAAEzF,WAAW,CAAG,CAAC,CAAG,GAC7B,CAAE,CACH,CACF,cAGDP,IAAA,QACEqD,KAAK,CAAE,CACL8B,QAAQ,CAAE,UAAU,CACpBc,GAAG,CAAE,CAAC,CACNtD,IAAI,CAAE,CAAC,CACPK,KAAK,CAAE,GAAGrC,aAAa,GAAG,CAC1B2C,MAAM,CAAE,MAAM,CACd8B,QAAQ,CAAE,QAAQ,CAClBlB,YAAY,CAAE,aAChB,CAAE,CAAAP,QAAA,cAEF3D,IAAA,QACE0F,GAAG,CAAEpF,aAAc,CACnBuF,GAAG,CAAC,0BAAM,CACVxC,KAAK,CAAE,CACLL,KAAK,CAAE,GAAG,GAAG,CAAG,GAAG,CAAGrC,aAAa,GAAG,CACtC2C,MAAM,CAAE,MAAM,CACd4C,SAAS,CAAE,OAAO,CAClBjC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CACC,CAAC,cAGNlE,IAAA,QACEqD,KAAK,CAAE,CACL8B,QAAQ,CAAE,UAAU,CACpBc,GAAG,CAAE,CAAC,CACNtD,IAAI,CAAE,GAAGhC,aAAa,GAAG,CACzBqC,KAAK,CAAE,KAAK,CACZM,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,SAAS,CAC1BU,MAAM,CAAE,WAAW,CACnBgC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,EACV,CAAE,CACFC,WAAW,CAAEnE,eAAgB,CAAAyB,QAAA,cAG7B3D,IAAA,QACEqD,KAAK,CAAE,CACL8B,QAAQ,CAAE,UAAU,CACpBc,GAAG,CAAE,KAAK,CACVtD,IAAI,CAAE,KAAK,CACXwD,SAAS,CAAE,uBAAuB,CAClCnD,KAAK,CAAE,MAAM,CACbM,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,SAAS,CAC1BS,YAAY,CAAE,KAAK,CACnBX,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpB0B,cAAc,CAAE,QAAQ,CACxB7B,KAAK,CAAE,OAAO,CACdU,QAAQ,CAAE,MAAM,CAChBG,UAAU,CAAE,MACd,CAAE,CAAAZ,QAAA,CACH,QAED,CAAK,CAAC,CACH,CAAC,CAEL,CAACpD,WAAW,EAAI,CAACE,UAAU,eAC1BT,IAAA,QAAKqD,KAAK,CAAE,CACV8B,QAAQ,CAAE,UAAU,CACpBc,GAAG,CAAE,KAAK,CACVtD,IAAI,CAAE,KAAK,CACXwD,SAAS,CAAE,uBAAuB,CAClCzC,KAAK,CAAE,MAAM,CACbD,eAAe,CAAE,iBAAiB,CAClCK,OAAO,CAAE,MAAM,CACfI,YAAY,CAAE,KAChB,CAAE,CAAAP,QAAA,CAAC,uBAEH,CAAK,CACN,EACE,CACN,cAGDzD,KAAA,QAAKmD,KAAK,CAAE,CACV8B,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,MAAM,CACd3D,IAAI,CAAE,KAAK,CACXwD,SAAS,CAAE,kBAAkB,CAC7B1C,eAAe,CAAE,iBAAiB,CAClCC,KAAK,CAAE,OAAO,CACdI,OAAO,CAAE,SAAS,CAClBI,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAAT,QAAA,EAAC,sFACkB,CAAChD,aAAa,CAACkE,OAAO,CAAC,CAAC,CAAC,CAAC,GAC/C,EAAK,CAAC,EACH,CAAC,cAEN;AACA3E,KAAA,QAAKmD,KAAK,CAAE,CACVE,OAAO,CAAE,MAAM,CACfQ,GAAG,CAAE,MAAM,CACXD,OAAO,CAAE,MAAM,CACfR,MAAM,CAAE,MAAM,CACdO,UAAU,CAAE,QAAQ,CACpB0B,cAAc,CAAE,QAClB,CAAE,CAAA5B,QAAA,EAECrD,aAAa,eACZJ,KAAA,QAAKmD,KAAK,CAAE,CAAEgB,IAAI,CAAE,CAAC,CAAEkC,SAAS,CAAE,QAAS,CAAE,CAAA5C,QAAA,eAC3C3D,IAAA,QAAKqD,KAAK,CAAE,CAAEmB,YAAY,CAAE,KAAK,CAAEJ,QAAQ,CAAE,MAAM,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,0BAAI,CAAK,CAAC,cAChF3D,IAAA,QACE0F,GAAG,CAAEpF,aAAc,CACnBuF,GAAG,CAAC,0BAAM,CACVxC,KAAK,CAAE,CACLmC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,OAAO,CAClBxB,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBgC,SAAS,CAAE,SACb,CAAE,CACH,CAAC,EACC,CACN,cAGDhG,KAAA,QAAKmD,KAAK,CAAE,CAAEgB,IAAI,CAAE,CAAC,CAAEkC,SAAS,CAAE,QAAS,CAAE,CAAA5C,QAAA,eAC3C3D,IAAA,QAAKqD,KAAK,CAAE,CAAEmB,YAAY,CAAE,KAAK,CAAEJ,QAAQ,CAAE,MAAM,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,0BAAI,CAAK,CAAC,CAE/ElD,UAAU,cACTT,IAAA,QAAKqD,KAAK,CAAE,CACVL,KAAK,CAAE,MAAM,CACbM,MAAM,CAAE,OAAO,CACfW,MAAM,CAAE,oBAAoB,CAC5BC,YAAY,CAAE,KAAK,CACnBX,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpB0B,cAAc,CAAE,QAAQ,CACxB7B,KAAK,CAAE,SAAS,CAChBD,eAAe,CAAE,SACnB,CAAE,CAAAE,QAAA,CAAC,sCAEH,CAAK,CAAC,cAEN3D,IAAA,QACE0F,GAAG,CAAE,gCAAgCrF,MAAM,CAACsB,YAAY,MAAMgE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CAC3EC,GAAG,CAAC,0BAAM,CACVC,MAAM,CAAE1E,eAAgB,CACxB2E,OAAO,CAAE1E,gBAAiB,CAC1BgC,KAAK,CAAE,CACLmC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,OAAO,CAClBxB,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBgC,SAAS,CAAE,SAAS,CACpB3C,OAAO,CAAEhD,WAAW,CAAG,OAAO,CAAG,MACnC,CAAE,CACH,CACF,CAEA,CAACA,WAAW,EAAI,CAACE,UAAU,eAC1BT,IAAA,QAAKqD,KAAK,CAAE,CACVL,KAAK,CAAE,MAAM,CACbM,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpB0B,cAAc,CAAE,QAAQ,CACxBtB,MAAM,CAAE,iBAAiB,CACzBC,YAAY,CAAE,KAAK,CACnBR,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,CAAC,uBAEH,CAAK,CACN,EACE,CAAC,EACH,CACN,CACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}