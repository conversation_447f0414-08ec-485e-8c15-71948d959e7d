{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\n// import UploadForm from './UploadForm';\n// import ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = e => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [{\n      label: '打开图像 (Ctrl+O)',\n      action: () => {\n        var _fileInputRef$current;\n        return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n      }\n    }, {\n      label: '保存结果 (Ctrl+S)',\n      action: downloadImage,\n      disabled: !result\n    }, {\n      label: '重置 (Ctrl+R)',\n      action: handleReset\n    }],\n    edit: [{\n      label: '撤销',\n      action: () => {},\n      disabled: true\n    }, {\n      label: '重做',\n      action: () => {},\n      disabled: true\n    }],\n    view: [{\n      label: '放大 (Ctrl+=)',\n      action: () => setZoomLevel(prev => Math.min(prev + 25, 400))\n    }, {\n      label: '缩小 (Ctrl+-)',\n      action: () => setZoomLevel(prev => Math.max(prev - 25, 25))\n    }, {\n      label: '实际大小 (Ctrl+0)',\n      action: () => setZoomLevel(100)\n    }],\n    tools: [{\n      label: '批量处理',\n      action: () => {},\n      disabled: true\n    }, {\n      label: '设置',\n      action: () => {},\n      disabled: true\n    }],\n    help: [{\n      label: '快捷键',\n      action: () => alert('快捷键:\\nCtrl+O: 打开文件\\nCtrl+S: 保存结果\\nCtrl+R: 重置\\nCtrl+=: 放大\\nCtrl+-: 缩小\\nCtrl+0: 实际大小')\n    }, {\n      label: '关于',\n      action: () => alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术')\n    }]\n  };\n  const handleUpload = async formData => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比和获取图像信息\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        setOriginalImage(e.target.result);\n        // 获取图像信息\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n  };\n  const downloadImage = () => {\n    if (result) {\n      const link = document.createElement('a');\n      link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n      link.download = `enhanced_${result.filename}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleMenuClick = menuKey => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n  const handleMenuItemClick = action => {\n    action();\n    setShowMenu(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: \"image/*\",\n      style: {\n        display: 'none'\n      },\n      onChange: e => {\n        const file = e.target.files[0];\n        if (file) {\n          const formData = new FormData();\n          formData.append('file', file);\n          formData.append('params', JSON.stringify({\n            scale: 4,\n            use_realesrgan: true,\n            sharpening: 0.0,\n            denoising: 0,\n            saturation: 1.0,\n            contrast: 1.0,\n            brightness: 0,\n            beauty: 0.0\n          }));\n          handleUpload(formData);\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff',\n        position: 'relative'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          fontSize: '13px'\n        },\n        children: Object.keys(menuItems).map(menuKey => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              cursor: 'pointer',\n              padding: '5px 10px',\n              borderRadius: '4px',\n              backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n            },\n            onClick: () => handleMenuClick(menuKey),\n            children: menuKey === 'file' ? '文件' : menuKey === 'edit' ? '编辑' : menuKey === 'view' ? '视图' : menuKey === 'tools' ? '工具' : '帮助'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), showMenu === menuKey && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '100%',\n              left: 0,\n              backgroundColor: '#3c3c3c',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              minWidth: '150px',\n              zIndex: 1000,\n              boxShadow: '0 4px 8px rgba(0,0,0,0.3)'\n            },\n            children: menuItems[menuKey].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '8px 12px',\n                cursor: item.disabled ? 'not-allowed' : 'pointer',\n                color: item.disabled ? '#888' : '#e0e0e0',\n                fontSize: '12px',\n                borderBottom: index < menuItems[menuKey].length - 1 ? '1px solid #555' : 'none'\n              },\n              onClick: () => !item.disabled && handleMenuItemClick(item.action),\n              onMouseEnter: e => {\n                if (!item.disabled) {\n                  e.target.style.backgroundColor = '#4a90e2';\n                }\n              },\n              onMouseLeave: e => {\n                e.target.style.backgroundColor = 'transparent';\n              },\n              children: item.label\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this)]\n        }, menuKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u6253\\u5F00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), result && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: downloadImage,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#17a2b8',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u4FDD\\u5B58\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(prev => Math.max(prev - 25, 25)),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#ccc',\n                fontSize: '11px',\n                minWidth: '40px',\n                textAlign: 'center'\n              },\n              children: [zoomLevel, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(prev => Math.min(prev + 25, 400)),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ccc',\n              fontSize: '12px'\n            },\n            children: result ? '已处理' : isLoading ? '处理中...' : '等待上传图像'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [\"\\u9519\\u8BEF: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '8px',\n              textAlign: 'center',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '30px',\n                height: '30px',\n                border: '3px solid #333',\n                borderTop: '3px solid #4a90e2',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite',\n                margin: '0 auto'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), result ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              transform: `scale(${zoomLevel / 100})`,\n              transformOrigin: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(ResultView, {\n              result: result,\n              originalImage: originalImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                marginTop: '10px',\n                color: '#666'\n              },\n              children: \"\\u6216\\u6309 Ctrl+O \\u6253\\u5F00\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: isLoading ? '处理中...' : '就绪'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this), imageInfo && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: imageInfo.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: imageInfo.dimensions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: imageInfo.size\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), processingTime && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u5904\\u7406\\u65F6\\u95F4: \", processingTime, \"s\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"RealESRGAN v2.1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      global: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"9LSDy8f3lHfFase4IRus21qnkXA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "showMenu", "setShowMenu", "imageInfo", "setImageInfo", "processingTime", "setProcessingTime", "zoomLevel", "setZoomLevel", "fileInputRef", "handleKeyDown", "e", "ctrl<PERSON>ey", "key", "preventDefault", "current", "click", "handleReset", "downloadImage", "prev", "Math", "min", "max", "document", "addEventListener", "removeEventListener", "menuItems", "file", "label", "action", "_fileInputRef$current", "disabled", "edit", "view", "tools", "help", "alert", "handleUpload", "formData", "startTime", "Date", "now", "get", "reader", "FileReader", "onload", "target", "img", "Image", "name", "size", "toFixed", "dimensions", "width", "height", "type", "src", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "endTime", "err", "console", "message", "link", "createElement", "href", "enhanced_url", "download", "filename", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handleMenuClick", "<PERSON><PERSON>ey", "handleMenuItemClick", "style", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "ref", "accept", "onChange", "files", "FormData", "append", "JSON", "stringify", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderBottom", "alignItems", "padding", "color", "position", "gap", "marginRight", "borderRadius", "flex", "textAlign", "fontSize", "fontWeight", "Object", "keys", "map", "cursor", "onClick", "top", "left", "border", "min<PERSON><PERSON><PERSON>", "zIndex", "boxShadow", "item", "index", "length", "onMouseEnter", "onMouseLeave", "_fileInputRef$current2", "justifyContent", "right", "transform", "marginBottom", "borderTop", "animation", "margin", "transform<PERSON><PERSON>in", "ResultView", "marginTop", "borderLeft", "UploadForm", "onUpload", "jsx", "global", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n// import UploadForm from './UploadForm';\n// import ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [\n      { label: '打开图像 (Ctrl+O)', action: () => fileInputRef.current?.click() },\n      { label: '保存结果 (Ctrl+S)', action: downloadImage, disabled: !result },\n      { label: '重置 (Ctrl+R)', action: handleReset }\n    ],\n    edit: [\n      { label: '撤销', action: () => {}, disabled: true },\n      { label: '重做', action: () => {}, disabled: true }\n    ],\n    view: [\n      { label: '放大 (Ctrl+=)', action: () => setZoomLevel(prev => Math.min(prev + 25, 400)) },\n      { label: '缩小 (Ctrl+-)', action: () => setZoomLevel(prev => Math.max(prev - 25, 25)) },\n      { label: '实际大小 (Ctrl+0)', action: () => setZoomLevel(100) }\n    ],\n    tools: [\n      { label: '批量处理', action: () => {}, disabled: true },\n      { label: '设置', action: () => {}, disabled: true }\n    ],\n    help: [\n      { label: '快捷键', action: () => alert('快捷键:\\nCtrl+O: 打开文件\\nCtrl+S: 保存结果\\nCtrl+R: 重置\\nCtrl+=: 放大\\nCtrl+-: 缩小\\nCtrl+0: 实际大小') },\n      { label: '关于', action: () => alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术') }\n    ]\n  };\n\n  const handleUpload = async (formData) => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比和获取图像信息\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setOriginalImage(e.target.result);\n        // 获取图像信息\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n  };\n\n  const downloadImage = () => {\n    if (result) {\n      const link = document.createElement('a');\n      link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n      link.download = `enhanced_${result.filename}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleMenuClick = (menuKey) => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n\n  const handleMenuItemClick = (action) => {\n    action();\n    setShowMenu(null);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 隐藏的文件输入 */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/*\"\n        style={{ display: 'none' }}\n        onChange={(e) => {\n          const file = e.target.files[0];\n          if (file) {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('params', JSON.stringify({\n              scale: 4,\n              use_realesrgan: true,\n              sharpening: 0.0,\n              denoising: 0,\n              saturation: 1.0,\n              contrast: 1.0,\n              brightness: 0,\n              beauty: 0.0\n            }));\n            handleUpload(formData);\n          }\n        }}\n      />\n\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff',\n        position: 'relative'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          {Object.keys(menuItems).map(menuKey => (\n            <div key={menuKey} style={{ position: 'relative' }}>\n              <span \n                style={{ \n                  cursor: 'pointer', \n                  padding: '5px 10px', \n                  borderRadius: '4px',\n                  backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n                }}\n                onClick={() => handleMenuClick(menuKey)}\n              >\n                {menuKey === 'file' ? '文件' : \n                 menuKey === 'edit' ? '编辑' :\n                 menuKey === 'view' ? '视图' :\n                 menuKey === 'tools' ? '工具' : '帮助'}\n              </span>\n              \n              {/* 下拉菜单 */}\n              {showMenu === menuKey && (\n                <div style={{\n                  position: 'absolute',\n                  top: '100%',\n                  left: 0,\n                  backgroundColor: '#3c3c3c',\n                  border: '1px solid #555',\n                  borderRadius: '4px',\n                  minWidth: '150px',\n                  zIndex: 1000,\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.3)'\n                }}>\n                  {menuItems[menuKey].map((item, index) => (\n                    <div\n                      key={index}\n                      style={{\n                        padding: '8px 12px',\n                        cursor: item.disabled ? 'not-allowed' : 'pointer',\n                        color: item.disabled ? '#888' : '#e0e0e0',\n                        fontSize: '12px',\n                        borderBottom: index < menuItems[menuKey].length - 1 ? '1px solid #555' : 'none'\n                      }}\n                      onClick={() => !item.disabled && handleMenuItemClick(item.action)}\n                      onMouseEnter={(e) => {\n                        if (!item.disabled) {\n                          e.target.style.backgroundColor = '#4a90e2';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.backgroundColor = 'transparent';\n                      }}\n                    >\n                      {item.label}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              新建\n            </button>\n            \n            <button\n              onClick={() => fileInputRef.current?.click()}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              打开\n            </button>\n            \n            {result && (\n              <button\n                onClick={downloadImage}\n                style={{\n                  padding: '6px 12px',\n                  backgroundColor: '#17a2b8',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                  fontSize: '12px'\n                }}\n              >\n                保存\n              </button>\n            )}\n            \n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n            \n            {/* 缩放控制 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>\n              <button\n                onClick={() => setZoomLevel(prev => Math.max(prev - 25, 25))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                -\n              </button>\n              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '40px', textAlign: 'center' }}>\n                {zoomLevel}%\n              </span>\n              <button\n                onClick={() => setZoomLevel(prev => Math.min(prev + 25, 400))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                +\n              </button>\n            </div>\n            \n            <div style={{ flex: 1 }}></div>\n            \n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {result ? '已处理' : isLoading ? '处理中...' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'center' }}>\n                <ResultView result={result} originalImage={originalImage} />\n              </div>\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n                <div style={{ fontSize: '14px', marginTop: '10px', color: '#666' }}>\n                  或按 Ctrl+O 打开文件\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>{isLoading ? '处理中...' : '就绪'}</span>\n        \n        {imageInfo && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>{imageInfo.name}</span>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>{imageInfo.dimensions}</span>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>{imageInfo.size}</span>\n          </>\n        )}\n        \n        {processingTime && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>处理时间: {processingTime}s</span>\n          </>\n        )}\n        \n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n\n      {/* CSS样式 */}\n      <style jsx global>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAMyB,YAAY,GAAGvB,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMyB,aAAa,GAAIC,CAAC,IAAK;MAC3B;MACA,IAAIA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClB,IAAIL,YAAY,CAACM,OAAO,EAAE;UACxBN,YAAY,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9B;MACF;MACA;MACA,IAAIL,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBG,WAAW,CAAC,CAAC;MACf;MACA;MACA,IAAIN,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,IAAIpB,MAAM,EAAE;QACxCkB,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBI,aAAa,CAAC,CAAC;MACjB;MACA;MACA,IAAIP,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBN,YAAY,CAACW,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;MAChD;MACA;MACA,IAAIR,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBN,YAAY,CAACW,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;MAC/C;MACA;MACA,IAAIR,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBN,YAAY,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;IAEDe,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEd,aAAa,CAAC;IACnD,OAAO,MAAMa,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEf,aAAa,CAAC;EACrE,CAAC,EAAE,CAACjB,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMiC,SAAS,GAAG;IAChBC,IAAI,EAAE,CACJ;MAAEC,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA;QAAA,IAAAC,qBAAA;QAAA,QAAAA,qBAAA,GAAMrB,YAAY,CAACM,OAAO,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,KAAK,CAAC,CAAC;MAAA;IAAC,CAAC,EACvE;MAAEY,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEX,aAAa;MAAEa,QAAQ,EAAE,CAACtC;IAAO,CAAC,EACpE;MAAEmC,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEZ;IAAY,CAAC,CAC9C;IACDe,IAAI,EAAE,CACJ;MAAEJ,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,EACjD;MAAEH,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,CAClD;IACDE,IAAI,EAAE,CACJ;MAAEL,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMrB,YAAY,CAACW,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC;IAAE,CAAC,EACtF;MAAES,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMrB,YAAY,CAACW,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC;IAAE,CAAC,EACrF;MAAES,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA,KAAMrB,YAAY,CAAC,GAAG;IAAE,CAAC,CAC5D;IACD0B,KAAK,EAAE,CACL;MAAEN,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,EACnD;MAAEH,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,CAClD;IACDI,IAAI,EAAE,CACJ;MAAEP,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEA,CAAA,KAAMO,KAAK,CAAC,oFAAoF;IAAE,CAAC,EAC3H;MAAER,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAMO,KAAK,CAAC,+BAA+B;IAAE,CAAC;EAEzE,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B7C,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAM6B,IAAI,GAAGW,QAAQ,CAACI,GAAG,CAAC,MAAM,CAAC;IACjC,IAAIf,IAAI,EAAE;MACR,MAAMgB,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIlC,CAAC,IAAK;QACrBX,gBAAgB,CAACW,CAAC,CAACmC,MAAM,CAACrD,MAAM,CAAC;QACjC;QACA,MAAMsD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACF,MAAM,GAAG,MAAM;UACjBzC,YAAY,CAAC;YACX6C,IAAI,EAAEtB,IAAI,CAACsB,IAAI;YACfC,IAAI,EAAE,CAACvB,IAAI,CAACuB,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;YAClDC,UAAU,EAAE,GAAGL,GAAG,CAACM,KAAK,MAAMN,GAAG,CAACO,MAAM,EAAE;YAC1CC,IAAI,EAAE5B,IAAI,CAAC4B;UACb,CAAC,CAAC;QACJ,CAAC;QACDR,GAAG,CAACS,GAAG,GAAG7C,CAAC,CAACmC,MAAM,CAACrD,MAAM;MAC3B,CAAC;MACDkD,MAAM,CAACc,aAAa,CAAC9B,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAM+B,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEvB;MACR,CAAC,CAAC;MAEF,IAAI,CAACoB,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,MAAMI,OAAO,GAAG5B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1BnC,iBAAiB,CAAC,CAAC,CAAC8D,OAAO,GAAG7B,SAAS,IAAI,IAAI,EAAEY,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5DzD,SAAS,CAACyE,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACzE,KAAK,CAAC,OAAO,EAAEwE,GAAG,CAAC;MAC3BvE,QAAQ,CAACuE,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR3E,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMqB,WAAW,GAAGA,CAAA,KAAM;IACxBvB,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;IACtBI,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,YAAY,CAAC,GAAG,CAAC;EACnB,CAAC;EAED,MAAMU,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzB,MAAM,EAAE;MACV,MAAM+E,IAAI,GAAGjD,QAAQ,CAACkD,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,gCAAgCjF,MAAM,CAACkF,YAAY,EAAE;MACjEH,IAAI,CAACI,QAAQ,GAAG,YAAYnF,MAAM,CAACoF,QAAQ,EAAE;MAC7CtD,QAAQ,CAACsC,IAAI,CAACiB,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACxD,KAAK,CAAC,CAAC;MACZO,QAAQ,CAACsC,IAAI,CAACkB,WAAW,CAACP,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMQ,eAAe,GAAIC,OAAO,IAAK;IACnC/E,WAAW,CAACD,QAAQ,KAAKgF,OAAO,GAAG,IAAI,GAAGA,OAAO,CAAC;EACpD,CAAC;EAED,MAAMC,mBAAmB,GAAIrD,MAAM,IAAK;IACtCA,MAAM,CAAC,CAAC;IACR3B,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,oBACEd,OAAA;IAAK+F,KAAK,EAAE;MACV7B,MAAM,EAAE,OAAO;MACf8B,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEArG,OAAA;MACEsG,GAAG,EAAEjF,YAAa;MAClB8C,IAAI,EAAC,MAAM;MACXoC,MAAM,EAAC,SAAS;MAChBR,KAAK,EAAE;QAAEG,OAAO,EAAE;MAAO,CAAE;MAC3BM,QAAQ,EAAGjF,CAAC,IAAK;QACf,MAAMgB,IAAI,GAAGhB,CAAC,CAACmC,MAAM,CAAC+C,KAAK,CAAC,CAAC,CAAC;QAC9B,IAAIlE,IAAI,EAAE;UACR,MAAMW,QAAQ,GAAG,IAAIwD,QAAQ,CAAC,CAAC;UAC/BxD,QAAQ,CAACyD,MAAM,CAAC,MAAM,EAAEpE,IAAI,CAAC;UAC7BW,QAAQ,CAACyD,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC;YACvCC,KAAK,EAAE,CAAC;YACRC,cAAc,EAAE,IAAI;YACpBC,UAAU,EAAE,GAAG;YACfC,SAAS,EAAE,CAAC;YACZC,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,GAAG;YACbC,UAAU,EAAE,CAAC;YACbC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;UACHpE,YAAY,CAACC,QAAQ,CAAC;QACxB;MACF;IAAE;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFzH,OAAA;MAAK+F,KAAK,EAAE;QACV7B,MAAM,EAAE,MAAM;QACd8B,eAAe,EAAE,SAAS;QAC1B0B,YAAY,EAAE,gBAAgB;QAC9BxB,OAAO,EAAE,MAAM;QACfyB,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAzB,QAAA,gBAEArG,OAAA;QAAK+F,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE6B,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAA3B,QAAA,gBAC/DrG,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAE+D,YAAY,EAAE,KAAK;YAAEjC,eAAe,EAAE;UAAU;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGzH,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAE+D,YAAY,EAAE,KAAK;YAAEjC,eAAe,EAAE;UAAU;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGzH,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAE+D,YAAY,EAAE,KAAK;YAAEjC,eAAe,EAAE;UAAU;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNzH,OAAA;QAAK+F,KAAK,EAAE;UACVmC,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBR,KAAK,EAAE;QACT,CAAE;QAAAxB,QAAA,EAAC;MAEH;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNzH,OAAA;QAAK+F,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE6B,GAAG,EAAE,MAAM;UAAEK,QAAQ,EAAE;QAAO,CAAE;QAAA/B,QAAA,EAC5DiC,MAAM,CAACC,IAAI,CAACjG,SAAS,CAAC,CAACkG,GAAG,CAAC3C,OAAO,iBACjC7F,OAAA;UAAmB+F,KAAK,EAAE;YAAE+B,QAAQ,EAAE;UAAW,CAAE;UAAAzB,QAAA,gBACjDrG,OAAA;YACE+F,KAAK,EAAE;cACL0C,MAAM,EAAE,SAAS;cACjBb,OAAO,EAAE,UAAU;cACnBK,YAAY,EAAE,KAAK;cACnBjC,eAAe,EAAEnF,QAAQ,KAAKgF,OAAO,GAAG,SAAS,GAAG;YACtD,CAAE;YACF6C,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAACC,OAAO,CAAE;YAAAQ,QAAA,EAEvCR,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,OAAO,GAAG,IAAI,GAAG;UAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EAGN5G,QAAQ,KAAKgF,OAAO,iBACnB7F,OAAA;YAAK+F,KAAK,EAAE;cACV+B,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACP5C,eAAe,EAAE,SAAS;cAC1B6C,MAAM,EAAE,gBAAgB;cACxBZ,YAAY,EAAE,KAAK;cACnBa,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE,IAAI;cACZC,SAAS,EAAE;YACb,CAAE;YAAA3C,QAAA,EACC/D,SAAS,CAACuD,OAAO,CAAC,CAAC2C,GAAG,CAAC,CAACS,IAAI,EAAEC,KAAK,kBAClClJ,OAAA;cAEE+F,KAAK,EAAE;gBACL6B,OAAO,EAAE,UAAU;gBACnBa,MAAM,EAAEQ,IAAI,CAACtG,QAAQ,GAAG,aAAa,GAAG,SAAS;gBACjDkF,KAAK,EAAEoB,IAAI,CAACtG,QAAQ,GAAG,MAAM,GAAG,SAAS;gBACzCyF,QAAQ,EAAE,MAAM;gBAChBV,YAAY,EAAEwB,KAAK,GAAG5G,SAAS,CAACuD,OAAO,CAAC,CAACsD,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG;cAC3E,CAAE;cACFT,OAAO,EAAEA,CAAA,KAAM,CAACO,IAAI,CAACtG,QAAQ,IAAImD,mBAAmB,CAACmD,IAAI,CAACxG,MAAM,CAAE;cAClE2G,YAAY,EAAG7H,CAAC,IAAK;gBACnB,IAAI,CAAC0H,IAAI,CAACtG,QAAQ,EAAE;kBAClBpB,CAAC,CAACmC,MAAM,CAACqC,KAAK,CAACC,eAAe,GAAG,SAAS;gBAC5C;cACF,CAAE;cACFqD,YAAY,EAAG9H,CAAC,IAAK;gBACnBA,CAAC,CAACmC,MAAM,CAACqC,KAAK,CAACC,eAAe,GAAG,aAAa;cAChD,CAAE;cAAAK,QAAA,EAED4C,IAAI,CAACzG;YAAK,GAlBN0G,KAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,GArDO5B,OAAO;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzH,OAAA;MAAK+F,KAAK,EAAE;QACVmC,IAAI,EAAE,CAAC;QACPhC,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEArG,OAAA;QAAK+F,KAAK,EAAE;UACVmC,IAAI,EAAE,CAAC;UACPlC,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvB2B,QAAQ,EAAE;QACZ,CAAE;QAAAzB,QAAA,gBAEArG,OAAA;UAAK+F,KAAK,EAAE;YACV7B,MAAM,EAAE,MAAM;YACd8B,eAAe,EAAE,MAAM;YACvB0B,YAAY,EAAE,gBAAgB;YAC9BxB,OAAO,EAAE,MAAM;YACfyB,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBG,GAAG,EAAE;UACP,CAAE;UAAA1B,QAAA,gBACArG,OAAA;YACE0I,OAAO,EAAE7G,WAAY;YACrBkE,KAAK,EAAE;cACL6B,OAAO,EAAE,UAAU;cACnB5B,eAAe,EAAE,SAAS;cAC1B6B,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBQ,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETzH,OAAA;YACE0I,OAAO,EAAEA,CAAA;cAAA,IAAAY,sBAAA;cAAA,QAAAA,sBAAA,GAAMjI,YAAY,CAACM,OAAO,cAAA2H,sBAAA,uBAApBA,sBAAA,CAAsB1H,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CmE,KAAK,EAAE;cACL6B,OAAO,EAAE,UAAU;cACnB5B,eAAe,EAAE,SAAS;cAC1B6B,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBQ,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERpH,MAAM,iBACLL,OAAA;YACE0I,OAAO,EAAE5G,aAAc;YACvBiE,KAAK,EAAE;cACL6B,OAAO,EAAE,UAAU;cACnB5B,eAAe,EAAE,SAAS;cAC1B6B,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBQ,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAEDzH,OAAA;YAAK+F,KAAK,EAAE;cAAE9B,KAAK,EAAE,KAAK;cAAEC,MAAM,EAAE,MAAM;cAAE8B,eAAe,EAAE;YAAO;UAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG7EzH,OAAA;YAAK+F,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEyB,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAA1B,QAAA,gBAChErG,OAAA;cACE0I,OAAO,EAAEA,CAAA,KAAMtH,YAAY,CAACW,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAE;cAC7DgE,KAAK,EAAE;gBACL6B,OAAO,EAAE,SAAS;gBAClB5B,eAAe,EAAE,SAAS;gBAC1B6B,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBL,QAAQ,EAAE;cACZ,CAAE;cAAA/B,QAAA,EACH;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzH,OAAA;cAAM+F,KAAK,EAAE;gBAAE8B,KAAK,EAAE,MAAM;gBAAEO,QAAQ,EAAE,MAAM;gBAAEU,QAAQ,EAAE,MAAM;gBAAEX,SAAS,EAAE;cAAS,CAAE;cAAA9B,QAAA,GACrFlF,SAAS,EAAC,GACb;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzH,OAAA;cACE0I,OAAO,EAAEA,CAAA,KAAMtH,YAAY,CAACW,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAE;cAC9DgE,KAAK,EAAE;gBACL6B,OAAO,EAAE,SAAS;gBAClB5B,eAAe,EAAE,SAAS;gBAC1B6B,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBL,QAAQ,EAAE;cACZ,CAAE;cAAA/B,QAAA,EACH;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENzH,OAAA;YAAK+F,KAAK,EAAE;cAAEmC,IAAI,EAAE;YAAE;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAE/BzH,OAAA;YAAM+F,KAAK,EAAE;cAAE8B,KAAK,EAAE,MAAM;cAAEO,QAAQ,EAAE;YAAO,CAAE;YAAA/B,QAAA,EAC9ChG,MAAM,GAAG,KAAK,GAAGE,SAAS,GAAG,QAAQ,GAAG;UAAQ;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzH,OAAA;UAAK+F,KAAK,EAAE;YACVmC,IAAI,EAAE,CAAC;YACPhC,OAAO,EAAE,MAAM;YACfyB,UAAU,EAAE,QAAQ;YACpB4B,cAAc,EAAE,QAAQ;YACxB3B,OAAO,EAAE,MAAM;YACfE,QAAQ,EAAE;UACZ,CAAE;UAAAzB,QAAA,GACC5F,KAAK,iBACJT,OAAA;YAAK+F,KAAK,EAAE;cACV+B,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZY,KAAK,EAAE,MAAM;cACbxD,eAAe,EAAE,SAAS;cAC1B6B,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBG,QAAQ,EAAE,MAAM;cAChBW,MAAM,EAAE;YACV,CAAE;YAAA1C,QAAA,GAAC,gBACG,EAAC5F,KAAK;UAAA;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,EAEAlH,SAAS,iBACRP,OAAA;YAAK+F,KAAK,EAAE;cACV+B,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXa,SAAS,EAAE,uBAAuB;cAClCzD,eAAe,EAAE,iBAAiB;cAClC6B,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBE,SAAS,EAAE,QAAQ;cACnBY,MAAM,EAAE;YACV,CAAE;YAAA1C,QAAA,gBACArG,OAAA;cAAK+F,KAAK,EAAE;gBAAE2D,YAAY,EAAE;cAAO,CAAE;cAAArD,QAAA,EAAC;YAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDzH,OAAA;cAAK+F,KAAK,EAAE;gBACV9B,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACd2E,MAAM,EAAE,gBAAgB;gBACxBc,SAAS,EAAE,mBAAmB;gBAC9B1B,YAAY,EAAE,KAAK;gBACnB2B,SAAS,EAAE,yBAAyB;gBACpCC,MAAM,EAAE;cACV;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEApH,MAAM,gBACLL,OAAA;YAAK+F,KAAK,EAAE;cAAE0D,SAAS,EAAE,SAAStI,SAAS,GAAG,GAAG,GAAG;cAAE2I,eAAe,EAAE;YAAS,CAAE;YAAAzD,QAAA,eAChFrG,OAAA,CAAC+J,UAAU;cAAC1J,MAAM,EAAEA,MAAO;cAACM,aAAa,EAAEA;YAAc;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,gBAENzH,OAAA;YAAK+F,KAAK,EAAE;cACVoC,SAAS,EAAE,QAAQ;cACnBN,KAAK,EAAE,MAAM;cACbO,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACArG,OAAA;cAAK+F,KAAK,EAAE;gBAAEqC,QAAQ,EAAE,MAAM;gBAAEsB,YAAY,EAAE;cAAO,CAAE;cAAArD,QAAA,EAAC;YAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEzH,OAAA;cAAAqG,QAAA,EAAK;YAAc;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzBzH,OAAA;cAAK+F,KAAK,EAAE;gBAAEqC,QAAQ,EAAE,MAAM;gBAAE4B,SAAS,EAAE,MAAM;gBAAEnC,KAAK,EAAE;cAAO,CAAE;cAAAxB,QAAA,EAAC;YAEpE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzH,OAAA;QAAK+F,KAAK,EAAE;UACV9B,KAAK,EAAE,OAAO;UACd+B,eAAe,EAAE,SAAS;UAC1BiE,UAAU,EAAE,gBAAgB;UAC5B/D,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEArG,OAAA;UAAK+F,KAAK,EAAE;YACV7B,MAAM,EAAE,MAAM;YACd8B,eAAe,EAAE,MAAM;YACvB0B,YAAY,EAAE,gBAAgB;YAC9BxB,OAAO,EAAE,MAAM;YACfyB,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBO,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAhC,QAAA,EAAC;QAEH;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNzH,OAAA;UAAK+F,KAAK,EAAE;YACVmC,IAAI,EAAE,CAAC;YACP9B,QAAQ,EAAE,MAAM;YAChBwB,OAAO,EAAE;UACX,CAAE;UAAAvB,QAAA,eACArG,OAAA,CAACkK,UAAU;YAACC,QAAQ,EAAElH,YAAa;YAAC1C,SAAS,EAAEA;UAAU;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzH,OAAA;MAAK+F,KAAK,EAAE;QACV7B,MAAM,EAAE,MAAM;QACd8B,eAAe,EAAE,MAAM;QACvB2D,SAAS,EAAE,gBAAgB;QAC3BzD,OAAO,EAAE,MAAM;QACfyB,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbO,QAAQ,EAAE;MACZ,CAAE;MAAA/B,QAAA,gBACArG,OAAA;QAAAqG,QAAA,EAAO9F,SAAS,GAAG,QAAQ,GAAG;MAAI;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAEzC1G,SAAS,iBACRf,OAAA,CAAAE,SAAA;QAAAmG,QAAA,gBACErG,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAE8B,eAAe,EAAE,MAAM;YAAE6D,MAAM,EAAE;UAAS;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FzH,OAAA;UAAAqG,QAAA,EAAOtF,SAAS,CAAC8C;QAAI;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7BzH,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAE8B,eAAe,EAAE,MAAM;YAAE6D,MAAM,EAAE;UAAS;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FzH,OAAA;UAAAqG,QAAA,EAAOtF,SAAS,CAACiD;QAAU;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnCzH,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAE8B,eAAe,EAAE,MAAM;YAAE6D,MAAM,EAAE;UAAS;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FzH,OAAA;UAAAqG,QAAA,EAAOtF,SAAS,CAAC+C;QAAI;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,eAC7B,CACH,EAEAxG,cAAc,iBACbjB,OAAA,CAAAE,SAAA;QAAAmG,QAAA,gBACErG,OAAA;UAAK+F,KAAK,EAAE;YAAE9B,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAE8B,eAAe,EAAE,MAAM;YAAE6D,MAAM,EAAE;UAAS;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FzH,OAAA;UAAAqG,QAAA,GAAM,4BAAM,EAACpF,cAAc,EAAC,GAAC;QAAA;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eACpC,CACH,eAEDzH,OAAA;QAAK+F,KAAK,EAAE;UAAEmC,IAAI,EAAE;QAAE;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BzH,OAAA;QAAAqG,QAAA,EAAM;MAAe;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNzH,OAAA;MAAOoK,GAAG;MAACC,MAAM;MAAAhE,QAAA,EAAE;AACzB;AACA;AACA;AACA;AACA;IAAO;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACrH,EAAA,CAniBQD,GAAG;AAAAmK,EAAA,GAAHnK,GAAG;AAqiBZ,eAAeA,GAAG;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}