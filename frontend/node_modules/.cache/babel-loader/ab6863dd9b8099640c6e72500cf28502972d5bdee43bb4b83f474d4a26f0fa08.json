{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const [viewMode, setViewMode] = useState('split'); // split, original, enhanced\n  const [rotation, setRotation] = useState(0);\n  const fileInputRef = useRef(null);\n  const handleUpload = async formData => {\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          fontSize: '13px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u89C6\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u5DE5\\u5177\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u5E2E\\u52A9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ccc',\n              fontSize: '12px'\n            },\n            children: result ? '已处理' : '等待上传图像'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [\"\\u9519\\u8BEF: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '8px',\n              textAlign: 'center',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '30px',\n                height: '30px',\n                border: '3px solid #333',\n                borderTop: '3px solid #4a90e2',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite',\n                margin: '0 auto'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), result ? /*#__PURE__*/_jsxDEV(ResultView, {\n            result: result,\n            originalImage: originalImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u5C31\\u7EEA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"RealESRGAN v2.1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"/wnUJUGA6SIC9LLjuQnSu+pOQ8A=\");\n_c = App;\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "showMenu", "setShowMenu", "imageInfo", "setImageInfo", "processingTime", "setProcessingTime", "zoomLevel", "setZoomLevel", "viewMode", "setViewMode", "rotation", "setRotation", "fileInputRef", "handleUpload", "formData", "file", "get", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "handleReset", "style", "height", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "borderBottom", "alignItems", "padding", "color", "gap", "marginRight", "width", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "textAlign", "fontSize", "fontWeight", "cursor", "position", "onClick", "border", "justifyContent", "top", "left", "right", "zIndex", "transform", "marginBottom", "borderTop", "animation", "margin", "borderLeft", "onUpload", "jsx", "_c", "globalStyles", "document", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const [viewMode, setViewMode] = useState('split'); // split, original, enhanced\n  const [rotation, setRotation] = useState(0);\n  const fileInputRef = useRef(null);\n\n  const handleUpload = async (formData) => {\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>文件</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>编辑</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>视图</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>工具</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>帮助</span>\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              新建\n            </button>\n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {result ? '已处理' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <ResultView result={result} originalImage={originalImage} />\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>就绪</span>\n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n\n      {/* CSS动画 */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM6B,YAAY,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAM4B,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvCnB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMkB,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKrB,gBAAgB,CAACqB,CAAC,CAACC,MAAM,CAAC7B,MAAM,CAAC;MACxDyB,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCpC,SAAS,CAACuC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACtC,KAAK,CAAC,OAAO,EAAEqC,GAAG,CAAC;MAC3BpC,QAAQ,CAACoC,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRxC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMyC,WAAW,GAAGA,CAAA,KAAM;IACxB3C,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,oBACEV,OAAA;IAAKgD,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAvD,OAAA;MAAKgD,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BM,YAAY,EAAE,gBAAgB;QAC9BJ,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAJ,QAAA,gBAEAvD,OAAA;QAAKgD,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC/DvD,OAAA;UAAKgD,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGnE,OAAA;UAAKgD,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGnE,OAAA;UAAKgD,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNnE,OAAA;QAAKgD,KAAK,EAAE;UACVoB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBZ,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNnE,OAAA;QAAKgD,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAC7DvD,OAAA;UAAMgD,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFnE,OAAA;UAAMgD,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFnE,OAAA;UAAMgD,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFnE,OAAA;UAAMgD,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFnE,OAAA;UAAMgD,KAAK,EAAE;YAAEwB,MAAM,EAAE,SAAS;YAAEd,OAAO,EAAE,UAAU;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA;MAAKgD,KAAK,EAAE;QACVoB,IAAI,EAAE,CAAC;QACPhB,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEAvD,OAAA;QAAKgD,KAAK,EAAE;UACVoB,IAAI,EAAE,CAAC;UACPlB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBoB,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,gBAEAvD,OAAA;UAAKgD,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBE,GAAG,EAAE;UACP,CAAE;UAAAL,QAAA,gBACAvD,OAAA;YACE0E,OAAO,EAAE3B,WAAY;YACrBC,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBR,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdgB,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBS,MAAM,EAAE,SAAS;cACjBF,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YAAKgD,KAAK,EAAE;cAAEc,KAAK,EAAE,KAAK;cAAEb,MAAM,EAAE,MAAM;cAAEC,eAAe,EAAE;YAAO;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7EnE,OAAA;YAAMgD,KAAK,EAAE;cAAEW,KAAK,EAAE,MAAM;cAAEW,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAC9CpD,MAAM,GAAG,KAAK,GAAG;UAAQ;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNnE,OAAA;UAAKgD,KAAK,EAAE;YACVoB,IAAI,EAAE,CAAC;YACPhB,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBmB,cAAc,EAAE,QAAQ;YACxBlB,OAAO,EAAE,MAAM;YACfe,QAAQ,EAAE;UACZ,CAAE;UAAAlB,QAAA,GACChD,KAAK,iBACJP,OAAA;YAAKgD,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZC,KAAK,EAAE,MAAM;cACb7B,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,MAAM;cAChBU,MAAM,EAAE;YACV,CAAE;YAAAzB,QAAA,GAAC,gBACG,EAAChD,KAAK;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,EAEA9D,SAAS,iBACRL,OAAA;YAAKgD,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXG,SAAS,EAAE,uBAAuB;cAClC/B,eAAe,EAAE,iBAAiB;cAClCS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBM,SAAS,EAAE,QAAQ;cACnBW,MAAM,EAAE;YACV,CAAE;YAAAzB,QAAA,gBACAvD,OAAA;cAAKgD,KAAK,EAAE;gBAAEkC,YAAY,EAAE;cAAO,CAAE;cAAA3B,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDnE,OAAA;cAAKgD,KAAK,EAAE;gBACVc,KAAK,EAAE,MAAM;gBACbb,MAAM,EAAE,MAAM;gBACd0B,MAAM,EAAE,gBAAgB;gBACxBQ,SAAS,EAAE,mBAAmB;gBAC9BpB,YAAY,EAAE,KAAK;gBACnBqB,SAAS,EAAE,yBAAyB;gBACpCC,MAAM,EAAE;cACV;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAhE,MAAM,gBACLH,OAAA,CAACF,UAAU;YAACK,MAAM,EAAEA,MAAO;YAACM,aAAa,EAAEA;UAAc;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5DnE,OAAA;YAAKgD,KAAK,EAAE;cACVqB,SAAS,EAAE,QAAQ;cACnBV,KAAK,EAAE,MAAM;cACbW,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,gBACAvD,OAAA;cAAKgD,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,MAAM;gBAAEY,YAAY,EAAE;cAAO,CAAE;cAAA3B,QAAA,EAAC;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEnE,OAAA;cAAAuD,QAAA,EAAK;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnE,OAAA;QAAKgD,KAAK,EAAE;UACVc,KAAK,EAAE,OAAO;UACdZ,eAAe,EAAE,SAAS;UAC1BoC,UAAU,EAAE,gBAAgB;UAC5BlC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEAvD,OAAA;UAAKgD,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNnE,OAAA;UAAKgD,KAAK,EAAE;YACVoB,IAAI,EAAE,CAAC;YACPd,QAAQ,EAAE,MAAM;YAChBI,OAAO,EAAE;UACX,CAAE;UAAAH,QAAA,eACAvD,OAAA,CAACH,UAAU;YAAC0F,QAAQ,EAAE/D,YAAa;YAACnB,SAAS,EAAEA;UAAU;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA;MAAKgD,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,MAAM;QACvBiC,SAAS,EAAE,gBAAgB;QAC3B/B,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbW,QAAQ,EAAE;MACZ,CAAE;MAAAf,QAAA,gBACAvD,OAAA;QAAAuD,QAAA,EAAM;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACfnE,OAAA;QAAKgD,KAAK,EAAE;UAAEoB,IAAI,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BnE,OAAA;QAAAuD,QAAA,EAAM;MAAe;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNnE,OAAA;MAAOwF,GAAG;MAAAjC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACjE,EAAA,CA7QQD,GAAG;AAAAwF,EAAA,GAAHxF,GAAG;AA+QZ,eAAeA,GAAG;;AAElB;AACA,MAAMyF,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,WAAW,GAAGJ,YAAY;EACvCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;AACzC;AAAC,IAAAH,EAAA;AAAAQ,YAAA,CAAAR,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}