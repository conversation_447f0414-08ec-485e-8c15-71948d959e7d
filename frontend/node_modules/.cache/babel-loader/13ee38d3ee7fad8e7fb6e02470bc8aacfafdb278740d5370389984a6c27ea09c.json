{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      color: 'white',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontSize: '24px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"AI\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u6D4B\\u8BD5\\u9875\\u9762 - \\u5982\\u679C\\u60A8\\u770B\\u5230\\u8FD9\\u4E2A\\uFF0C\\u8BF4\\u660EReact\\u6B63\\u5728\\u5DE5\\u4F5C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "App", "style", "height", "backgroundColor", "color", "display", "alignItems", "justifyContent", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\n\nfunction App() {\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      color: 'white',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontSize: '24px'\n    }}>\n      <div>\n        <h1>AI图像增强工具</h1>\n        <p>测试页面 - 如果您看到这个，说明React正在工作</p>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eACAV,OAAA;MAAAU,QAAA,gBACEV,OAAA;QAAAU,QAAA,EAAI;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBd,OAAA;QAAAU,QAAA,EAAG;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAjBQd,GAAG;AAmBZ,eAAeA,GAAG;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}