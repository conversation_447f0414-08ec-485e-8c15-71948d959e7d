{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/').then(res => res.json()).then(data => {\n      setPresets(data.presets);\n      if (data.presets.default) {\n        setParams(data.presets.default.params);\n      }\n    }).catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n  const validateAndSetFile = file => {\n    // 验证文件类型\n    if (!file.type.startsWith('image/')) {\n      setError('请选择图像文件');\n      return false;\n    }\n\n    // 验证文件大小 (10MB)\n    if (file.size > 10 * 1024 * 1024) {\n      setError('文件大小不能超过10MB');\n      return false;\n    }\n    setSelectedFile(file);\n    setError(null);\n\n    // 创建预览\n    const reader = new FileReader();\n    reader.onload = e => setPreviewUrl(e.target.result);\n    reader.readAsDataURL(file);\n    return true;\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      validateAndSetFile(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  // 拖拽处理\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      validateAndSetFile(files[0]);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100%',\n      backgroundColor: '#2d2d2d',\n      color: '#e0e0e0',\n      fontSize: '13px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        height: '100%',\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '10px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u56FE\\u50CF\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          onDragOver: handleDragOver,\n          onDragLeave: handleDragLeave,\n          onDrop: handleDrop,\n          style: {\n            border: `2px dashed ${isDragOver ? '#4a90e2' : '#555'}`,\n            borderRadius: '8px',\n            padding: '20px',\n            textAlign: 'center',\n            backgroundColor: isDragOver ? 'rgba(74, 144, 226, 0.1)' : '#3c3c3c',\n            transition: 'all 0.3s ease',\n            marginBottom: '10px',\n            cursor: 'pointer'\n          },\n          onClick: () => document.getElementById('file-input').click(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '24px',\n              marginBottom: '8px'\n            },\n            children: isDragOver ? '📁' : '📷'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#e0e0e0',\n              fontSize: '12px',\n              marginBottom: '4px'\n            },\n            children: isDragOver ? '释放文件以上传' : '点击选择图像或拖拽到此处'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#888',\n              fontSize: '10px'\n            },\n            children: \"\\u652F\\u6301 JPG, PNG, GIF \\u7B49\\u683C\\u5F0F\\uFF0C\\u6700\\u5927 10MB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-input\",\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: previewUrl,\n            alt: \"\\u9884\\u89C8\",\n            style: {\n              width: '100%',\n              maxHeight: '120px',\n              objectFit: 'contain',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              backgroundColor: '#1e1e1e'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            padding: '6px',\n            backgroundColor: '#d32f2f',\n            color: 'white',\n            borderRadius: '3px',\n            fontSize: '11px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '8px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u9884\\u8BBE\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedPreset,\n          onChange: e => handlePresetChange(e.target.value),\n          style: {\n            width: '100%',\n            padding: '6px',\n            backgroundColor: '#3c3c3c',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            color: '#e0e0e0',\n            fontSize: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"default\",\n            children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"portrait\",\n            children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"landscape\",\n            children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"vintage\",\n            children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"fast\",\n            children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"custom\",\n            children: \"\\u81EA\\u5B9A\\u4E49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u57FA\\u7840\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u8D85\\u5206\\u500D\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [params.scale, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"2\",\n            max: \"4\",\n            step: \"2\",\n            value: params.scale,\n            onChange: e => handleParamChange('scale', parseInt(e.target.value)),\n            style: {\n              width: '100%',\n              height: '4px',\n              backgroundColor: '#555',\n              outline: 'none',\n              borderRadius: '2px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px'\n            },\n            children: \"RealESRGAN\\u6A21\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: params.use_realesrgan,\n            onChange: e => handleParamChange('use_realesrgan', e.target.checked),\n            style: {\n              width: '16px',\n              height: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '15px',\n          overflow: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u9AD8\\u7EA7\\u53C2\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u9510\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [(params.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"1\",\n            step: \"0.05\",\n            value: params.sharpening,\n            onChange: e => handleParamChange('sharpening', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u964D\\u566A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: params.denoising\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"30\",\n            step: \"1\",\n            value: params.denoising,\n            onChange: e => handleParamChange('denoising', parseInt(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u9971\\u548C\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: params.saturation.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"2\",\n            step: \"0.1\",\n            value: params.saturation,\n            onChange: e => handleParamChange('saturation', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u5BF9\\u6BD4\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: params.contrast.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"2\",\n            step: \"0.05\",\n            value: params.contrast,\n            onChange: e => handleParamChange('contrast', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u4EAE\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [params.brightness > 0 ? '+' : '', params.brightness]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"-100\",\n            max: \"100\",\n            step: \"5\",\n            value: params.brightness,\n            onChange: e => handleParamChange('brightness', parseInt(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7F8E\\u989C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [(params.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"1\",\n            step: \"0.05\",\n            value: params.beauty,\n            onChange: e => handleParamChange('beauty', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderTop: '1px solid #444'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !selectedFile || isLoading,\n          style: {\n            width: '100%',\n            padding: '10px',\n            backgroundColor: !selectedFile || isLoading ? '#555' : '#4a90e2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: !selectedFile || isLoading ? 'not-allowed' : 'pointer',\n            fontSize: '13px',\n            fontWeight: '500'\n          },\n          children: isLoading ? '处理中...' : '开始增强'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"3+HPKV3b7ObPKTUPYbaABiz2yqA=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "isDragOver", "setIsDragOver", "showAdvanced", "setShowAdvanced", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fetch", "then", "res", "json", "data", "default", "catch", "err", "console", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "validateAndSetFile", "file", "type", "startsWith", "size", "reader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "handleFileChange", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "length", "handleSubmit", "formData", "FormData", "append", "JSON", "stringify", "style", "height", "backgroundColor", "color", "fontSize", "children", "onSubmit", "display", "flexDirection", "padding", "borderBottom", "marginBottom", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDragOver", "onDragLeave", "onDrop", "border", "borderRadius", "textAlign", "transition", "cursor", "onClick", "document", "getElementById", "click", "id", "onChange", "accept", "required", "disabled", "marginTop", "src", "alt", "width", "maxHeight", "objectFit", "justifyContent", "min", "max", "step", "parseInt", "outline", "alignItems", "checked", "flex", "overflow", "toFixed", "parseFloat", "borderTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/')\n      .then(res => res.json())\n      .then(data => {\n        setPresets(data.presets);\n        if (data.presets.default) {\n          setParams(data.presets.default.params);\n        }\n      })\n      .catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n\n  const validateAndSetFile = (file) => {\n    // 验证文件类型\n    if (!file.type.startsWith('image/')) {\n      setError('请选择图像文件');\n      return false;\n    }\n\n    // 验证文件大小 (10MB)\n    if (file.size > 10 * 1024 * 1024) {\n      setError('文件大小不能超过10MB');\n      return false;\n    }\n\n    setSelectedFile(file);\n    setError(null);\n\n    // 创建预览\n    const reader = new FileReader();\n    reader.onload = (e) => setPreviewUrl(e.target.result);\n    reader.readAsDataURL(file);\n    return true;\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      validateAndSetFile(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  // 拖拽处理\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      validateAndSetFile(files[0]);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    \n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  return (\n    <div style={{ \n      height: '100%',\n      backgroundColor: '#2d2d2d',\n      color: '#e0e0e0',\n      fontSize: '13px'\n    }}>\n      <form onSubmit={handleSubmit} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n        \n        {/* 文件上传区域 */}\n        <div style={{\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        }}>\n          <div style={{\n            marginBottom: '10px',\n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            图像文件\n          </div>\n\n          {/* 拖拽上传区域 */}\n          <div\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n            style={{\n              border: `2px dashed ${isDragOver ? '#4a90e2' : '#555'}`,\n              borderRadius: '8px',\n              padding: '20px',\n              textAlign: 'center',\n              backgroundColor: isDragOver ? 'rgba(74, 144, 226, 0.1)' : '#3c3c3c',\n              transition: 'all 0.3s ease',\n              marginBottom: '10px',\n              cursor: 'pointer'\n            }}\n            onClick={() => document.getElementById('file-input').click()}\n          >\n            <div style={{ fontSize: '24px', marginBottom: '8px' }}>\n              {isDragOver ? '📁' : '📷'}\n            </div>\n            <div style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '4px' }}>\n              {isDragOver ? '释放文件以上传' : '点击选择图像或拖拽到此处'}\n            </div>\n            <div style={{ color: '#888', fontSize: '10px' }}>\n              支持 JPG, PNG, GIF 等格式，最大 10MB\n            </div>\n          </div>\n\n          <input\n            id=\"file-input\"\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{ display: 'none' }}\n          />\n\n          {previewUrl && (\n            <div style={{ marginTop: '10px' }}>\n              <img\n                src={previewUrl}\n                alt=\"预览\"\n                style={{\n                  width: '100%',\n                  maxHeight: '120px',\n                  objectFit: 'contain',\n                  border: '1px solid #555',\n                  borderRadius: '4px',\n                  backgroundColor: '#1e1e1e'\n                }}\n              />\n            </div>\n          )}\n\n          {error && (\n            <div style={{\n              marginTop: '8px',\n              padding: '6px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              borderRadius: '3px',\n              fontSize: '11px'\n            }}>\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 预设配置 */}\n        <div style={{ \n          padding: '15px',\n          borderBottom: '1px solid #444'\n        }}>\n          <div style={{ \n            marginBottom: '8px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            预设配置\n          </div>\n          \n          <select \n            value={selectedPreset}\n            onChange={(e) => handlePresetChange(e.target.value)}\n            style={{ \n              width: '100%', \n              padding: '6px', \n              backgroundColor: '#3c3c3c',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#e0e0e0',\n              fontSize: '12px'\n            }}\n          >\n            <option value=\"default\">默认设置</option>\n            <option value=\"portrait\">人像优化</option>\n            <option value=\"landscape\">风景增强</option>\n            <option value=\"vintage\">复古风格</option>\n            <option value=\"fast\">快速处理</option>\n            <option value=\"custom\">自定义</option>\n          </select>\n        </div>\n\n        {/* 基础参数 */}\n        <div style={{ \n          padding: '15px',\n          borderBottom: '1px solid #444'\n        }}>\n          <div style={{ \n            marginBottom: '12px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            基础设置\n          </div>\n\n          {/* 超分倍数 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>超分倍数</span>\n              <span>{params.scale}x</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"2\"\n              max=\"4\"\n              step=\"2\"\n              value={params.scale}\n              onChange={(e) => handleParamChange('scale', parseInt(e.target.value))}\n              style={{ \n                width: '100%',\n                height: '4px',\n                backgroundColor: '#555',\n                outline: 'none',\n                borderRadius: '2px'\n              }}\n            />\n          </div>\n\n          {/* RealESRGAN开关 */}\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            justifyContent: 'space-between',\n            marginBottom: '8px'\n          }}>\n            <span style={{ fontSize: '12px' }}>RealESRGAN模型</span>\n            <input \n              type=\"checkbox\"\n              checked={params.use_realesrgan}\n              onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}\n              style={{ \n                width: '16px',\n                height: '16px'\n              }}\n            />\n          </div>\n        </div>\n\n        {/* 高级参数 */}\n        <div style={{ \n          flex: 1,\n          padding: '15px',\n          overflow: 'auto'\n        }}>\n          <div style={{ \n            marginBottom: '12px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            高级参数\n          </div>\n\n          {/* 锐化 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>锐化</span>\n              <span>{(params.sharpening * 100).toFixed(0)}%</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.05\"\n              value={params.sharpening}\n              onChange={(e) => handleParamChange('sharpening', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 降噪 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>降噪</span>\n              <span>{params.denoising}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"30\"\n              step=\"1\"\n              value={params.denoising}\n              onChange={(e) => handleParamChange('denoising', parseInt(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 饱和度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>饱和度</span>\n              <span>{params.saturation.toFixed(1)}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.1\"\n              value={params.saturation}\n              onChange={(e) => handleParamChange('saturation', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 对比度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>对比度</span>\n              <span>{params.contrast.toFixed(1)}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.05\"\n              value={params.contrast}\n              onChange={(e) => handleParamChange('contrast', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 亮度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>亮度</span>\n              <span>{params.brightness > 0 ? '+' : ''}{params.brightness}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"-100\"\n              max=\"100\"\n              step=\"5\"\n              value={params.brightness}\n              onChange={(e) => handleParamChange('brightness', parseInt(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 美颜 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>美颜</span>\n              <span>{(params.beauty * 100).toFixed(0)}%</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.05\"\n              value={params.beauty}\n              onChange={(e) => handleParamChange('beauty', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n        </div>\n\n        {/* 底部按钮 */}\n        <div style={{ \n          padding: '15px',\n          borderTop: '1px solid #444'\n        }}>\n          <button \n            type=\"submit\" \n            disabled={!selectedFile || isLoading}\n            style={{\n              width: '100%',\n              padding: '10px',\n              backgroundColor: !selectedFile || isLoading ? '#555' : '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: !selectedFile || isLoading ? 'not-allowed' : 'pointer',\n              fontSize: '13px',\n              fontWeight: '500'\n            }}\n          >\n            {isLoading ? '处理中...' : '开始增强'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default UploadForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC;IACnCwB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA9B,SAAS,CAAC,MAAM;IACd+B,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZrB,UAAU,CAACqB,IAAI,CAACtB,OAAO,CAAC;MACxB,IAAIsB,IAAI,CAACtB,OAAO,CAACuB,OAAO,EAAE;QACxBd,SAAS,CAACa,IAAI,CAACtB,OAAO,CAACuB,OAAO,CAACf,MAAM,CAAC;MACxC;IACF,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAAC5B,KAAK,CAAC,WAAW,EAAE2B,GAAG,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,kBAAkB,GAAIC,SAAS,IAAK;IACxCzB,iBAAiB,CAACyB,SAAS,CAAC;IAC5B,IAAI5B,OAAO,CAAC4B,SAAS,CAAC,EAAE;MACtBnB,SAAS,CAACT,OAAO,CAAC4B,SAAS,CAAC,CAACpB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxCtB,SAAS,CAACuB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACH5B,iBAAiB,CAAC,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAM8B,kBAAkB,GAAIC,IAAI,IAAK;IACnC;IACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnCrC,QAAQ,CAAC,SAAS,CAAC;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,IAAImC,IAAI,CAACG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MAChCtC,QAAQ,CAAC,cAAc,CAAC;MACxB,OAAO,KAAK;IACd;IAEAJ,eAAe,CAACuC,IAAI,CAAC;IACrBnC,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMuC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACC,MAAM,CAAC;IACrDL,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;IAC1B,OAAO,IAAI;EACb,CAAC;EAED,MAAMW,gBAAgB,GAAIJ,CAAC,IAAK;IAC9B,MAAMP,IAAI,GAAGO,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIZ,IAAI,EAAE;MACRD,kBAAkB,CAACC,IAAI,CAAC;IAC1B,CAAC,MAAM;MACLvC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMkD,cAAc,GAAIN,CAAC,IAAK;IAC5BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB3C,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM4C,eAAe,GAAIR,CAAC,IAAK;IAC7BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB3C,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM6C,UAAU,GAAIT,CAAC,IAAK;IACxBA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB3C,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMyC,KAAK,GAAGL,CAAC,CAACU,YAAY,CAACL,KAAK;IAClC,IAAIA,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACpBnB,kBAAkB,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMO,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAMuD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE9D,YAAY,CAAC;IACrC4D,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAClD,MAAM,CAAC,CAAC;IACjDjB,QAAQ,CAAC+D,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEjE,OAAA;IAAKsE,KAAK,EAAE;MACVC,MAAM,EAAE,MAAM;MACdC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eACA3E,OAAA;MAAM4E,QAAQ,EAAEZ,YAAa;MAACM,KAAK,EAAE;QAAEC,MAAM,EAAE,MAAM;QAAEM,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAS,CAAE;MAAAH,QAAA,gBAGhG3E,OAAA;QAAKsE,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACA3E,OAAA;UAAKsE,KAAK,EAAE;YACVW,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNtF,OAAA;UACEuF,UAAU,EAAE7B,cAAe;UAC3B8B,WAAW,EAAE5B,eAAgB;UAC7B6B,MAAM,EAAE5B,UAAW;UACnBS,KAAK,EAAE;YACLoB,MAAM,EAAE,cAAc3E,UAAU,GAAG,SAAS,GAAG,MAAM,EAAE;YACvD4E,YAAY,EAAE,KAAK;YACnBZ,OAAO,EAAE,MAAM;YACfa,SAAS,EAAE,QAAQ;YACnBpB,eAAe,EAAEzD,UAAU,GAAG,yBAAyB,GAAG,SAAS;YACnE8E,UAAU,EAAE,eAAe;YAC3BZ,YAAY,EAAE,MAAM;YACpBa,MAAM,EAAE;UACV,CAAE;UACFC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;UAAAvB,QAAA,gBAE7D3E,OAAA;YAAKsE,KAAK,EAAE;cAAEI,QAAQ,EAAE,MAAM;cAAEO,YAAY,EAAE;YAAM,CAAE;YAAAN,QAAA,EACnD5D,UAAU,GAAG,IAAI,GAAG;UAAI;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNtF,OAAA;YAAKsE,KAAK,EAAE;cAAEG,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEO,YAAY,EAAE;YAAM,CAAE;YAAAN,QAAA,EACrE5D,UAAU,GAAG,SAAS,GAAG;UAAc;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNtF,OAAA;YAAKsE,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAEjD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA;UACEmG,EAAE,EAAC,YAAY;UACfrD,IAAI,EAAC,MAAM;UACXsD,QAAQ,EAAE5C,gBAAiB;UAC3B6C,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAEpG,SAAU;UACpBmE,KAAK,EAAE;YAAEO,OAAO,EAAE;UAAO;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,EAED/E,UAAU,iBACTP,OAAA;UAAKsE,KAAK,EAAE;YAAEkC,SAAS,EAAE;UAAO,CAAE;UAAA7B,QAAA,eAChC3E,OAAA;YACEyG,GAAG,EAAElG,UAAW;YAChBmG,GAAG,EAAC,cAAI;YACRpC,KAAK,EAAE;cACLqC,KAAK,EAAE,MAAM;cACbC,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,SAAS;cACpBnB,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBnB,eAAe,EAAE;YACnB;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA7E,KAAK,iBACJT,OAAA;UAAKsE,KAAK,EAAE;YACVkC,SAAS,EAAE,KAAK;YAChBzB,OAAO,EAAE,KAAK;YACdP,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdkB,YAAY,EAAE,KAAK;YACnBjB,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACClE;QAAK;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtF,OAAA;QAAKsE,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACA3E,OAAA;UAAKsE,KAAK,EAAE;YACVW,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAENtF,OAAA;UACE0C,KAAK,EAAE7B,cAAe;UACtBuF,QAAQ,EAAGhD,CAAC,IAAKd,kBAAkB,CAACc,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UACpD4B,KAAK,EAAE;YACLqC,KAAK,EAAE,MAAM;YACb5B,OAAO,EAAE,KAAK;YACdP,eAAe,EAAE,SAAS;YAC1BkB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBlB,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,gBAEF3E,OAAA;YAAQ0C,KAAK,EAAC,SAAS;YAAAiC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCtF,OAAA;YAAQ0C,KAAK,EAAC,UAAU;YAAAiC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtF,OAAA;YAAQ0C,KAAK,EAAC,WAAW;YAAAiC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvCtF,OAAA;YAAQ0C,KAAK,EAAC,SAAS;YAAAiC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCtF,OAAA;YAAQ0C,KAAK,EAAC,MAAM;YAAAiC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClCtF,OAAA;YAAQ0C,KAAK,EAAC,QAAQ;YAAAiC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNtF,OAAA;QAAKsE,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACA3E,OAAA;UAAKsE,KAAK,EAAE;YACVW,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBtF,OAAA;cAAA2E,QAAA,GAAOxD,MAAM,CAACE,KAAK,EAAC,GAAC;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,GAAG;YACRvE,KAAK,EAAEvB,MAAM,CAACE,KAAM;YACpB+E,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,OAAO,EAAE0E,QAAQ,CAAC9D,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YACtE4B,KAAK,EAAE;cACLqC,KAAK,EAAE,MAAM;cACbpC,MAAM,EAAE,KAAK;cACbC,eAAe,EAAE,MAAM;cACvB2C,OAAO,EAAE,MAAM;cACfxB,YAAY,EAAE;YAChB;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfuC,UAAU,EAAE,QAAQ;YACpBN,cAAc,EAAE,eAAe;YAC/B7B,YAAY,EAAE;UAChB,CAAE;UAAAN,QAAA,gBACA3E,OAAA;YAAMsE,KAAK,EAAE;cAAEI,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDtF,OAAA;YACE8C,IAAI,EAAC,UAAU;YACfuE,OAAO,EAAElG,MAAM,CAACG,cAAe;YAC/B8E,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,gBAAgB,EAAEY,CAAC,CAACC,MAAM,CAACgE,OAAO,CAAE;YACvE/C,KAAK,EAAE;cACLqC,KAAK,EAAE,MAAM;cACbpC,MAAM,EAAE;YACV;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKsE,KAAK,EAAE;UACVgD,IAAI,EAAE,CAAC;UACPvC,OAAO,EAAE,MAAM;UACfwC,QAAQ,EAAE;QACZ,CAAE;QAAA5C,QAAA,gBACA3E,OAAA;UAAKsE,KAAK,EAAE;YACVW,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACftF,OAAA;cAAA2E,QAAA,GAAO,CAACxD,MAAM,CAACI,UAAU,GAAG,GAAG,EAAEiG,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,MAAM;YACXvE,KAAK,EAAEvB,MAAM,CAACI,UAAW;YACzB6E,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,YAAY,EAAEiF,UAAU,CAACrE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YAC7E4B,KAAK,EAAE;cAAEqC,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACftF,OAAA;cAAA2E,QAAA,EAAOxD,MAAM,CAACK;YAAS;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,GAAG;YACRvE,KAAK,EAAEvB,MAAM,CAACK,SAAU;YACxB4E,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,WAAW,EAAE0E,QAAQ,CAAC9D,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YAC1E4B,KAAK,EAAE;cAAEqC,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChBtF,OAAA;cAAA2E,QAAA,EAAOxD,MAAM,CAACM,UAAU,CAAC+F,OAAO,CAAC,CAAC;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,KAAK;YACVvE,KAAK,EAAEvB,MAAM,CAACM,UAAW;YACzB2E,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,YAAY,EAAEiF,UAAU,CAACrE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YAC7E4B,KAAK,EAAE;cAAEqC,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChBtF,OAAA;cAAA2E,QAAA,EAAOxD,MAAM,CAACO,QAAQ,CAAC8F,OAAO,CAAC,CAAC;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,MAAM;YACXvE,KAAK,EAAEvB,MAAM,CAACO,QAAS;YACvB0E,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,UAAU,EAAEiF,UAAU,CAACrE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YAC3E4B,KAAK,EAAE;cAAEqC,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACftF,OAAA;cAAA2E,QAAA,GAAOxD,MAAM,CAACQ,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAER,MAAM,CAACQ,UAAU;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,MAAM;YACVC,GAAG,EAAC,KAAK;YACTC,IAAI,EAAC,GAAG;YACRvE,KAAK,EAAEvB,MAAM,CAACQ,UAAW;YACzByE,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,YAAY,EAAE0E,QAAQ,CAAC9D,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YAC3E4B,KAAK,EAAE;cAAEqC,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAKsE,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC3E,OAAA;YAAKsE,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfiC,cAAc,EAAE,eAAe;cAC/B7B,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACA3E,OAAA;cAAA2E,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACftF,OAAA;cAAA2E,QAAA,GAAO,CAACxD,MAAM,CAACS,MAAM,GAAG,GAAG,EAAE4F,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNtF,OAAA;YACE8C,IAAI,EAAC,OAAO;YACZiE,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,MAAM;YACXvE,KAAK,EAAEvB,MAAM,CAACS,MAAO;YACrBwE,QAAQ,EAAGhD,CAAC,IAAKZ,iBAAiB,CAAC,QAAQ,EAAEiF,UAAU,CAACrE,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC,CAAE;YACzE4B,KAAK,EAAE;cAAEqC,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKsE,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACf2C,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA3E,OAAA;UACE8C,IAAI,EAAC,QAAQ;UACbyD,QAAQ,EAAE,CAAClG,YAAY,IAAIF,SAAU;UACrCmE,KAAK,EAAE;YACLqC,KAAK,EAAE,MAAM;YACb5B,OAAO,EAAE,MAAM;YACfP,eAAe,EAAE,CAACnE,YAAY,IAAIF,SAAS,GAAG,MAAM,GAAG,SAAS;YAChEsE,KAAK,EAAE,OAAO;YACdiB,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBG,MAAM,EAAE,CAACzF,YAAY,IAAIF,SAAS,GAAG,aAAa,GAAG,SAAS;YAC9DuE,QAAQ,EAAE,MAAM;YAChBQ,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAEDxE,SAAS,GAAG,QAAQ,GAAG;QAAM;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClF,EAAA,CA7dIH,UAAU;AAAA0H,EAAA,GAAV1H,UAAU;AA+dhB,eAAeA,UAAU;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}