{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/').then(res => res.json()).then(data => {\n      setPresets(data.presets);\n      if (data.presets.default) {\n        setParams(data.presets.default.params);\n      }\n    }).catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // 验证文件类型\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      // 验证文件大小 (10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n      setSelectedFile(file);\n      setError(null);\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = e => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100%',\n      backgroundColor: '#2d2d2d',\n      color: '#e0e0e0',\n      fontSize: '13px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        height: '100%',\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '10px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u56FE\\u50CF\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '8px',\n            backgroundColor: '#3c3c3c',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            color: '#e0e0e0',\n            fontSize: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: previewUrl,\n            alt: \"\\u9884\\u89C8\",\n            style: {\n              width: '100%',\n              maxHeight: '120px',\n              objectFit: 'contain',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              backgroundColor: '#1e1e1e'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            padding: '6px',\n            backgroundColor: '#d32f2f',\n            color: 'white',\n            borderRadius: '3px',\n            fontSize: '11px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '8px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u9884\\u8BBE\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedPreset,\n          onChange: e => handlePresetChange(e.target.value),\n          style: {\n            width: '100%',\n            padding: '6px',\n            backgroundColor: '#3c3c3c',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            color: '#e0e0e0',\n            fontSize: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"default\",\n            children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"portrait\",\n            children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"landscape\",\n            children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"vintage\",\n            children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"fast\",\n            children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"custom\",\n            children: \"\\u81EA\\u5B9A\\u4E49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderBottom: '1px solid #444'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u57FA\\u7840\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u8D85\\u5206\\u500D\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [params.scale, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"2\",\n            max: \"4\",\n            step: \"2\",\n            value: params.scale,\n            onChange: e => handleParamChange('scale', parseInt(e.target.value)),\n            style: {\n              width: '100%',\n              height: '4px',\n              backgroundColor: '#555',\n              outline: 'none',\n              borderRadius: '2px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px'\n            },\n            children: \"RealESRGAN\\u6A21\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: params.use_realesrgan,\n            onChange: e => handleParamChange('use_realesrgan', e.target.checked),\n            style: {\n              width: '16px',\n              height: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '15px',\n          overflow: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            fontWeight: '500',\n            color: '#fff'\n          },\n          children: \"\\u9AD8\\u7EA7\\u53C2\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u9510\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [(params.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"1\",\n            step: \"0.05\",\n            value: params.sharpening,\n            onChange: e => handleParamChange('sharpening', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u964D\\u566A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: params.denoising\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"30\",\n            step: \"1\",\n            value: params.denoising,\n            onChange: e => handleParamChange('denoising', parseInt(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u9971\\u548C\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: params.saturation.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"2\",\n            step: \"0.1\",\n            value: params.saturation,\n            onChange: e => handleParamChange('saturation', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u5BF9\\u6BD4\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: params.contrast.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"2\",\n            step: \"0.05\",\n            value: params.contrast,\n            onChange: e => handleParamChange('contrast', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u4EAE\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [params.brightness > 0 ? '+' : '', params.brightness]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"-100\",\n            max: \"100\",\n            step: \"5\",\n            value: params.brightness,\n            onChange: e => handleParamChange('brightness', parseInt(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '4px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7F8E\\u989C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [(params.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"1\",\n            step: \"0.05\",\n            value: params.beauty,\n            onChange: e => handleParamChange('beauty', parseFloat(e.target.value)),\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          borderTop: '1px solid #444'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !selectedFile || isLoading,\n          style: {\n            width: '100%',\n            padding: '10px',\n            backgroundColor: !selectedFile || isLoading ? '#555' : '#4a90e2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: !selectedFile || isLoading ? 'not-allowed' : 'pointer',\n            fontSize: '13px',\n            fontWeight: '500'\n          },\n          children: isLoading ? '处理中...' : '开始增强'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"xSC9RCNijr9WJFhDDQlEUkoMNHo=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fetch", "then", "res", "json", "data", "default", "catch", "err", "console", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "type", "startsWith", "size", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "style", "height", "backgroundColor", "color", "fontSize", "children", "onSubmit", "display", "flexDirection", "padding", "borderBottom", "marginBottom", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "accept", "required", "disabled", "width", "border", "borderRadius", "marginTop", "src", "alt", "maxHeight", "objectFit", "justifyContent", "min", "max", "step", "parseInt", "outline", "alignItems", "checked", "flex", "overflow", "toFixed", "parseFloat", "borderTop", "cursor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/')\n      .then(res => res.json())\n      .then(data => {\n        setPresets(data.presets);\n        if (data.presets.default) {\n          setParams(data.presets.default.params);\n        }\n      })\n      .catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      // 验证文件类型\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      // 验证文件大小 (10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      setSelectedFile(file);\n      setError(null);\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    \n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  return (\n    <div style={{ \n      height: '100%',\n      backgroundColor: '#2d2d2d',\n      color: '#e0e0e0',\n      fontSize: '13px'\n    }}>\n      <form onSubmit={handleSubmit} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n        \n        {/* 文件上传区域 */}\n        <div style={{ \n          padding: '15px',\n          borderBottom: '1px solid #444'\n        }}>\n          <div style={{ \n            marginBottom: '10px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            图像文件\n          </div>\n          \n          <input\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{ \n              width: '100%',\n              padding: '8px',\n              backgroundColor: '#3c3c3c',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#e0e0e0',\n              fontSize: '12px'\n            }}\n          />\n\n          {previewUrl && (\n            <div style={{ marginTop: '10px' }}>\n              <img\n                src={previewUrl}\n                alt=\"预览\"\n                style={{\n                  width: '100%',\n                  maxHeight: '120px',\n                  objectFit: 'contain',\n                  border: '1px solid #555',\n                  borderRadius: '4px',\n                  backgroundColor: '#1e1e1e'\n                }}\n              />\n            </div>\n          )}\n\n          {error && (\n            <div style={{\n              marginTop: '8px',\n              padding: '6px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              borderRadius: '3px',\n              fontSize: '11px'\n            }}>\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 预设配置 */}\n        <div style={{ \n          padding: '15px',\n          borderBottom: '1px solid #444'\n        }}>\n          <div style={{ \n            marginBottom: '8px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            预设配置\n          </div>\n          \n          <select \n            value={selectedPreset}\n            onChange={(e) => handlePresetChange(e.target.value)}\n            style={{ \n              width: '100%', \n              padding: '6px', \n              backgroundColor: '#3c3c3c',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#e0e0e0',\n              fontSize: '12px'\n            }}\n          >\n            <option value=\"default\">默认设置</option>\n            <option value=\"portrait\">人像优化</option>\n            <option value=\"landscape\">风景增强</option>\n            <option value=\"vintage\">复古风格</option>\n            <option value=\"fast\">快速处理</option>\n            <option value=\"custom\">自定义</option>\n          </select>\n        </div>\n\n        {/* 基础参数 */}\n        <div style={{ \n          padding: '15px',\n          borderBottom: '1px solid #444'\n        }}>\n          <div style={{ \n            marginBottom: '12px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            基础设置\n          </div>\n\n          {/* 超分倍数 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>超分倍数</span>\n              <span>{params.scale}x</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"2\"\n              max=\"4\"\n              step=\"2\"\n              value={params.scale}\n              onChange={(e) => handleParamChange('scale', parseInt(e.target.value))}\n              style={{ \n                width: '100%',\n                height: '4px',\n                backgroundColor: '#555',\n                outline: 'none',\n                borderRadius: '2px'\n              }}\n            />\n          </div>\n\n          {/* RealESRGAN开关 */}\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            justifyContent: 'space-between',\n            marginBottom: '8px'\n          }}>\n            <span style={{ fontSize: '12px' }}>RealESRGAN模型</span>\n            <input \n              type=\"checkbox\"\n              checked={params.use_realesrgan}\n              onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}\n              style={{ \n                width: '16px',\n                height: '16px'\n              }}\n            />\n          </div>\n        </div>\n\n        {/* 高级参数 */}\n        <div style={{ \n          flex: 1,\n          padding: '15px',\n          overflow: 'auto'\n        }}>\n          <div style={{ \n            marginBottom: '12px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            高级参数\n          </div>\n\n          {/* 锐化 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>锐化</span>\n              <span>{(params.sharpening * 100).toFixed(0)}%</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.05\"\n              value={params.sharpening}\n              onChange={(e) => handleParamChange('sharpening', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 降噪 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>降噪</span>\n              <span>{params.denoising}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"30\"\n              step=\"1\"\n              value={params.denoising}\n              onChange={(e) => handleParamChange('denoising', parseInt(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 饱和度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>饱和度</span>\n              <span>{params.saturation.toFixed(1)}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.1\"\n              value={params.saturation}\n              onChange={(e) => handleParamChange('saturation', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 对比度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>对比度</span>\n              <span>{params.contrast.toFixed(1)}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"2\"\n              step=\"0.05\"\n              value={params.contrast}\n              onChange={(e) => handleParamChange('contrast', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 亮度 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>亮度</span>\n              <span>{params.brightness > 0 ? '+' : ''}{params.brightness}</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"-100\"\n              max=\"100\"\n              step=\"5\"\n              value={params.brightness}\n              onChange={(e) => handleParamChange('brightness', parseInt(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n\n          {/* 美颜 */}\n          <div style={{ marginBottom: '12px' }}>\n            <div style={{ \n              display: 'flex', \n              justifyContent: 'space-between', \n              marginBottom: '4px',\n              fontSize: '12px'\n            }}>\n              <span>美颜</span>\n              <span>{(params.beauty * 100).toFixed(0)}%</span>\n            </div>\n            <input \n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.05\"\n              value={params.beauty}\n              onChange={(e) => handleParamChange('beauty', parseFloat(e.target.value))}\n              style={{ width: '100%' }}\n            />\n          </div>\n        </div>\n\n        {/* 底部按钮 */}\n        <div style={{ \n          padding: '15px',\n          borderTop: '1px solid #444'\n        }}>\n          <button \n            type=\"submit\" \n            disabled={!selectedFile || isLoading}\n            style={{\n              width: '100%',\n              padding: '10px',\n              backgroundColor: !selectedFile || isLoading ? '#555' : '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: !selectedFile || isLoading ? 'not-allowed' : 'pointer',\n              fontSize: '13px',\n              fontWeight: '500'\n            }}\n          >\n            {isLoading ? '处理中...' : '开始增强'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default UploadForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC;IACnCoB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA1B,SAAS,CAAC,MAAM;IACd2B,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZjB,UAAU,CAACiB,IAAI,CAAClB,OAAO,CAAC;MACxB,IAAIkB,IAAI,CAAClB,OAAO,CAACmB,OAAO,EAAE;QACxBd,SAAS,CAACa,IAAI,CAAClB,OAAO,CAACmB,OAAO,CAACf,MAAM,CAAC;MACxC;IACF,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACxB,KAAK,CAAC,WAAW,EAAEuB,GAAG,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,kBAAkB,GAAIC,SAAS,IAAK;IACxCrB,iBAAiB,CAACqB,SAAS,CAAC;IAC5B,IAAIxB,OAAO,CAACwB,SAAS,CAAC,EAAE;MACtBnB,SAAS,CAACL,OAAO,CAACwB,SAAS,CAAC,CAACpB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxCtB,SAAS,CAACuB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACHxB,iBAAiB,CAAC,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAM0B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCpC,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;;MAEA;MACA,IAAIgC,IAAI,CAACK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCrC,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;MAEAJ,eAAe,CAACoC,IAAI,CAAC;MACrBhC,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMsC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIT,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLpC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACjD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAM6C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpD,YAAY,CAAC;IACrCkD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC5C,MAAM,CAAC,CAAC;IACjDb,QAAQ,CAACqD,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEvD,OAAA;IAAK4D,KAAK,EAAE;MACVC,MAAM,EAAE,MAAM;MACdC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eACAjE,OAAA;MAAMkE,QAAQ,EAAEb,YAAa;MAACO,KAAK,EAAE;QAAEC,MAAM,EAAE,MAAM;QAAEM,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAS,CAAE;MAAAH,QAAA,gBAGhGjE,OAAA;QAAK4D,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACAjE,OAAA;UAAK4D,KAAK,EAAE;YACVW,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEN5E,OAAA;UACE6C,IAAI,EAAC,MAAM;UACXgC,QAAQ,EAAErC,gBAAiB;UAC3BsC,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAE7E,SAAU;UACpByD,KAAK,EAAE;YACLqB,KAAK,EAAE,MAAM;YACbZ,OAAO,EAAE,KAAK;YACdP,eAAe,EAAE,SAAS;YAC1BoB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBpB,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDrE,UAAU,iBACTP,OAAA;UAAK4D,KAAK,EAAE;YAAEwB,SAAS,EAAE;UAAO,CAAE;UAAAnB,QAAA,eAChCjE,OAAA;YACEqF,GAAG,EAAE9E,UAAW;YAChB+E,GAAG,EAAC,cAAI;YACR1B,KAAK,EAAE;cACLqB,KAAK,EAAE,MAAM;cACbM,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,SAAS;cACpBN,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBrB,eAAe,EAAE;YACnB;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAnE,KAAK,iBACJT,OAAA;UAAK4D,KAAK,EAAE;YACVwB,SAAS,EAAE,KAAK;YAChBf,OAAO,EAAE,KAAK;YACdP,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdoB,YAAY,EAAE,KAAK;YACnBnB,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACCxD;QAAK;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5E,OAAA;QAAK4D,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACAjE,OAAA;UAAK4D,KAAK,EAAE;YACVW,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEN5E,OAAA;UACEsC,KAAK,EAAEzB,cAAe;UACtBgE,QAAQ,EAAGpC,CAAC,IAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;UACpDsB,KAAK,EAAE;YACLqB,KAAK,EAAE,MAAM;YACbZ,OAAO,EAAE,KAAK;YACdP,eAAe,EAAE,SAAS;YAC1BoB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBpB,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,gBAEFjE,OAAA;YAAQsC,KAAK,EAAC,SAAS;YAAA2B,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC5E,OAAA;YAAQsC,KAAK,EAAC,UAAU;YAAA2B,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC5E,OAAA;YAAQsC,KAAK,EAAC,WAAW;YAAA2B,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvC5E,OAAA;YAAQsC,KAAK,EAAC,SAAS;YAAA2B,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC5E,OAAA;YAAQsC,KAAK,EAAC,MAAM;YAAA2B,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC5E,OAAA;YAAQsC,KAAK,EAAC,QAAQ;YAAA2B,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN5E,OAAA;QAAK4D,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACAjE,OAAA;UAAK4D,KAAK,EAAE;YACVW,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjB5E,OAAA;cAAAiE,QAAA,GAAOlD,MAAM,CAACE,KAAK,EAAC,GAAC;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,GAAG;YACRtD,KAAK,EAAEvB,MAAM,CAACE,KAAM;YACpB4D,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,OAAO,EAAEyD,QAAQ,CAACpD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YACtEsB,KAAK,EAAE;cACLqB,KAAK,EAAE,MAAM;cACbpB,MAAM,EAAE,KAAK;cACbC,eAAe,EAAE,MAAM;cACvBgC,OAAO,EAAE,MAAM;cACfX,YAAY,EAAE;YAChB;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACf4B,UAAU,EAAE,QAAQ;YACpBN,cAAc,EAAE,eAAe;YAC/BlB,YAAY,EAAE;UAChB,CAAE;UAAAN,QAAA,gBACAjE,OAAA;YAAM4D,KAAK,EAAE;cAAEI,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtD5E,OAAA;YACE6C,IAAI,EAAC,UAAU;YACfmD,OAAO,EAAEjF,MAAM,CAACG,cAAe;YAC/B2D,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,gBAAgB,EAAEK,CAAC,CAACE,MAAM,CAACqD,OAAO,CAAE;YACvEpC,KAAK,EAAE;cACLqB,KAAK,EAAE,MAAM;cACbpB,MAAM,EAAE;YACV;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAK4D,KAAK,EAAE;UACVqC,IAAI,EAAE,CAAC;UACP5B,OAAO,EAAE,MAAM;UACf6B,QAAQ,EAAE;QACZ,CAAE;QAAAjC,QAAA,gBACAjE,OAAA;UAAK4D,KAAK,EAAE;YACVW,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf5E,OAAA;cAAAiE,QAAA,GAAO,CAAClD,MAAM,CAACI,UAAU,GAAG,GAAG,EAAEgF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,MAAM;YACXtD,KAAK,EAAEvB,MAAM,CAACI,UAAW;YACzB0D,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YAC7EsB,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf5E,OAAA;cAAAiE,QAAA,EAAOlD,MAAM,CAACK;YAAS;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRC,IAAI,EAAC,GAAG;YACRtD,KAAK,EAAEvB,MAAM,CAACK,SAAU;YACxByD,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,WAAW,EAAEyD,QAAQ,CAACpD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YAC1EsB,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChB5E,OAAA;cAAAiE,QAAA,EAAOlD,MAAM,CAACM,UAAU,CAAC8E,OAAO,CAAC,CAAC;YAAC;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,KAAK;YACVtD,KAAK,EAAEvB,MAAM,CAACM,UAAW;YACzBwD,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YAC7EsB,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChB5E,OAAA;cAAAiE,QAAA,EAAOlD,MAAM,CAACO,QAAQ,CAAC6E,OAAO,CAAC,CAAC;YAAC;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,MAAM;YACXtD,KAAK,EAAEvB,MAAM,CAACO,QAAS;YACvBuD,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,UAAU,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YAC3EsB,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf5E,OAAA;cAAAiE,QAAA,GAAOlD,MAAM,CAACQ,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAER,MAAM,CAACQ,UAAU;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,MAAM;YACVC,GAAG,EAAC,KAAK;YACTC,IAAI,EAAC,GAAG;YACRtD,KAAK,EAAEvB,MAAM,CAACQ,UAAW;YACzBsD,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,YAAY,EAAEyD,QAAQ,CAACpD,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YAC3EsB,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5E,OAAA;UAAK4D,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnCjE,OAAA;YAAK4D,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfsB,cAAc,EAAE,eAAe;cAC/BlB,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,gBACAjE,OAAA;cAAAiE,QAAA,EAAM;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACf5E,OAAA;cAAAiE,QAAA,GAAO,CAAClD,MAAM,CAACS,MAAM,GAAG,GAAG,EAAE2E,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN5E,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZ6C,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,MAAM;YACXtD,KAAK,EAAEvB,MAAM,CAACS,MAAO;YACrBqD,QAAQ,EAAGpC,CAAC,IAAKL,iBAAiB,CAAC,QAAQ,EAAEgE,UAAU,CAAC3D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;YACzEsB,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAK4D,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfgC,SAAS,EAAE;QACb,CAAE;QAAApC,QAAA,eACAjE,OAAA;UACE6C,IAAI,EAAC,QAAQ;UACbmC,QAAQ,EAAE,CAAC3E,YAAY,IAAIF,SAAU;UACrCyD,KAAK,EAAE;YACLqB,KAAK,EAAE,MAAM;YACbZ,OAAO,EAAE,MAAM;YACfP,eAAe,EAAE,CAACzD,YAAY,IAAIF,SAAS,GAAG,MAAM,GAAG,SAAS;YAChE4D,KAAK,EAAE,OAAO;YACdmB,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBmB,MAAM,EAAE,CAACjG,YAAY,IAAIF,SAAS,GAAG,aAAa,GAAG,SAAS;YAC9D6D,QAAQ,EAAE,MAAM;YAChBQ,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAED9D,SAAS,GAAG,QAAQ,GAAG;QAAM;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxE,EAAA,CA5aIH,UAAU;AAAAsG,EAAA,GAAVtG,UAAU;AA8ahB,eAAeA,UAAU;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}