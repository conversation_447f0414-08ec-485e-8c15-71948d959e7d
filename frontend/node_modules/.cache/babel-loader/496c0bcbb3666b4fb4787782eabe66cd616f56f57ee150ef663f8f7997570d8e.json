{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const containerRef = useRef(null);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !containerRef.current) return;\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginTop: '20px',\n      padding: '20px',\n      border: '1px solid #ddd',\n      borderRadius: '10px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '20px',\n        flexWrap: 'wrap',\n        gap: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#28a745',\n          margin: 0\n        },\n        children: \"\\u2705 \\u5904\\u7406\\u5B8C\\u6210\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '6px 12px',\n            backgroundColor: viewMode === 'split' ? '#007bff' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '14px'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '6px 12px',\n            backgroundColor: viewMode === 'side-by-side' ? '#007bff' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '14px'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '6px 12px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '14px'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), result.message && /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#6c757d',\n        marginBottom: '15px'\n      },\n      children: result.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px',\n        padding: '10px',\n        backgroundColor: '#f8f9fa',\n        borderRadius: '5px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          marginBottom: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            margin: 0,\n            marginRight: '10px'\n          },\n          children: \"\\u5904\\u7406\\u53C2\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowParams(!showParams),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#6c757d',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px'\n          },\n          children: showParams ? '隐藏' : '显示'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this), showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          color: '#495057'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8D85\\u5206\\u500D\\u6570:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 22\n            }, this), \" \", result.params_used.scale, \"x\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"AI\\u6A21\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 22\n            }, this), \" \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9510\\u5316:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 22\n            }, this), \" \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u964D\\u566A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 22\n            }, this), \" \", result.params_used.denoising]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9971\\u548C\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 22\n            }, this), \" \", result.params_used.saturation.toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 22\n            }, this), \" \", result.params_used.contrast.toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4EAE\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 22\n            }, this), \" \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u7F8E\\u989C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 22\n            }, this), \" \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this), viewMode === 'split' ?\n    /*#__PURE__*/\n    // 分割线对比模式\n    _jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        position: 'relative',\n        width: '100%',\n        maxWidth: '800px',\n        margin: '0 auto',\n        cursor: isDragging ? 'ew-resize' : 'default',\n        userSelect: 'none'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#495057',\n            fontSize: '18px'\n          },\n          children: \"\\u62D6\\u62FD\\u5206\\u5272\\u7EBF\\u5BF9\\u6BD4\\u539F\\u56FE\\u4E0E\\u589E\\u5F3A\\u6548\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: '5px 0',\n            fontSize: '14px',\n            color: '#6c757d'\n          },\n          children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5F53\\u524D\\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          display: 'inline-block',\n          width: '100%'\n        },\n        children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '400px',\n            border: '2px dashed #dc3545',\n            borderRadius: '8px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: '#dc3545',\n            backgroundColor: '#f8d7da'\n          },\n          children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n          src: `http://localhost:8001/result/${result.enhanced_url}`,\n          alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n          onLoad: handleImageLoad,\n          onError: handleImageError,\n          style: {\n            width: '100%',\n            maxWidth: '800px',\n            height: 'auto',\n            border: '2px solid #28a745',\n            borderRadius: '8px',\n            display: 'block',\n            opacity: imageLoaded ? 1 : 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: `${splitPosition}%`,\n            height: '100%',\n            overflow: 'hidden',\n            borderRadius: '8px 0 0 8px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: originalImage,\n            alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n            style: {\n              width: `${100 * 100 / splitPosition}%`,\n              height: '100%',\n              objectFit: 'cover',\n              border: '2px solid #ddd',\n              borderRadius: '8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: `${splitPosition}%`,\n            width: '4px',\n            height: '100%',\n            backgroundColor: '#fff',\n            cursor: 'ew-resize',\n            boxShadow: '0 0 10px rgba(0,0,0,0.3)',\n            transform: 'translateX(-2px)',\n            zIndex: 10\n          },\n          onMouseDown: handleMouseDown,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              width: '20px',\n              height: '40px',\n              backgroundColor: '#007bff',\n              borderRadius: '10px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '12px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u27F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 15\n        }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            color: '#6c757d',\n            backgroundColor: 'rgba(255,255,255,0.8)',\n            padding: '10px',\n            borderRadius: '5px'\n          },\n          children: \"\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    // 并排对比模式\n    _jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '20px',\n        flexWrap: 'wrap',\n        justifyContent: 'center'\n      },\n      children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          flex: '1',\n          minWidth: '300px',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '10px',\n            color: '#495057'\n          },\n          children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: originalImage,\n          alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n          style: {\n            width: '100%',\n            height: 'auto',\n            maxHeight: '400px',\n            border: '2px solid #007bff',\n            borderRadius: '8px',\n            objectFit: 'contain'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          flex: '1',\n          minWidth: '300px',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '10px',\n            color: '#495057'\n          },\n          children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '300px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '2px dashed #ccc',\n            borderRadius: '8px',\n            color: '#6c757d'\n          },\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 15\n        }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '300px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '2px solid #dc3545',\n            borderRadius: '8px',\n            color: '#dc3545',\n            backgroundColor: '#f8d7da'\n          },\n          children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: `http://localhost:8001/result/${result.enhanced_url}`,\n          alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n          onLoad: handleImageLoad,\n          onError: handleImageError,\n          style: {\n            width: '100%',\n            height: 'auto',\n            maxHeight: '400px',\n            border: '2px solid #28a745',\n            borderRadius: '8px',\n            objectFit: 'contain',\n            display: imageLoaded ? 'block' : 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 9\n    }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginTop: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '10px 20px',\n          backgroundColor: '#28a745',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: 'pointer',\n          fontSize: '16px',\n          marginRight: '10px'\n        },\n        children: \"\\uD83D\\uDCE5 \\u4E0B\\u8F7D\\u589E\\u5F3A\\u56FE\\u50CF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `http://localhost:8001/result/${result.enhanced_url}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          padding: '10px 20px',\n          backgroundColor: '#007bff',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '5px',\n          fontSize: '16px'\n        },\n        children: \"\\uD83D\\uDD0D \\u5728\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"MAS2SsxpKPGhRwLgxWCvAbzcNVs=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "containerRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleMouseDown", "e", "preventDefault", "handleMouseMove", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleMouseUp", "addEventListener", "removeEventListener", "resetSplit", "style", "marginTop", "padding", "border", "borderRadius", "children", "display", "justifyContent", "alignItems", "marginBottom", "flexWrap", "gap", "color", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "backgroundColor", "cursor", "fontSize", "message", "params_used", "marginRight", "gridTemplateColumns", "scale", "use_realesrgan", "sharpening", "toFixed", "denoising", "saturation", "contrast", "brightness", "beauty", "ref", "position", "max<PERSON><PERSON><PERSON>", "userSelect", "textAlign", "height", "src", "alt", "onLoad", "onError", "opacity", "top", "overflow", "objectFit", "boxShadow", "transform", "zIndex", "onMouseDown", "fontWeight", "flex", "min<PERSON><PERSON><PERSON>", "maxHeight", "target", "rel", "textDecoration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const containerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n  };\n\n  const handleMouseMove = (e) => {\n    if (!isDragging || !containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ marginTop: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '10px' }}>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px', flexWrap: 'wrap', gap: '10px' }}>\n        <h2 style={{ color: '#28a745', margin: 0 }}>✅ 处理完成</h2>\n\n        {/* 视图模式切换 */}\n        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: viewMode === 'split' ? '#007bff' : '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: viewMode === 'side-by-side' ? '#007bff' : '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '14px'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {result.message && (\n        <p style={{ color: '#6c757d', marginBottom: '15px' }}>{result.message}</p>\n      )}\n\n      {/* 显示使用的参数 */}\n      {result.params_used && (\n        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>\n            <h4 style={{ margin: 0, marginRight: '10px' }}>处理参数</h4>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              {showParams ? '隐藏' : '显示'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ fontSize: '14px', color: '#495057' }}>\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '8px' }}>\n                <div><strong>超分倍数:</strong> {result.params_used.scale}x</div>\n                <div><strong>AI模型:</strong> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n                <div><strong>锐化:</strong> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n                <div><strong>降噪:</strong> {result.params_used.denoising}</div>\n                <div><strong>饱和度:</strong> {result.params_used.saturation.toFixed(1)}</div>\n                <div><strong>对比度:</strong> {result.params_used.contrast.toFixed(1)}</div>\n                <div><strong>亮度:</strong> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n                <div><strong>美颜:</strong> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 图像对比区域 */}\n      {viewMode === 'split' ? (\n        // 分割线对比模式\n        <div\n          ref={containerRef}\n          style={{\n            position: 'relative',\n            width: '100%',\n            maxWidth: '800px',\n            margin: '0 auto',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none'\n          }}\n        >\n          <div style={{ textAlign: 'center', marginBottom: '15px' }}>\n            <h3 style={{ margin: 0, color: '#495057', fontSize: '18px' }}>拖拽分割线对比原图与增强效果</h3>\n            <p style={{ margin: '5px 0', fontSize: '14px', color: '#6c757d' }}>\n              左侧：原始图像 | 右侧：增强图像 | 当前位置：{splitPosition.toFixed(0)}%\n            </p>\n          </div>\n\n          {originalImage && (\n            <div style={{ position: 'relative', display: 'inline-block', width: '100%' }}>\n              {/* 增强图像作为背景 */}\n              {imageError ? (\n                <div style={{\n                  width: '100%',\n                  height: '400px',\n                  border: '2px dashed #dc3545',\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#dc3545',\n                  backgroundColor: '#f8d7da'\n                }}>\n                  增强图像加载失败\n                </div>\n              ) : (\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    width: '100%',\n                    maxWidth: '800px',\n                    height: 'auto',\n                    border: '2px solid #28a745',\n                    borderRadius: '8px',\n                    display: 'block',\n                    opacity: imageLoaded ? 1 : 0.5\n                  }}\n                />\n              )}\n\n              {/* 原始图像覆盖层 */}\n              <div\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: `${splitPosition}%`,\n                  height: '100%',\n                  overflow: 'hidden',\n                  borderRadius: '8px 0 0 8px'\n                }}\n              >\n                <img\n                  src={originalImage}\n                  alt=\"原始图像\"\n                  style={{\n                    width: `${100 * 100 / splitPosition}%`,\n                    height: '100%',\n                    objectFit: 'cover',\n                    border: '2px solid #ddd',\n                    borderRadius: '8px'\n                  }}\n                />\n              </div>\n\n              {/* 分割线 */}\n              <div\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: `${splitPosition}%`,\n                  width: '4px',\n                  height: '100%',\n                  backgroundColor: '#fff',\n                  cursor: 'ew-resize',\n                  boxShadow: '0 0 10px rgba(0,0,0,0.3)',\n                  transform: 'translateX(-2px)',\n                  zIndex: 10\n                }}\n                onMouseDown={handleMouseDown}\n              >\n                {/* 分割线手柄 */}\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    width: '20px',\n                    height: '40px',\n                    backgroundColor: '#007bff',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '12px',\n                    fontWeight: 'bold'\n                  }}\n                >\n                  ⟷\n                </div>\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  color: '#6c757d',\n                  backgroundColor: 'rgba(255,255,255,0.8)',\n                  padding: '10px',\n                  borderRadius: '5px'\n                }}>\n                  加载中...\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      ) : (\n        // 并排对比模式\n        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', justifyContent: 'center' }}>\n          {/* 原始图像 */}\n          {originalImage && (\n            <div style={{ textAlign: 'center', flex: '1', minWidth: '300px', maxWidth: '400px' }}>\n              <h3 style={{ marginBottom: '10px', color: '#495057' }}>原始图像</h3>\n              <img\n                src={originalImage}\n                alt=\"原始图像\"\n                style={{\n                  width: '100%',\n                  height: 'auto',\n                  maxHeight: '400px',\n                  border: '2px solid #007bff',\n                  borderRadius: '8px',\n                  objectFit: 'contain'\n                }}\n              />\n            </div>\n          )}\n\n          {/* 增强图像 */}\n          <div style={{ textAlign: 'center', flex: '1', minWidth: '300px', maxWidth: '400px' }}>\n            <h3 style={{ marginBottom: '10px', color: '#495057' }}>增强图像</h3>\n\n            {!imageLoaded && !imageError && (\n              <div style={{\n                width: '100%',\n                height: '300px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '2px dashed #ccc',\n                borderRadius: '8px',\n                color: '#6c757d'\n              }}>\n                正在加载图像...\n              </div>\n            )}\n\n            {imageError && (\n              <div style={{\n                width: '100%',\n                height: '300px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '2px solid #dc3545',\n                borderRadius: '8px',\n                color: '#dc3545',\n                backgroundColor: '#f8d7da'\n              }}>\n                图像加载失败\n              </div>\n            )}\n\n            <img\n              src={`http://localhost:8001/result/${result.enhanced_url}`}\n              alt=\"增强图像\"\n              onLoad={handleImageLoad}\n              onError={handleImageError}\n              style={{\n                width: '100%',\n                height: 'auto',\n                maxHeight: '400px',\n                border: '2px solid #28a745',\n                borderRadius: '8px',\n                objectFit: 'contain',\n                display: imageLoaded ? 'block' : 'none'\n              }}\n            />\n          </div>\n        </div>\n      )}\n\n      {imageLoaded && (\n        <div style={{ textAlign: 'center', marginTop: '20px' }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '10px 20px',\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '5px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              marginRight: '10px'\n            }}\n          >\n            📥 下载增强图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '10px 20px',\n              backgroundColor: '#007bff',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '5px',\n              fontSize: '16px'\n            }}\n          >\n            🔍 在新窗口查看\n          </a>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMqB,YAAY,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5BZ,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7BX,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,gCAAgCtB,MAAM,CAACuB,YAAY,EAAE;IACjEJ,IAAI,CAACK,QAAQ,GAAG,YAAYxB,MAAM,CAACyB,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,eAAe,GAAIC,CAAC,IAAK;IAC7BnB,aAAa,CAAC,IAAI,CAAC;IACnBmB,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,eAAe,GAAIF,CAAC,IAAK;IAC7B,IAAI,CAACpB,UAAU,IAAI,CAACI,YAAY,CAACmB,OAAO,EAAE;IAE1C,MAAMC,IAAI,GAAGpB,YAAY,CAACmB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IACzD,MAAMC,CAAC,GAAGN,CAAC,CAACO,OAAO,GAAGH,IAAI,CAACI,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGN,CAAC,GAAGF,IAAI,CAACS,KAAK,GAAI,GAAG,CAAC,CAAC;IACrElC,gBAAgB,CAAC8B,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BjC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACAhB,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACdS,QAAQ,CAAC0B,gBAAgB,CAAC,WAAW,EAAEb,eAAe,CAAC;MACvDb,QAAQ,CAAC0B,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MACnD,OAAO,MAAM;QACXzB,QAAQ,CAAC2B,mBAAmB,CAAC,WAAW,EAAEd,eAAe,CAAC;QAC1Db,QAAQ,CAAC2B,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAAClC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvBtC,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEZ,OAAA;IAAKmD,KAAK,EAAE;MAAEC,SAAS,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE,gBAAgB;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACjGxD,OAAA;MAAKmD,KAAK,EAAE;QAAEM,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAN,QAAA,gBAC1IxD,OAAA;QAAImD,KAAK,EAAE;UAAEY,KAAK,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAR,QAAA,EAAC;MAAM;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGvDpE,OAAA;QAAKmD,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEK,GAAG,EAAE,KAAK;UAAED,QAAQ,EAAE;QAAO,CAAE;QAAAL,QAAA,gBAC5DxD,OAAA;UACEqE,OAAO,EAAEA,CAAA,KAAMrD,WAAW,CAAC,OAAO,CAAE;UACpCmC,KAAK,EAAE;YACLE,OAAO,EAAE,UAAU;YACnBiB,eAAe,EAAEvD,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;YAC7DgD,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpE,OAAA;UACEqE,OAAO,EAAEA,CAAA,KAAMrD,WAAW,CAAC,cAAc,CAAE;UAC3CmC,KAAK,EAAE;YACLE,OAAO,EAAE,UAAU;YACnBiB,eAAe,EAAEvD,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,SAAS;YACpEgD,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRrD,QAAQ,KAAK,OAAO,iBACnBf,OAAA;UACEqE,OAAO,EAAEnB,UAAW;UACpBC,KAAK,EAAE;YACLE,OAAO,EAAE,UAAU;YACnBiB,eAAe,EAAE,SAAS;YAC1BP,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlE,MAAM,CAACuE,OAAO,iBACbzE,OAAA;MAAGmD,KAAK,EAAE;QAAEY,KAAK,EAAE,SAAS;QAAEH,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,EAAEtD,MAAM,CAACuE;IAAO;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAC1E,EAGAlE,MAAM,CAACwE,WAAW,iBACjB1E,OAAA;MAAKmD,KAAK,EAAE;QAAES,YAAY,EAAE,MAAM;QAAEP,OAAO,EAAE,MAAM;QAAEiB,eAAe,EAAE,SAAS;QAAEf,YAAY,EAAE;MAAM,CAAE;MAAAC,QAAA,gBACrGxD,OAAA;QAAKmD,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAC1ExD,OAAA;UAAImD,KAAK,EAAE;YAAEa,MAAM,EAAE,CAAC;YAAEW,WAAW,EAAE;UAAO,CAAE;UAAAnB,QAAA,EAAC;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDpE,OAAA;UACEqE,OAAO,EAAEA,CAAA,KAAM3D,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1C0C,KAAK,EAAE;YACLE,OAAO,EAAE,SAAS;YAClBiB,eAAe,EAAE,SAAS;YAC1BP,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,EAED/C,UAAU,GAAG,IAAI,GAAG;QAAI;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL3D,UAAU,iBACTT,OAAA;QAAKmD,KAAK,EAAE;UAAEqB,QAAQ,EAAE,MAAM;UAAET,KAAK,EAAE;QAAU,CAAE;QAAAP,QAAA,eACjDxD,OAAA;UAAKmD,KAAK,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEmB,mBAAmB,EAAE,sCAAsC;YAAEd,GAAG,EAAE;UAAM,CAAE;UAAAN,QAAA,gBACvGxD,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,MAAM,CAACwE,WAAW,CAACG,KAAK,EAAC,GAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7DpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,MAAM,CAACwE,WAAW,CAACI,cAAc,GAAG,YAAY,GAAG,MAAM;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7FpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAAClE,MAAM,CAACwE,WAAW,CAACK,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnFpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,MAAM,CAACwE,WAAW,CAACO,SAAS;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9DpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,MAAM,CAACwE,WAAW,CAACQ,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3EpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,MAAM,CAACwE,WAAW,CAACS,QAAQ,CAACH,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,MAAM,CAACwE,WAAW,CAACU,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAElF,MAAM,CAACwE,WAAW,CAACU,UAAU;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7GpE,OAAA;YAAAwD,QAAA,gBAAKxD,OAAA;cAAAwD,QAAA,EAAQ;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAAClE,MAAM,CAACwE,WAAW,CAACW,MAAM,GAAG,GAAG,EAAEL,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGArD,QAAQ,KAAK,OAAO;IAAA;IACnB;IACAf,OAAA;MACEsF,GAAG,EAAErE,YAAa;MAClBkC,KAAK,EAAE;QACLoC,QAAQ,EAAE,UAAU;QACpBzC,KAAK,EAAE,MAAM;QACb0C,QAAQ,EAAE,OAAO;QACjBxB,MAAM,EAAE,QAAQ;QAChBO,MAAM,EAAE1D,UAAU,GAAG,WAAW,GAAG,SAAS;QAC5C4E,UAAU,EAAE;MACd,CAAE;MAAAjC,QAAA,gBAEFxD,OAAA;QAAKmD,KAAK,EAAE;UAAEuC,SAAS,EAAE,QAAQ;UAAE9B,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACxDxD,OAAA;UAAImD,KAAK,EAAE;YAAEa,MAAM,EAAE,CAAC;YAAED,KAAK,EAAE,SAAS;YAAES,QAAQ,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFpE,OAAA;UAAGmD,KAAK,EAAE;YAAEa,MAAM,EAAE,OAAO;YAAEQ,QAAQ,EAAE,MAAM;YAAET,KAAK,EAAE;UAAU,CAAE;UAAAP,QAAA,GAAC,0HACxC,EAAC7C,aAAa,CAACqE,OAAO,CAAC,CAAC,CAAC,EAAC,GACrD;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAELjE,aAAa,iBACZH,OAAA;QAAKmD,KAAK,EAAE;UAAEoC,QAAQ,EAAE,UAAU;UAAE9B,OAAO,EAAE,cAAc;UAAEX,KAAK,EAAE;QAAO,CAAE;QAAAU,QAAA,GAE1EjD,UAAU,gBACTP,OAAA;UAAKmD,KAAK,EAAE;YACVL,KAAK,EAAE,MAAM;YACb6C,MAAM,EAAE,OAAO;YACfrC,MAAM,EAAE,oBAAoB;YAC5BC,YAAY,EAAE,KAAK;YACnBE,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBK,KAAK,EAAE,SAAS;YAChBO,eAAe,EAAE;UACnB,CAAE;UAAAd,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAENpE,OAAA;UACE4F,GAAG,EAAE,gCAAgC1F,MAAM,CAACuB,YAAY,EAAG;UAC3DoE,GAAG,EAAC,0BAAM;UACVC,MAAM,EAAE5E,eAAgB;UACxB6E,OAAO,EAAE5E,gBAAiB;UAC1BgC,KAAK,EAAE;YACLL,KAAK,EAAE,MAAM;YACb0C,QAAQ,EAAE,OAAO;YACjBG,MAAM,EAAE,MAAM;YACdrC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBE,OAAO,EAAE,OAAO;YAChBuC,OAAO,EAAE3F,WAAW,GAAG,CAAC,GAAG;UAC7B;QAAE;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,eAGDpE,OAAA;UACEmD,KAAK,EAAE;YACLoC,QAAQ,EAAE,UAAU;YACpBU,GAAG,EAAE,CAAC;YACNxD,IAAI,EAAE,CAAC;YACPK,KAAK,EAAE,GAAGnC,aAAa,GAAG;YAC1BgF,MAAM,EAAE,MAAM;YACdO,QAAQ,EAAE,QAAQ;YAClB3C,YAAY,EAAE;UAChB,CAAE;UAAAC,QAAA,eAEFxD,OAAA;YACE4F,GAAG,EAAEzF,aAAc;YACnB0F,GAAG,EAAC,0BAAM;YACV1C,KAAK,EAAE;cACLL,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAGnC,aAAa,GAAG;cACtCgF,MAAM,EAAE,MAAM;cACdQ,SAAS,EAAE,OAAO;cAClB7C,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE;YAChB;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpE,OAAA;UACEmD,KAAK,EAAE;YACLoC,QAAQ,EAAE,UAAU;YACpBU,GAAG,EAAE,CAAC;YACNxD,IAAI,EAAE,GAAG9B,aAAa,GAAG;YACzBmC,KAAK,EAAE,KAAK;YACZ6C,MAAM,EAAE,MAAM;YACdrB,eAAe,EAAE,MAAM;YACvBC,MAAM,EAAE,WAAW;YACnB6B,SAAS,EAAE,0BAA0B;YACrCC,SAAS,EAAE,kBAAkB;YAC7BC,MAAM,EAAE;UACV,CAAE;UACFC,WAAW,EAAEvE,eAAgB;UAAAwB,QAAA,eAG7BxD,OAAA;YACEmD,KAAK,EAAE;cACLoC,QAAQ,EAAE,UAAU;cACpBU,GAAG,EAAE,KAAK;cACVxD,IAAI,EAAE,KAAK;cACX4D,SAAS,EAAE,uBAAuB;cAClCvD,KAAK,EAAE,MAAM;cACb6C,MAAM,EAAE,MAAM;cACdrB,eAAe,EAAE,SAAS;cAC1Bf,YAAY,EAAE,MAAM;cACpBE,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBK,KAAK,EAAE,OAAO;cACdS,QAAQ,EAAE,MAAM;cAChBgC,UAAU,EAAE;YACd,CAAE;YAAAhD,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL,CAAC/D,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;UAAKmD,KAAK,EAAE;YACVoC,QAAQ,EAAE,UAAU;YACpBU,GAAG,EAAE,KAAK;YACVxD,IAAI,EAAE,KAAK;YACX4D,SAAS,EAAE,uBAAuB;YAClCtC,KAAK,EAAE,SAAS;YAChBO,eAAe,EAAE,uBAAuB;YACxCjB,OAAO,EAAE,MAAM;YACfE,YAAY,EAAE;UAChB,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;IAAA;IAEN;IACApE,OAAA;MAAKmD,KAAK,EAAE;QAAEM,OAAO,EAAE,MAAM;QAAEK,GAAG,EAAE,MAAM;QAAED,QAAQ,EAAE,MAAM;QAAEH,cAAc,EAAE;MAAS,CAAE;MAAAF,QAAA,GAEtFrD,aAAa,iBACZH,OAAA;QAAKmD,KAAK,EAAE;UAAEuC,SAAS,EAAE,QAAQ;UAAEe,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE,OAAO;UAAElB,QAAQ,EAAE;QAAQ,CAAE;QAAAhC,QAAA,gBACnFxD,OAAA;UAAImD,KAAK,EAAE;YAAES,YAAY,EAAE,MAAM;YAAEG,KAAK,EAAE;UAAU,CAAE;UAAAP,QAAA,EAAC;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEpE,OAAA;UACE4F,GAAG,EAAEzF,aAAc;UACnB0F,GAAG,EAAC,0BAAM;UACV1C,KAAK,EAAE;YACLL,KAAK,EAAE,MAAM;YACb6C,MAAM,EAAE,MAAM;YACdgB,SAAS,EAAE,OAAO;YAClBrD,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnB4C,SAAS,EAAE;UACb;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGDpE,OAAA;QAAKmD,KAAK,EAAE;UAAEuC,SAAS,EAAE,QAAQ;UAAEe,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE,OAAO;UAAElB,QAAQ,EAAE;QAAQ,CAAE;QAAAhC,QAAA,gBACnFxD,OAAA;UAAImD,KAAK,EAAE;YAAES,YAAY,EAAE,MAAM;YAAEG,KAAK,EAAE;UAAU,CAAE;UAAAP,QAAA,EAAC;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAE/D,CAAC/D,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;UAAKmD,KAAK,EAAE;YACVL,KAAK,EAAE,MAAM;YACb6C,MAAM,EAAE,OAAO;YACflC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBJ,MAAM,EAAE,iBAAiB;YACzBC,YAAY,EAAE,KAAK;YACnBQ,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EAEA7D,UAAU,iBACTP,OAAA;UAAKmD,KAAK,EAAE;YACVL,KAAK,EAAE,MAAM;YACb6C,MAAM,EAAE,OAAO;YACflC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBJ,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBQ,KAAK,EAAE,SAAS;YAChBO,eAAe,EAAE;UACnB,CAAE;UAAAd,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAEDpE,OAAA;UACE4F,GAAG,EAAE,gCAAgC1F,MAAM,CAACuB,YAAY,EAAG;UAC3DoE,GAAG,EAAC,0BAAM;UACVC,MAAM,EAAE5E,eAAgB;UACxB6E,OAAO,EAAE5E,gBAAiB;UAC1BgC,KAAK,EAAE;YACLL,KAAK,EAAE,MAAM;YACb6C,MAAM,EAAE,MAAM;YACdgB,SAAS,EAAE,OAAO;YAClBrD,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnB4C,SAAS,EAAE,SAAS;YACpB1C,OAAO,EAAEpD,WAAW,GAAG,OAAO,GAAG;UACnC;QAAE;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA/D,WAAW,iBACVL,OAAA;MAAKmD,KAAK,EAAE;QAAEuC,SAAS,EAAE,QAAQ;QAAEtC,SAAS,EAAE;MAAO,CAAE;MAAAI,QAAA,gBACrDxD,OAAA;QACEqE,OAAO,EAAEjD,aAAc;QACvB+B,KAAK,EAAE;UACLE,OAAO,EAAE,WAAW;UACpBiB,eAAe,EAAE,SAAS;UAC1BP,KAAK,EAAE,OAAO;UACdT,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBgB,MAAM,EAAE,SAAS;UACjBC,QAAQ,EAAE,MAAM;UAChBG,WAAW,EAAE;QACf,CAAE;QAAAnB,QAAA,EACH;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpE,OAAA;QACEwB,IAAI,EAAE,gCAAgCtB,MAAM,CAACuB,YAAY,EAAG;QAC5DmF,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,qBAAqB;QACzB1D,KAAK,EAAE;UACLE,OAAO,EAAE,WAAW;UACpBiB,eAAe,EAAE,SAAS;UAC1BP,KAAK,EAAE,OAAO;UACd+C,cAAc,EAAE,MAAM;UACtBvD,YAAY,EAAE,KAAK;UACnBiB,QAAQ,EAAE;QACZ,CAAE;QAAAhB,QAAA,EACH;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChE,EAAA,CA1ZIH,UAAU;AAAA8G,EAAA,GAAV9G,UAAU;AA4ZhB,eAAeA,UAAU;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}