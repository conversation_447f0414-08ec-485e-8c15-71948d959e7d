{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const handleUpload = async formData => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比和获取图像信息\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        setOriginalImage(e.target.result);\n        // 获取图像信息\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          fontSize: '13px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u89C6\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u5DE5\\u5177\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer',\n            padding: '5px 10px',\n            borderRadius: '4px'\n          },\n          children: \"\\u5E2E\\u52A9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ccc',\n              fontSize: '12px'\n            },\n            children: result ? '已处理' : '等待上传图像'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [\"\\u9519\\u8BEF: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '8px',\n              textAlign: 'center',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '30px',\n                height: '30px',\n                border: '3px solid #333',\n                borderTop: '3px solid #4a90e2',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite',\n                margin: '0 auto'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), result ? /*#__PURE__*/_jsxDEV(ResultView, {\n            result: result,\n            originalImage: originalImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u5C31\\u7EEA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"RealESRGAN v2.1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"RavOJqoDvKIC3V1TFpTBDCe1kuU=\");\n_c = App;\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "handleUpload", "formData", "startTime", "Date", "now", "file", "get", "reader", "FileReader", "onload", "e", "target", "img", "Image", "setImageInfo", "name", "size", "toFixed", "dimensions", "width", "height", "type", "src", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "endTime", "setProcessingTime", "err", "console", "message", "handleReset", "style", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "borderBottom", "alignItems", "padding", "color", "gap", "marginRight", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "textAlign", "fontSize", "fontWeight", "cursor", "position", "onClick", "border", "justifyContent", "top", "left", "right", "zIndex", "transform", "marginBottom", "borderTop", "animation", "margin", "borderLeft", "onUpload", "jsx", "_c", "globalStyles", "document", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n\n  const handleUpload = async (formData) => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比和获取图像信息\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setOriginalImage(e.target.result);\n        // 获取图像信息\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>文件</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>编辑</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>视图</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>工具</span>\n          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>帮助</span>\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              新建\n            </button>\n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {result ? '已处理' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <ResultView result={result} originalImage={originalImage} />\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>就绪</span>\n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n\n      {/* CSS动画 */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMe,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5BT,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMQ,IAAI,GAAGJ,QAAQ,CAACK,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACrBX,gBAAgB,CAACW,CAAC,CAACC,MAAM,CAACnB,MAAM,CAAC;QACjC;QACA,MAAMoB,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACH,MAAM,GAAG,MAAM;UACjBK,YAAY,CAAC;YACXC,IAAI,EAAEV,IAAI,CAACU,IAAI;YACfC,IAAI,EAAE,CAACX,IAAI,CAACW,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;YAClDC,UAAU,EAAE,GAAGN,GAAG,CAACO,KAAK,MAAMP,GAAG,CAACQ,MAAM,EAAE;YAC1CC,IAAI,EAAEhB,IAAI,CAACgB;UACb,CAAC,CAAC;QACJ,CAAC;QACDT,GAAG,CAACU,GAAG,GAAGZ,CAAC,CAACC,MAAM,CAACnB,MAAM;MAC3B,CAAC;MACDe,MAAM,CAACgB,aAAa,CAAClB,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE1B;MACR,CAAC,CAAC;MAEF,IAAI,CAACuB,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,MAAMI,OAAO,GAAG/B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B+B,iBAAiB,CAAC,CAAC,CAACD,OAAO,GAAGhC,SAAS,IAAI,IAAI,EAAEe,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5DxB,SAAS,CAACwC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,OAAO,EAAEwC,GAAG,CAAC;MAC3BvC,QAAQ,CAACuC,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR3C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4C,WAAW,GAAGA,CAAA,KAAM;IACxB9C,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,oBACEV,OAAA;IAAKmD,KAAK,EAAE;MACVpB,MAAM,EAAE,OAAO;MACfqB,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAzD,OAAA;MAAKmD,KAAK,EAAE;QACVpB,MAAM,EAAE,MAAM;QACdqB,eAAe,EAAE,SAAS;QAC1BM,YAAY,EAAE,gBAAgB;QAC9BJ,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAJ,QAAA,gBAEAzD,OAAA;QAAKmD,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC/DzD,OAAA;UAAKmD,KAAK,EAAE;YAAErB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEiC,YAAY,EAAE,KAAK;YAAEZ,eAAe,EAAE;UAAU;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGpE,OAAA;UAAKmD,KAAK,EAAE;YAAErB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEiC,YAAY,EAAE,KAAK;YAAEZ,eAAe,EAAE;UAAU;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGpE,OAAA;UAAKmD,KAAK,EAAE;YAAErB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEiC,YAAY,EAAE,KAAK;YAAEZ,eAAe,EAAE;UAAU;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNpE,OAAA;QAAKmD,KAAK,EAAE;UACVkB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNpE,OAAA;QAAKmD,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,MAAM;UAAES,QAAQ,EAAE;QAAO,CAAE;QAAAd,QAAA,gBAC7DzD,OAAA;UAAMmD,KAAK,EAAE;YAAEsB,MAAM,EAAE,SAAS;YAAEb,OAAO,EAAE,UAAU;YAAEI,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpE,OAAA;UAAMmD,KAAK,EAAE;YAAEsB,MAAM,EAAE,SAAS;YAAEb,OAAO,EAAE,UAAU;YAAEI,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpE,OAAA;UAAMmD,KAAK,EAAE;YAAEsB,MAAM,EAAE,SAAS;YAAEb,OAAO,EAAE,UAAU;YAAEI,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpE,OAAA;UAAMmD,KAAK,EAAE;YAAEsB,MAAM,EAAE,SAAS;YAAEb,OAAO,EAAE,UAAU;YAAEI,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvFpE,OAAA;UAAMmD,KAAK,EAAE;YAAEsB,MAAM,EAAE,SAAS;YAAEb,OAAO,EAAE,UAAU;YAAEI,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpE,OAAA;MAAKmD,KAAK,EAAE;QACVkB,IAAI,EAAE,CAAC;QACPf,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEAzD,OAAA;QAAKmD,KAAK,EAAE;UACVkB,IAAI,EAAE,CAAC;UACPjB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBmB,QAAQ,EAAE;QACZ,CAAE;QAAAjB,QAAA,gBAEAzD,OAAA;UAAKmD,KAAK,EAAE;YACVpB,MAAM,EAAE,MAAM;YACdqB,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBE,GAAG,EAAE;UACP,CAAE;UAAAL,QAAA,gBACAzD,OAAA;YACE2E,OAAO,EAAEzB,WAAY;YACrBC,KAAK,EAAE;cACLS,OAAO,EAAE,UAAU;cACnBR,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACde,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBS,MAAM,EAAE,SAAS;cACjBF,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,EACH;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpE,OAAA;YAAKmD,KAAK,EAAE;cAAErB,KAAK,EAAE,KAAK;cAAEC,MAAM,EAAE,MAAM;cAAEqB,eAAe,EAAE;YAAO;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7EpE,OAAA;YAAMmD,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAEU,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAC9CtD,MAAM,GAAG,KAAK,GAAG;UAAQ;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNpE,OAAA;UAAKmD,KAAK,EAAE;YACVkB,IAAI,EAAE,CAAC;YACPf,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBkB,cAAc,EAAE,QAAQ;YACxBjB,OAAO,EAAE,MAAM;YACfc,QAAQ,EAAE;UACZ,CAAE;UAAAjB,QAAA,GACClD,KAAK,iBACJP,OAAA;YAAKmD,KAAK,EAAE;cACVuB,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZC,KAAK,EAAE,MAAM;cACb5B,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfI,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,MAAM;cAChBU,MAAM,EAAE;YACV,CAAE;YAAAxB,QAAA,GAAC,gBACG,EAAClD,KAAK;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,EAEA/D,SAAS,iBACRL,OAAA;YAAKmD,KAAK,EAAE;cACVuB,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXG,SAAS,EAAE,uBAAuB;cAClC9B,eAAe,EAAE,iBAAiB;cAClCS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfI,YAAY,EAAE,KAAK;cACnBM,SAAS,EAAE,QAAQ;cACnBW,MAAM,EAAE;YACV,CAAE;YAAAxB,QAAA,gBACAzD,OAAA;cAAKmD,KAAK,EAAE;gBAAEgC,YAAY,EAAE;cAAO,CAAE;cAAA1B,QAAA,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDpE,OAAA;cAAKmD,KAAK,EAAE;gBACVrB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACd6C,MAAM,EAAE,gBAAgB;gBACxBQ,SAAS,EAAE,mBAAmB;gBAC9BpB,YAAY,EAAE,KAAK;gBACnBqB,SAAS,EAAE,yBAAyB;gBACpCC,MAAM,EAAE;cACV;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAjE,MAAM,gBACLH,OAAA,CAACF,UAAU;YAACK,MAAM,EAAEA,MAAO;YAACM,aAAa,EAAEA;UAAc;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5DpE,OAAA;YAAKmD,KAAK,EAAE;cACVmB,SAAS,EAAE,QAAQ;cACnBT,KAAK,EAAE,MAAM;cACbU,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,gBACAzD,OAAA;cAAKmD,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEY,YAAY,EAAE;cAAO,CAAE;cAAA1B,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEpE,OAAA;cAAAyD,QAAA,EAAK;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpE,OAAA;QAAKmD,KAAK,EAAE;UACVrB,KAAK,EAAE,OAAO;UACdsB,eAAe,EAAE,SAAS;UAC1BmC,UAAU,EAAE,gBAAgB;UAC5BjC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEAzD,OAAA;UAAKmD,KAAK,EAAE;YACVpB,MAAM,EAAE,MAAM;YACdqB,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNpE,OAAA;UAAKmD,KAAK,EAAE;YACVkB,IAAI,EAAE,CAAC;YACPb,QAAQ,EAAE,MAAM;YAChBI,OAAO,EAAE;UACX,CAAE;UAAAH,QAAA,eACAzD,OAAA,CAACH,UAAU;YAAC2F,QAAQ,EAAE7E,YAAa;YAACN,SAAS,EAAEA;UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpE,OAAA;MAAKmD,KAAK,EAAE;QACVpB,MAAM,EAAE,MAAM;QACdqB,eAAe,EAAE,MAAM;QACvBgC,SAAS,EAAE,gBAAgB;QAC3B9B,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbU,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACAzD,OAAA;QAAAyD,QAAA,EAAM;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACfpE,OAAA;QAAKmD,KAAK,EAAE;UAAEkB,IAAI,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BpE,OAAA;QAAAyD,QAAA,EAAM;MAAe;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNpE,OAAA;MAAOyF,GAAG;MAAAhC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAClE,EAAA,CAtRQD,GAAG;AAAAyF,EAAA,GAAHzF,GAAG;AAwRZ,eAAeA,GAAG;;AAElB;AACA,MAAM0F,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,WAAW,GAAGJ,YAAY;EACvCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;AACzC;AAAC,IAAAH,EAAA;AAAAQ,YAAA,CAAAR,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}