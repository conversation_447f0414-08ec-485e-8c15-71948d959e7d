{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const [viewMode, setViewMode] = useState('split');\n  const [rotation, setRotation] = useState(0);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n  const handleUpload = async formData => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        setOriginalImage(e.target.result);\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177 - \\u5DF2\\u542F\\u52A8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            color: '#888',\n            fontSize: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              marginBottom: '20px'\n            },\n            children: \"\\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '24px',\n              color: '#4a90e2',\n              marginBottom: '10px'\n            },\n            children: \"\\u5E94\\u7528\\u5DF2\\u6210\\u529F\\u542F\\u52A8\\uFF01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"AI\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\\u6B63\\u5728\\u8FD0\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              marginTop: '10px',\n              color: '#666'\n            },\n            children: \"\\u5982\\u679C\\u60A8\\u770B\\u5230\\u8FD9\\u4E2A\\u9875\\u9762\\uFF0C\\u8BF4\\u660EReact\\u5E94\\u7528\\u5DE5\\u4F5C\\u6B63\\u5E38\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              marginTop: '20px',\n              color: '#4a90e2'\n            },\n            children: [\"\\u524D\\u7AEF\\u670D\\u52A1: \\u2705 \\u6B63\\u5E38\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 25\n            }, this), \"\\u540E\\u7AEF\\u670D\\u52A1: \\u2705 \\u6B63\\u5E38\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 25\n            }, this), \"\\u754C\\u9762\\u6E32\\u67D3: \\u2705 \\u6B63\\u5E38\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u7CFB\\u7EDF\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#e0e0e0',\n              fontSize: '13px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#4a90e2',\n                marginBottom: '15px'\n              },\n              children: \"\\u2705 \\u7CFB\\u7EDF\\u68C0\\u67E5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 React \\u5E94\\u7528: \\u6B63\\u5E38\\u8FD0\\u884C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u524D\\u7AEF\\u670D\\u52A1: \\u7AEF\\u53E33000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u540E\\u7AEF\\u670D\\u52A1: \\u7AEF\\u53E38001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u754C\\u9762\\u6E32\\u67D3: \\u6210\\u529F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#4a90e2',\n                marginTop: '20px',\n                marginBottom: '15px'\n              },\n              children: \"\\uD83D\\uDD27 \\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u5E94\\u7528\\u5DF2\\u6210\\u529F\\u542F\\u52A8\\u5E76\\u6B63\\u5728\\u8FD0\\u884C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6240\\u6709\\u670D\\u52A1\\u90FD\\u5DF2\\u5C31\\u7EEA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              style: {\n                marginTop: '20px',\n                padding: '10px 16px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                width: '100%'\n              },\n              children: \"\\u5237\\u65B0\\u9875\\u9762\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u2705 \\u7CFB\\u7EDF\\u6B63\\u5E38\\u8FD0\\u884C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"RealESRGAN v2.1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"GkmyJuU5iK16dA3VuAq//3myZjs=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "showMenu", "setShowMenu", "imageInfo", "setImageInfo", "processingTime", "setProcessingTime", "zoomLevel", "setZoomLevel", "viewMode", "setViewMode", "rotation", "setRotation", "fileInputRef", "handleKeyDown", "e", "ctrl<PERSON>ey", "key", "preventDefault", "current", "click", "handleReset", "downloadImage", "prev", "Math", "min", "max", "document", "addEventListener", "removeEventListener", "handleUpload", "formData", "startTime", "Date", "now", "file", "get", "reader", "FileReader", "onload", "target", "img", "Image", "name", "size", "toFixed", "dimensions", "width", "height", "type", "src", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "endTime", "err", "console", "message", "enhanced_image", "link", "createElement", "href", "download", "style", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "borderBottom", "alignItems", "padding", "color", "gap", "marginRight", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "textAlign", "fontSize", "fontWeight", "justifyContent", "marginBottom", "marginTop", "borderLeft", "onClick", "window", "location", "reload", "border", "cursor", "borderTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const [viewMode, setViewMode] = useState('split');\n  const [rotation, setRotation] = useState(0);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  const handleUpload = async (formData) => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setOriginalImage(e.target.result);\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具 - 已启动\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}>\n          <div style={{\n            textAlign: 'center',\n            color: '#888',\n            fontSize: '16px'\n          }}>\n            <div style={{ fontSize: '48px', marginBottom: '20px' }}>🎉</div>\n            <div style={{ fontSize: '24px', color: '#4a90e2', marginBottom: '10px' }}>\n              应用已成功启动！\n            </div>\n            <div>AI图像增强工具正在运行</div>\n            <div style={{ fontSize: '14px', marginTop: '10px', color: '#666' }}>\n              如果您看到这个页面，说明React应用工作正常\n            </div>\n            <div style={{ fontSize: '12px', marginTop: '20px', color: '#4a90e2' }}>\n              前端服务: ✅ 正常<br/>\n              后端服务: ✅ 正常<br/>\n              界面渲染: ✅ 正常\n            </div>\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            系统状态\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '15px'\n          }}>\n            <div style={{ color: '#e0e0e0', fontSize: '13px' }}>\n              <h3 style={{ color: '#4a90e2', marginBottom: '15px' }}>✅ 系统检查</h3>\n              <p>• React 应用: 正常运行</p>\n              <p>• 前端服务: 端口3000</p>\n              <p>• 后端服务: 端口8001</p>\n              <p>• 界面渲染: 成功</p>\n              \n              <h3 style={{ color: '#4a90e2', marginTop: '20px', marginBottom: '15px' }}>🔧 状态</h3>\n              <p>应用已成功启动并正在运行</p>\n              <p>所有服务都已就绪</p>\n              \n              <button\n                onClick={() => window.location.reload()}\n                style={{\n                  marginTop: '20px',\n                  padding: '10px 16px',\n                  backgroundColor: '#28a745',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                  fontSize: '12px',\n                  width: '100%'\n                }}\n              >\n                刷新页面\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>✅ 系统正常运行</span>\n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM6B,YAAY,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAD,SAAS,CAAC,MAAM;IACd,MAAM6B,aAAa,GAAIC,CAAC,IAAK;MAC3B,IAAIA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClB,IAAIL,YAAY,CAACM,OAAO,EAAE;UACxBN,YAAY,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9B;MACF;MACA,IAAIL,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBG,WAAW,CAAC,CAAC;MACf;MACA,IAAIN,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,IAAIxB,MAAM,EAAE;QACxCsB,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBI,aAAa,CAAC,CAAC;MACjB;MACA,IAAIP,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBV,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;MAChD;MACA,IAAIR,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBV,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;MAC/C;MACA,IAAIR,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBV,YAAY,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;IAEDmB,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEd,aAAa,CAAC;IACnD,OAAO,MAAMa,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEf,aAAa,CAAC;EACrE,CAAC,EAAE,CAACrB,MAAM,CAAC,CAAC;EAEZ,MAAMqC,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5BtC,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,MAAMqC,IAAI,GAAGJ,QAAQ,CAACK,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIxB,CAAC,IAAK;QACrBf,gBAAgB,CAACe,CAAC,CAACyB,MAAM,CAAC/C,MAAM,CAAC;QACjC,MAAMgD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACF,MAAM,GAAG,MAAM;UACjBnC,YAAY,CAAC;YACXuC,IAAI,EAAER,IAAI,CAACQ,IAAI;YACfC,IAAI,EAAE,CAACT,IAAI,CAACS,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;YAClDC,UAAU,EAAE,GAAGL,GAAG,CAACM,KAAK,MAAMN,GAAG,CAACO,MAAM,EAAE;YAC1CC,IAAI,EAAEd,IAAI,CAACc;UACb,CAAC,CAAC;QACJ,CAAC;QACDR,GAAG,CAACS,GAAG,GAAGnC,CAAC,CAACyB,MAAM,CAAC/C,MAAM;MAC3B,CAAC;MACD4C,MAAM,CAACc,aAAa,CAAChB,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAExB;MACR,CAAC,CAAC;MAEF,IAAI,CAACqB,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,MAAMI,OAAO,GAAG7B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B5B,iBAAiB,CAAC,CAAC,CAACwD,OAAO,GAAG9B,SAAS,IAAI,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5DnD,SAAS,CAACmE,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACnE,KAAK,CAAC,OAAO,EAAEkE,GAAG,CAAC;MAC3BjE,QAAQ,CAACiE,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRrE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI7B,MAAM,IAAIA,MAAM,CAACyE,cAAc,EAAE;MACnC,MAAMC,IAAI,GAAGxC,QAAQ,CAACyC,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,yBAAyB5E,MAAM,CAACyE,cAAc,EAAE;MAC5DC,IAAI,CAACG,QAAQ,GAAG,YAAYrC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC5CiC,IAAI,CAAC/C,KAAK,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB3B,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;IACtBI,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,YAAY,CAAC,GAAG,CAAC;IACjBE,WAAW,CAAC,OAAO,CAAC;IACpBE,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EACD,oBACEtB,OAAA;IAAKiF,KAAK,EAAE;MACVvB,MAAM,EAAE,OAAO;MACfwB,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAvF,OAAA;MAAKiF,KAAK,EAAE;QACVvB,MAAM,EAAE,MAAM;QACdwB,eAAe,EAAE,SAAS;QAC1BM,YAAY,EAAE,gBAAgB;QAC9BJ,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAJ,QAAA,gBAEAvF,OAAA;QAAKiF,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC/DvF,OAAA;UAAKiF,KAAK,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEoC,YAAY,EAAE,KAAK;YAAEZ,eAAe,EAAE;UAAU;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGlG,OAAA;UAAKiF,KAAK,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEoC,YAAY,EAAE,KAAK;YAAEZ,eAAe,EAAE;UAAU;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGlG,OAAA;UAAKiF,KAAK,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEoC,YAAY,EAAE,KAAK;YAAEZ,eAAe,EAAE;UAAU;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNlG,OAAA;QAAKiF,KAAK,EAAE;UACVkB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAKiF,KAAK,EAAE;QACVkB,IAAI,EAAE,CAAC;QACPf,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEAvF,OAAA;QAAKiF,KAAK,EAAE;UACVkB,IAAI,EAAE,CAAC;UACPjB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfK,UAAU,EAAE,QAAQ;UACpBc,cAAc,EAAE;QAClB,CAAE;QAAAhB,QAAA,eACAvF,OAAA;UAAKiF,KAAK,EAAE;YACVmB,SAAS,EAAE,QAAQ;YACnBT,KAAK,EAAE,MAAM;YACbU,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,gBACAvF,OAAA;YAAKiF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEG,YAAY,EAAE;YAAO,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChElG,OAAA;YAAKiF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEV,KAAK,EAAE,SAAS;cAAEa,YAAY,EAAE;YAAO,CAAE;YAAAjB,QAAA,EAAC;UAE1E;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlG,OAAA;YAAAuF,QAAA,EAAK;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBlG,OAAA;YAAKiF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEI,SAAS,EAAE,MAAM;cAAEd,KAAK,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlG,OAAA;YAAKiF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEI,SAAS,EAAE,MAAM;cAAEd,KAAK,EAAE;YAAU,CAAE;YAAAJ,QAAA,GAAC,+CAC3D,eAAAvF,OAAA;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,iDACL,eAAAlG,OAAA;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,iDAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlG,OAAA;QAAKiF,KAAK,EAAE;UACVxB,KAAK,EAAE,OAAO;UACdyB,eAAe,EAAE,SAAS;UAC1BwB,UAAU,EAAE,gBAAgB;UAC5BtB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEAvF,OAAA;UAAKiF,KAAK,EAAE;YACVvB,MAAM,EAAE,MAAM;YACdwB,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNlG,OAAA;UAAKiF,KAAK,EAAE;YACVkB,IAAI,EAAE,CAAC;YACPb,QAAQ,EAAE,MAAM;YAChBI,OAAO,EAAE;UACX,CAAE;UAAAH,QAAA,eACAvF,OAAA;YAAKiF,KAAK,EAAE;cAAEU,KAAK,EAAE,SAAS;cAAEU,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,gBACjDvF,OAAA;cAAIiF,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEa,YAAY,EAAE;cAAO,CAAE;cAAAjB,QAAA,EAAC;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClElG,OAAA;cAAAuF,QAAA,EAAG;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvBlG,OAAA;cAAAuF,QAAA,EAAG;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrBlG,OAAA;cAAAuF,QAAA,EAAG;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrBlG,OAAA;cAAAuF,QAAA,EAAG;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEjBlG,OAAA;cAAIiF,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEc,SAAS,EAAE,MAAM;gBAAED,YAAY,EAAE;cAAO,CAAE;cAAAjB,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpFlG,OAAA;cAAAuF,QAAA,EAAG;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnBlG,OAAA;cAAAuF,QAAA,EAAG;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEflG,OAAA;cACE2G,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxC7B,KAAK,EAAE;gBACLwB,SAAS,EAAE,MAAM;gBACjBf,OAAO,EAAE,WAAW;gBACpBR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,OAAO;gBACdoB,MAAM,EAAE,MAAM;gBACdjB,YAAY,EAAE,KAAK;gBACnBkB,MAAM,EAAE,SAAS;gBACjBX,QAAQ,EAAE,MAAM;gBAChB5C,KAAK,EAAE;cACT,CAAE;cAAA8B,QAAA,EACH;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAKiF,KAAK,EAAE;QACVvB,MAAM,EAAE,MAAM;QACdwB,eAAe,EAAE,MAAM;QACvB+B,SAAS,EAAE,gBAAgB;QAC3B7B,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbU,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACAvF,OAAA;QAAAuF,QAAA,EAAM;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrBlG,OAAA;QAAKiF,KAAK,EAAE;UAAEkB,IAAI,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BlG,OAAA;QAAAuF,QAAA,EAAM;MAAe;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChG,EAAA,CAzQQD,GAAG;AAAAiH,EAAA,GAAHjH,GAAG;AA2QZ,eAAeA,GAAG;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}