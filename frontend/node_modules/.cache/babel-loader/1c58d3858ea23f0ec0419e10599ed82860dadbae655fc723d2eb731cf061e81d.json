{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = ({\n  onUpload,\n  isLoading\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/').then(res => res.json()).then(data => {\n      setPresets(data.presets);\n      if (data.presets.default) {\n        setParams(data.presets.default.params);\n      }\n    }).catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n      setSelectedFile(file);\n      setError(null);\n      const reader = new FileReader();\n      reader.onload = e => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n  const SliderControl = ({\n    label,\n    value,\n    min,\n    max,\n    step,\n    onChange,\n    unit = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '6px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          color: '#ddd',\n          fontSize: '13px',\n          fontWeight: '500'\n        },\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#aaa',\n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        },\n        children: [typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value, unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"range\",\n      min: min,\n      max: max,\n      step: step,\n      value: value,\n      onChange: e => onChange(parseFloat(e.target.value)),\n      style: {\n        width: '100%',\n        height: '4px',\n        borderRadius: '2px',\n        background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${(value - min) / (max - min) * 100}%, #555 ${(value - min) / (max - min) * 100}%, #555 100%)`,\n        outline: 'none',\n        appearance: 'none',\n        cursor: 'pointer'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #555'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '8px',\n            backgroundColor: '#333',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            color: '#ddd',\n            fontSize: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '12px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: previewUrl,\n            alt: \"\\u9884\\u89C8\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '120px',\n              borderRadius: '4px',\n              border: '1px solid #555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            color: '#ff6b6b',\n            fontSize: '12px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #555'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 12px 0',\n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          },\n          children: \"\\u57FA\\u672C\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '6px',\n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            },\n            children: \"\\u9884\\u8BBE\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedPreset,\n            onChange: e => handlePresetChange(e.target.value),\n            style: {\n              width: '100%',\n              padding: '6px 8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"default\",\n              children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"portrait\",\n              children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"landscape\",\n              children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"vintage\",\n              children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"fast\",\n              children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"custom\",\n              children: \"\\u81EA\\u5B9A\\u4E49\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n          label: \"\\u8D85\\u5206\\u500D\\u6570\",\n          value: params.scale,\n          min: 2,\n          max: 4,\n          step: 2,\n          onChange: value => handleParamChange('scale', value),\n          unit: \"x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              cursor: 'pointer',\n              color: '#ddd',\n              fontSize: '13px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: params.use_realesrgan,\n              onChange: e => handleParamChange('use_realesrgan', e.target.checked),\n              style: {\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), \"\\u4F7F\\u7528RealESRGAN\\u6A21\\u578B\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u9510\\u5316\\u548C\\u964D\\u566A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u9510\\u5316\",\n            value: params.sharpening,\n            min: 0,\n            max: 1,\n            step: 0.05,\n            onChange: value => handleParamChange('sharpening', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u964D\\u566A\",\n            value: params.denoising,\n            min: 0,\n            max: 30,\n            step: 1,\n            onChange: value => handleParamChange('denoising', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u8272\\u5F69\\u8C03\\u6574\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u9971\\u548C\\u5EA6\",\n            value: params.saturation,\n            min: 0,\n            max: 2,\n            step: 0.1,\n            onChange: value => handleParamChange('saturation', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u5BF9\\u6BD4\\u5EA6\",\n            value: params.contrast,\n            min: 0,\n            max: 2,\n            step: 0.05,\n            onChange: value => handleParamChange('contrast', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u4EAE\\u5EA6\",\n            value: params.brightness,\n            min: -100,\n            max: 100,\n            step: 5,\n            onChange: value => handleParamChange('brightness', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u7F8E\\u989C\\u6548\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u7F8E\\u989C\\u5F3A\\u5EA6\",\n            value: params.beauty,\n            min: 0,\n            max: 1,\n            step: 0.05,\n            onChange: value => handleParamChange('beauty', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadForm, \"xSC9RCNijr9WJFhDDQlEUkoMNHo=\");\n_c = UploadForm;\nexport default UploadForm;\nvar _c;\n$RefreshReg$(_c, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UploadForm", "onUpload", "isLoading", "_s", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "params", "setParams", "scale", "use_realesrgan", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "fetch", "then", "res", "json", "data", "default", "catch", "err", "console", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "size", "type", "startsWith", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "SliderControl", "label", "min", "max", "step", "onChange", "unit", "style", "marginBottom", "children", "display", "justifyContent", "alignItems", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "min<PERSON><PERSON><PERSON>", "textAlign", "toFixed", "parseFloat", "width", "height", "borderRadius", "background", "outline", "appearance", "cursor", "flexDirection", "onSubmit", "flex", "padding", "borderBottom", "accept", "required", "disabled", "backgroundColor", "border", "marginTop", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "margin", "checked", "marginRight", "overflowY", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UploadForm = ({ onUpload, isLoading }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    use_realesrgan: true,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    fetch('http://localhost:8001/presets/')\n      .then(res => res.json())\n      .then(data => {\n        setPresets(data.presets);\n        if (data.presets.default) {\n          setParams(data.presets.default.params);\n        }\n      })\n      .catch(err => console.error('获取预设配置失败:', err));\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      setSelectedFile(file);\n      setError(null);\n\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    \n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  const SliderControl = ({ label, value, min, max, step, onChange, unit = '' }) => (\n    <div style={{ marginBottom: '16px' }}>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        marginBottom: '6px'\n      }}>\n        <label style={{ \n          color: '#ddd', \n          fontSize: '13px',\n          fontWeight: '500'\n        }}>\n          {label}\n        </label>\n        <span style={{ \n          color: '#aaa', \n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        }}>\n          {typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value}{unit}\n        </span>\n      </div>\n      <input \n        type=\"range\"\n        min={min}\n        max={max}\n        step={step}\n        value={value}\n        onChange={(e) => onChange(parseFloat(e.target.value))}\n        style={{\n          width: '100%',\n          height: '4px',\n          borderRadius: '2px',\n          background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${((value - min) / (max - min)) * 100}%, #555 ${((value - min) / (max - min)) * 100}%, #555 100%)`,\n          outline: 'none',\n          appearance: 'none',\n          cursor: 'pointer'\n        }}\n      />\n    </div>\n  );\n\n  return (\n    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <form onSubmit={handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n        \n        {/* 文件上传区域 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <label style={{ \n            display: 'block', \n            marginBottom: '8px', \n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          }}>\n            选择图像文件\n          </label>\n          <input\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{\n              width: '100%',\n              padding: '8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            }}\n          />\n          \n          {previewUrl && (\n            <div style={{ marginTop: '12px', textAlign: 'center' }}>\n              <img\n                src={previewUrl}\n                alt=\"预览\"\n                style={{\n                  maxWidth: '100%',\n                  maxHeight: '120px',\n                  borderRadius: '4px',\n                  border: '1px solid #555'\n                }}\n              />\n            </div>\n          )}\n          \n          {error && (\n            <div style={{ \n              marginTop: '8px',\n              color: '#ff6b6b', \n              fontSize: '12px'\n            }}>\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 基本设置 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <h3 style={{ \n            margin: '0 0 12px 0', \n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          }}>\n            基本设置\n          </h3>\n          \n          {/* 预设选择 */}\n          <div style={{ marginBottom: '16px' }}>\n            <label style={{ \n              display: 'block', \n              marginBottom: '6px', \n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            }}>\n              预设配置\n            </label>\n            <select \n              value={selectedPreset}\n              onChange={(e) => handlePresetChange(e.target.value)}\n              style={{ \n                width: '100%', \n                padding: '6px 8px', \n                backgroundColor: '#333',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                color: '#ddd',\n                fontSize: '12px'\n              }}\n            >\n              <option value=\"default\">默认设置</option>\n              <option value=\"portrait\">人像优化</option>\n              <option value=\"landscape\">风景增强</option>\n              <option value=\"vintage\">复古风格</option>\n              <option value=\"fast\">快速处理</option>\n              <option value=\"custom\">自定义</option>\n            </select>\n          </div>\n\n          {/* 超分倍数 */}\n          <SliderControl\n            label=\"超分倍数\"\n            value={params.scale}\n            min={2}\n            max={4}\n            step={2}\n            onChange={(value) => handleParamChange('scale', value)}\n            unit=\"x\"\n          />\n\n          {/* RealESRGAN开关 */}\n          <div style={{ marginBottom: '16px' }}>\n            <label style={{ \n              display: 'flex', \n              alignItems: 'center', \n              cursor: 'pointer',\n              color: '#ddd',\n              fontSize: '13px'\n            }}>\n              <input \n                type=\"checkbox\"\n                checked={params.use_realesrgan}\n                onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}\n                style={{ marginRight: '8px' }}\n              />\n              使用RealESRGAN模型\n            </label>\n          </div>\n        </div>\n\n        {/* 高级调整 */}\n        <div style={{\n          flex: 1,\n          overflowY: 'auto'\n        }}>\n          {/* 锐化和降噪 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              锐化和降噪\n            </h3>\n\n            <SliderControl\n              label=\"锐化\"\n              value={params.sharpening}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('sharpening', value)}\n            />\n\n            <SliderControl\n              label=\"降噪\"\n              value={params.denoising}\n              min={0}\n              max={30}\n              step={1}\n              onChange={(value) => handleParamChange('denoising', value)}\n            />\n          </div>\n\n          {/* 色彩调整 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              色彩调整\n            </h3>\n\n            <SliderControl\n              label=\"饱和度\"\n              value={params.saturation}\n              min={0}\n              max={2}\n              step={0.1}\n              onChange={(value) => handleParamChange('saturation', value)}\n            />\n\n            <SliderControl\n              label=\"对比度\"\n              value={params.contrast}\n              min={0}\n              max={2}\n              step={0.05}\n              onChange={(value) => handleParamChange('contrast', value)}\n            />\n\n            <SliderControl\n              label=\"亮度\"\n              value={params.brightness}\n              min={-100}\n              max={100}\n              step={5}\n              onChange={(value) => handleParamChange('brightness', value)}\n            />\n          </div>\n\n          {/* 美颜效果 */}\n          <div style={{\n            padding: '16px'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              美颜效果\n            </h3>\n\n            <SliderControl\n              label=\"美颜强度\"\n              value={params.beauty}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('beauty', value)}\n            />\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default UploadForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC;IACnCoB,KAAK,EAAE,CAAC;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA1B,SAAS,CAAC,MAAM;IACd2B,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZjB,UAAU,CAACiB,IAAI,CAAClB,OAAO,CAAC;MACxB,IAAIkB,IAAI,CAAClB,OAAO,CAACmB,OAAO,EAAE;QACxBd,SAAS,CAACa,IAAI,CAAClB,OAAO,CAACmB,OAAO,CAACf,MAAM,CAAC;MACxC;IACF,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACxB,KAAK,CAAC,WAAW,EAAEuB,GAAG,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,kBAAkB,GAAIC,SAAS,IAAK;IACxCrB,iBAAiB,CAACqB,SAAS,CAAC;IAC5B,IAAIxB,OAAO,IAAIA,OAAO,CAACwB,SAAS,CAAC,EAAE;MACjCnB,SAAS,CAACL,OAAO,CAACwB,SAAS,CAAC,CAACpB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxCtB,SAAS,CAACuB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACHxB,iBAAiB,CAAC,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAM0B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,IAAIA,IAAI,CAACG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCnC,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;MAEA,IAAI,CAACgC,IAAI,CAACI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCrC,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;MAEAJ,eAAe,CAACoC,IAAI,CAAC;MACrBhC,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMsC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIT,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLpC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACjD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAM6C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpD,YAAY,CAAC;IACrCkD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC5C,MAAM,CAAC,CAAC;IACjDb,QAAQ,CAACqD,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAC;IAAEC,KAAK;IAAEvB,KAAK;IAAEwB,GAAG;IAAEC,GAAG;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,IAAI,GAAG;EAAG,CAAC,kBAC1ElE,OAAA;IAAKmE,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACnCrE,OAAA;MAAKmE,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBJ,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,gBACArE,OAAA;QAAOmE,KAAK,EAAE;UACZM,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAN,QAAA,EACCR;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACR/E,OAAA;QAAMmE,KAAK,EAAE;UACXM,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBM,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAE;QAAAZ,QAAA,GACC,OAAO/B,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC4C,OAAO,CAAClB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG1B,KAAK,EAAE4B,IAAI;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACN/E,OAAA;MACE8C,IAAI,EAAC,OAAO;MACZgB,GAAG,EAAEA,GAAI;MACTC,GAAG,EAAEA,GAAI;MACTC,IAAI,EAAEA,IAAK;MACX1B,KAAK,EAAEA,KAAM;MACb2B,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAACkB,UAAU,CAAC1C,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;MACtD6B,KAAK,EAAE;QACLiB,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,KAAK;QACbC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,iDAAkD,CAACjD,KAAK,GAAGwB,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,WAAY,CAACxB,KAAK,GAAGwB,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,eAAe;QAC7J0B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE;MACV;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACE/E,OAAA;IAAKmE,KAAK,EAAE;MAAEkB,MAAM,EAAE,MAAM;MAAEf,OAAO,EAAE,MAAM;MAAEqB,aAAa,EAAE;IAAS,CAAE;IAAAtB,QAAA,eACvErE,OAAA;MAAM4F,QAAQ,EAAEvC,YAAa;MAACc,KAAK,EAAE;QAAE0B,IAAI,EAAE,CAAC;QAAEvB,OAAO,EAAE,MAAM;QAAEqB,aAAa,EAAE;MAAS,CAAE;MAAAtB,QAAA,gBAGzFrE,OAAA;QAAKmE,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA1B,QAAA,gBACArE,OAAA;UAAOmE,KAAK,EAAE;YACZG,OAAO,EAAE,OAAO;YAChBF,YAAY,EAAE,KAAK;YACnBK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/E,OAAA;UACE8C,IAAI,EAAC,MAAM;UACXmB,QAAQ,EAAEzB,gBAAiB;UAC3BwD,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAE/F,SAAU;UACpBgE,KAAK,EAAE;YACLiB,KAAK,EAAE,MAAM;YACbU,OAAO,EAAE,KAAK;YACdK,eAAe,EAAE,MAAM;YACvBC,MAAM,EAAE,gBAAgB;YACxBd,YAAY,EAAE,KAAK;YACnBb,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE;UACZ;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDxE,UAAU,iBACTP,OAAA;UAAKmE,KAAK,EAAE;YAAEkC,SAAS,EAAE,MAAM;YAAEpB,SAAS,EAAE;UAAS,CAAE;UAAAZ,QAAA,eACrDrE,OAAA;YACEsG,GAAG,EAAE/F,UAAW;YAChBgG,GAAG,EAAC,cAAI;YACRpC,KAAK,EAAE;cACLqC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClBnB,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE;YACV;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAtE,KAAK,iBACJT,OAAA;UAAKmE,KAAK,EAAE;YACVkC,SAAS,EAAE,KAAK;YAChB5B,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,EACC5D;QAAK;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/E,OAAA;QAAKmE,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA1B,QAAA,gBACArE,OAAA;UAAImE,KAAK,EAAE;YACTuC,MAAM,EAAE,YAAY;YACpBjC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGL/E,OAAA;UAAKmE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACnCrE,OAAA;YAAOmE,KAAK,EAAE;cACZG,OAAO,EAAE,OAAO;cAChBF,YAAY,EAAE,KAAK;cACnBK,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEsC,KAAK,EAAEzB,cAAe;YACtBoD,QAAQ,EAAGxB,CAAC,IAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;YACpD6B,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACbU,OAAO,EAAE,SAAS;cAClBK,eAAe,EAAE,MAAM;cACvBC,MAAM,EAAE,gBAAgB;cACxBd,YAAY,EAAE,KAAK;cACnBb,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,gBAEFrE,OAAA;cAAQsC,KAAK,EAAC,SAAS;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC/E,OAAA;cAAQsC,KAAK,EAAC,UAAU;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC/E,OAAA;cAAQsC,KAAK,EAAC,WAAW;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC/E,OAAA;cAAQsC,KAAK,EAAC,SAAS;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC/E,OAAA;cAAQsC,KAAK,EAAC,MAAM;cAAA+B,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC/E,OAAA;cAAQsC,KAAK,EAAC,QAAQ;cAAA+B,QAAA,EAAC;YAAG;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN/E,OAAA,CAAC4D,aAAa;UACZC,KAAK,EAAC,0BAAM;UACZvB,KAAK,EAAEvB,MAAM,CAACE,KAAM;UACpB6C,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,CAAE;UACRC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,OAAO,EAAEE,KAAK,CAAE;UACvD4B,IAAI,EAAC;QAAG;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGF/E,OAAA;UAAKmE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,eACnCrE,OAAA;YAAOmE,KAAK,EAAE;cACZG,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBkB,MAAM,EAAE,SAAS;cACjBjB,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,gBACArE,OAAA;cACE8C,IAAI,EAAC,UAAU;cACf6D,OAAO,EAAE5F,MAAM,CAACG,cAAe;cAC/B+C,QAAQ,EAAGxB,CAAC,IAAKL,iBAAiB,CAAC,gBAAgB,EAAEK,CAAC,CAACE,MAAM,CAACgE,OAAO,CAAE;cACvExC,KAAK,EAAE;gBAAEyC,WAAW,EAAE;cAAM;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,sCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA;QAAKmE,KAAK,EAAE;UACV0B,IAAI,EAAE,CAAC;UACPgB,SAAS,EAAE;QACb,CAAE;QAAAxC,QAAA,gBAEArE,OAAA;UAAKmE,KAAK,EAAE;YACV2B,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,gBACArE,OAAA;YAAImE,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL/E,OAAA,CAAC4D,aAAa;YACZC,KAAK,EAAC,cAAI;YACVvB,KAAK,EAAEvB,MAAM,CAACI,UAAW;YACzB2C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAEF/E,OAAA,CAAC4D,aAAa;YACZC,KAAK,EAAC,cAAI;YACVvB,KAAK,EAAEvB,MAAM,CAACK,SAAU;YACxB0C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRC,IAAI,EAAE,CAAE;YACRC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,WAAW,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/E,OAAA;UAAKmE,KAAK,EAAE;YACV2B,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,gBACArE,OAAA;YAAImE,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL/E,OAAA,CAAC4D,aAAa;YACZC,KAAK,EAAC,oBAAK;YACXvB,KAAK,EAAEvB,MAAM,CAACM,UAAW;YACzByC,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,GAAI;YACVC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAEF/E,OAAA,CAAC4D,aAAa;YACZC,KAAK,EAAC,oBAAK;YACXvB,KAAK,EAAEvB,MAAM,CAACO,QAAS;YACvBwC,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,UAAU,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEF/E,OAAA,CAAC4D,aAAa;YACZC,KAAK,EAAC,cAAI;YACVvB,KAAK,EAAEvB,MAAM,CAACQ,UAAW;YACzBuC,GAAG,EAAE,CAAC,GAAI;YACVC,GAAG,EAAE,GAAI;YACTC,IAAI,EAAE,CAAE;YACRC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/E,OAAA;UAAKmE,KAAK,EAAE;YACV2B,OAAO,EAAE;UACX,CAAE;UAAAzB,QAAA,gBACArE,OAAA;YAAImE,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL/E,OAAA,CAAC4D,aAAa;YACZC,KAAK,EAAC,0BAAM;YACZvB,KAAK,EAAEvB,MAAM,CAACS,MAAO;YACrBsC,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG3B,KAAK,IAAKF,iBAAiB,CAAC,QAAQ,EAAEE,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAvXIH,UAAU;AAAA6G,EAAA,GAAV7G,UAAU;AAyXhB,eAAeA,UAAU;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}