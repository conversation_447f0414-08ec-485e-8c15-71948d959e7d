{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const handleUpload = async formData => {\n    setIsLoading(true);\n    setError(null);\n\n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = e => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [{\n      label: '打开图像 (Ctrl+O)',\n      action: () => {\n        var _fileInputRef$current;\n        return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n      }\n    }, {\n      label: '保存结果 (Ctrl+S)',\n      action: () => downloadImage(),\n      disabled: !result\n    }, {\n      label: '重置 (Ctrl+R)',\n      action: () => handleReset()\n    }],\n    edit: [{\n      label: '撤销',\n      action: () => {},\n      disabled: true\n    }, {\n      label: '重做',\n      action: () => {},\n      disabled: true\n    }],\n    view: [{\n      label: '分屏对比',\n      action: () => setViewMode('split'),\n      checked: viewMode === 'split'\n    }, {\n      label: '原图',\n      action: () => setViewMode('original'),\n      checked: viewMode === 'original'\n    }, {\n      label: '增强图',\n      action: () => setViewMode('enhanced'),\n      checked: viewMode === 'enhanced'\n    }, {\n      label: '---'\n    }, {\n      label: '放大 (Ctrl+=)',\n      action: () => setZoomLevel(prev => Math.min(prev + 25, 400))\n    }, {\n      label: '缩小 (Ctrl+-)',\n      action: () => setZoomLevel(prev => Math.max(prev - 25, 25))\n    }, {\n      label: '实际大小 (Ctrl+0)',\n      action: () => setZoomLevel(100)\n    }],\n    tools: [{\n      label: '顺时针旋转',\n      action: () => setRotation(prev => (prev + 90) % 360)\n    }, {\n      label: '逆时针旋转',\n      action: () => setRotation(prev => (prev - 90 + 360) % 360)\n    }, {\n      label: '重置旋转',\n      action: () => setRotation(0)\n    }, {\n      label: '---'\n    }, {\n      label: '批量处理',\n      action: () => {},\n      disabled: true\n    }],\n    help: [{\n      label: '快捷键',\n      action: () => showShortcuts()\n    }, {\n      label: '关于',\n      action: () => showAbout()\n    }]\n  };\n  const handleMenuClick = menuKey => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n  const showShortcuts = () => {\n    alert(`快捷键说明:\n\n文件操作:\n• Ctrl+O: 打开图像\n• Ctrl+S: 保存结果\n• Ctrl+R: 重置\n\n视图控制:\n• Ctrl+=: 放大\n• Ctrl+-: 缩小\n• Ctrl+0: 实际大小`);\n  };\n  const showAbout = () => {\n    alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          fontSize: '13px'\n        },\n        children: ['file', 'edit', 'view', 'tools', 'help'].map(menuKey => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              cursor: 'pointer',\n              padding: '5px 10px',\n              borderRadius: '4px',\n              backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n            },\n            onClick: () => setShowMenu(showMenu === menuKey ? null : menuKey),\n            children: menuKey === 'file' ? '文件' : menuKey === 'edit' ? '编辑' : menuKey === 'view' ? '视图' : menuKey === 'tools' ? '工具' : '帮助'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), showMenu === menuKey && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '100%',\n              left: 0,\n              backgroundColor: '#3c3c3c',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              minWidth: '180px',\n              zIndex: 1000,\n              boxShadow: '0 4px 8px rgba(0,0,0,0.3)',\n              padding: '8px 0'\n            },\n            children: [menuKey === 'file' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  var _fileInputRef$current2;\n                  (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDCC1 \\u6253\\u5F00\\u56FE\\u50CF (Ctrl+O)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: result ? '#e0e0e0' : '#666',\n                  fontSize: '12px',\n                  cursor: result ? 'pointer' : 'not-allowed'\n                },\n                onClick: () => {\n                  if (result) {\n                    downloadImage();\n                    setShowMenu(null);\n                  }\n                },\n                children: \"\\uD83D\\uDCBE \\u4FDD\\u5B58\\u7ED3\\u679C (Ctrl+S)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '1px',\n                  backgroundColor: '#555',\n                  margin: '4px 0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  handleReset();\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E (Ctrl+R)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'view' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  backgroundColor: viewMode === 'split' ? '#4a90e2' : 'transparent'\n                },\n                onClick: () => {\n                  setViewMode('split');\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD00 \\u5206\\u5C4F\\u5BF9\\u6BD4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  backgroundColor: viewMode === 'original' ? '#4a90e2' : 'transparent'\n                },\n                onClick: () => {\n                  setViewMode('original');\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDDBC\\uFE0F \\u539F\\u56FE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : 'transparent'\n                },\n                onClick: () => {\n                  setViewMode('enhanced');\n                  setShowMenu(null);\n                },\n                children: \"\\u2728 \\u589E\\u5F3A\\u56FE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '1px',\n                  backgroundColor: '#555',\n                  margin: '4px 0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setZoomLevel(prev => Math.min(prev + 25, 400));\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD0D \\u653E\\u5927 (Ctrl+=)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setZoomLevel(prev => Math.max(prev - 25, 25));\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD0D \\u7F29\\u5C0F (Ctrl+-)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setZoomLevel(100);\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDCD0 \\u5B9E\\u9645\\u5927\\u5C0F (Ctrl+0)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'tools' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setRotation(prev => (prev + 90) % 360);\n                  setShowMenu(null);\n                },\n                children: \"\\u21BB \\u987A\\u65F6\\u9488\\u65CB\\u8F6C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setRotation(prev => (prev - 90 + 360) % 360);\n                  setShowMenu(null);\n                },\n                children: \"\\u21BA \\u9006\\u65F6\\u9488\\u65CB\\u8F6C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setRotation(0);\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E\\u65CB\\u8F6C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '1px',\n                  backgroundColor: '#555',\n                  margin: '4px 0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#666',\n                  fontSize: '12px'\n                },\n                children: \"\\uD83D\\uDCE6 \\u6279\\u91CF\\u5904\\u7406 (\\u5F00\\u53D1\\u4E2D)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'help' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  alert(`快捷键说明:\\n\\n文件操作:\\n• Ctrl+O: 打开图像\\n• Ctrl+S: 保存结果\\n• Ctrl+R: 重置\\n\\n视图控制:\\n• Ctrl+=: 放大\\n• Ctrl+-: 缩小\\n• Ctrl+0: 实际大小`);\n                  setShowMenu(null);\n                },\n                children: \"\\u2328\\uFE0F \\u5FEB\\u6377\\u952E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n                  setShowMenu(null);\n                },\n                children: \"\\u2139\\uFE0F \\u5173\\u4E8E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'edit' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#666',\n                  fontSize: '12px'\n                },\n                children: \"\\u21B6 \\u64A4\\u9500 (\\u5F00\\u53D1\\u4E2D)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#666',\n                  fontSize: '12px'\n                },\n                children: \"\\u21B7 \\u91CD\\u505A (\\u5F00\\u53D1\\u4E2D)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this)]\n        }, menuKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ccc',\n              fontSize: '12px'\n            },\n            children: result ? '已处理' : '等待上传图像'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [\"\\u9519\\u8BEF: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '8px',\n              textAlign: 'center',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '30px',\n                height: '30px',\n                border: '3px solid #333',\n                borderTop: '3px solid #4a90e2',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite',\n                margin: '0 auto'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this), result ? /*#__PURE__*/_jsxDEV(ResultView, {\n            result: result,\n            originalImage: originalImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u5C31\\u7EEA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"RealESRGAN v2.1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"1TlV2uPXZUja6R8rp8rFORARBYU=\");\n_c = App;\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "handleUpload", "formData", "file", "get", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "useEffect", "handleKeyDown", "ctrl<PERSON>ey", "key", "preventDefault", "fileInputRef", "current", "click", "handleReset", "downloadImage", "setZoomLevel", "prev", "Math", "min", "max", "document", "addEventListener", "removeEventListener", "menuItems", "label", "action", "_fileInputRef$current", "disabled", "edit", "view", "setViewMode", "checked", "viewMode", "tools", "setRotation", "help", "showShortcuts", "showAbout", "handleMenuClick", "<PERSON><PERSON>ey", "setShowMenu", "showMenu", "enhanced_image", "link", "createElement", "href", "download", "Date", "now", "alert", "setImageInfo", "setProcessingTime", "style", "height", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "borderBottom", "alignItems", "padding", "color", "gap", "marginRight", "width", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "textAlign", "fontSize", "fontWeight", "map", "position", "cursor", "onClick", "top", "left", "border", "min<PERSON><PERSON><PERSON>", "zIndex", "boxShadow", "_fileInputRef$current2", "margin", "justifyContent", "right", "transform", "marginBottom", "borderTop", "animation", "borderLeft", "onUpload", "jsx", "_c", "globalStyles", "styleElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n\n  const handleUpload = async (formData) => {\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      // Ctrl+O 打开文件\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      // Ctrl+R 重置\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      // Ctrl+S 保存（如果有结果）\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      // Ctrl+= 放大\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      // Ctrl+- 缩小\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      // Ctrl+0 重置缩放\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  // 菜单功能\n  const menuItems = {\n    file: [\n      { label: '打开图像 (Ctrl+O)', action: () => fileInputRef.current?.click() },\n      { label: '保存结果 (Ctrl+S)', action: () => downloadImage(), disabled: !result },\n      { label: '重置 (Ctrl+R)', action: () => handleReset() }\n    ],\n    edit: [\n      { label: '撤销', action: () => {}, disabled: true },\n      { label: '重做', action: () => {}, disabled: true }\n    ],\n    view: [\n      { label: '分屏对比', action: () => setViewMode('split'), checked: viewMode === 'split' },\n      { label: '原图', action: () => setViewMode('original'), checked: viewMode === 'original' },\n      { label: '增强图', action: () => setViewMode('enhanced'), checked: viewMode === 'enhanced' },\n      { label: '---' },\n      { label: '放大 (Ctrl+=)', action: () => setZoomLevel(prev => Math.min(prev + 25, 400)) },\n      { label: '缩小 (Ctrl+-)', action: () => setZoomLevel(prev => Math.max(prev - 25, 25)) },\n      { label: '实际大小 (Ctrl+0)', action: () => setZoomLevel(100) }\n    ],\n    tools: [\n      { label: '顺时针旋转', action: () => setRotation(prev => (prev + 90) % 360) },\n      { label: '逆时针旋转', action: () => setRotation(prev => (prev - 90 + 360) % 360) },\n      { label: '重置旋转', action: () => setRotation(0) },\n      { label: '---' },\n      { label: '批量处理', action: () => {}, disabled: true }\n    ],\n    help: [\n      { label: '快捷键', action: () => showShortcuts() },\n      { label: '关于', action: () => showAbout() }\n    ]\n  };\n\n  const handleMenuClick = (menuKey) => {\n    setShowMenu(showMenu === menuKey ? null : menuKey);\n  };\n\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n\n  const showShortcuts = () => {\n    alert(`快捷键说明:\n\n文件操作:\n• Ctrl+O: 打开图像\n• Ctrl+S: 保存结果\n• Ctrl+R: 重置\n\n视图控制:\n• Ctrl+=: 放大\n• Ctrl+-: 缩小\n• Ctrl+0: 实际大小`);\n  };\n\n  const showAbout = () => {\n    alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          {['file', 'edit', 'view', 'tools', 'help'].map(menuKey => (\n            <div key={menuKey} style={{ position: 'relative' }}>\n              <span\n                style={{\n                  cursor: 'pointer',\n                  padding: '5px 10px',\n                  borderRadius: '4px',\n                  backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n                }}\n                onClick={() => setShowMenu(showMenu === menuKey ? null : menuKey)}\n              >\n                {menuKey === 'file' ? '文件' :\n                 menuKey === 'edit' ? '编辑' :\n                 menuKey === 'view' ? '视图' :\n                 menuKey === 'tools' ? '工具' : '帮助'}\n              </span>\n\n              {/* 下拉菜单 */}\n              {showMenu === menuKey && (\n                <div style={{\n                  position: 'absolute',\n                  top: '100%',\n                  left: 0,\n                  backgroundColor: '#3c3c3c',\n                  border: '1px solid #555',\n                  borderRadius: '4px',\n                  minWidth: '180px',\n                  zIndex: 1000,\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.3)',\n                  padding: '8px 0'\n                }}>\n                  {menuKey === 'file' && (\n                    <>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { fileInputRef.current?.click(); setShowMenu(null); }}\n                      >\n                        📁 打开图像 (Ctrl+O)\n                      </div>\n                      <div\n                        style={{\n                          padding: '8px 12px',\n                          color: result ? '#e0e0e0' : '#666',\n                          fontSize: '12px',\n                          cursor: result ? 'pointer' : 'not-allowed'\n                        }}\n                        onClick={() => { if(result) { downloadImage(); setShowMenu(null); } }}\n                      >\n                        💾 保存结果 (Ctrl+S)\n                      </div>\n                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { handleReset(); setShowMenu(null); }}\n                      >\n                        🔄 重置 (Ctrl+R)\n                      </div>\n                    </>\n                  )}\n\n                  {menuKey === 'view' && (\n                    <>\n                      <div\n                        style={{\n                          padding: '8px 12px',\n                          color: '#e0e0e0',\n                          fontSize: '12px',\n                          cursor: 'pointer',\n                          backgroundColor: viewMode === 'split' ? '#4a90e2' : 'transparent'\n                        }}\n                        onClick={() => { setViewMode('split'); setShowMenu(null); }}\n                      >\n                        🔀 分屏对比\n                      </div>\n                      <div\n                        style={{\n                          padding: '8px 12px',\n                          color: '#e0e0e0',\n                          fontSize: '12px',\n                          cursor: 'pointer',\n                          backgroundColor: viewMode === 'original' ? '#4a90e2' : 'transparent'\n                        }}\n                        onClick={() => { setViewMode('original'); setShowMenu(null); }}\n                      >\n                        🖼️ 原图\n                      </div>\n                      <div\n                        style={{\n                          padding: '8px 12px',\n                          color: '#e0e0e0',\n                          fontSize: '12px',\n                          cursor: 'pointer',\n                          backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : 'transparent'\n                        }}\n                        onClick={() => { setViewMode('enhanced'); setShowMenu(null); }}\n                      >\n                        ✨ 增强图\n                      </div>\n                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setZoomLevel(prev => Math.min(prev + 25, 400)); setShowMenu(null); }}\n                      >\n                        🔍 放大 (Ctrl+=)\n                      </div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setZoomLevel(prev => Math.max(prev - 25, 25)); setShowMenu(null); }}\n                      >\n                        🔍 缩小 (Ctrl+-)\n                      </div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setZoomLevel(100); setShowMenu(null); }}\n                      >\n                        📐 实际大小 (Ctrl+0)\n                      </div>\n                    </>\n                  )}\n\n                  {menuKey === 'tools' && (\n                    <>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setRotation(prev => (prev + 90) % 360); setShowMenu(null); }}\n                      >\n                        ↻ 顺时针旋转\n                      </div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setRotation(prev => (prev - 90 + 360) % 360); setShowMenu(null); }}\n                      >\n                        ↺ 逆时针旋转\n                      </div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setRotation(0); setShowMenu(null); }}\n                      >\n                        🔄 重置旋转\n                      </div>\n                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>\n                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>\n                        📦 批量处理 (开发中)\n                      </div>\n                    </>\n                  )}\n\n                  {menuKey === 'help' && (\n                    <>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => {\n                          alert(`快捷键说明:\\n\\n文件操作:\\n• Ctrl+O: 打开图像\\n• Ctrl+S: 保存结果\\n• Ctrl+R: 重置\\n\\n视图控制:\\n• Ctrl+=: 放大\\n• Ctrl+-: 缩小\\n• Ctrl+0: 实际大小`);\n                          setShowMenu(null);\n                        }}\n                      >\n                        ⌨️ 快捷键\n                      </div>\n                      <div\n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => {\n                          alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n                          setShowMenu(null);\n                        }}\n                      >\n                        ℹ️ 关于\n                      </div>\n                    </>\n                  )}\n\n                  {menuKey === 'edit' && (\n                    <>\n                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>\n                        ↶ 撤销 (开发中)\n                      </div>\n                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>\n                        ↷ 重做 (开发中)\n                      </div>\n                    </>\n                  )}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              新建\n            </button>\n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {result ? '已处理' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <ResultView result={result} originalImage={originalImage} />\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        <span>就绪</span>\n        <div style={{ flex: 1 }}></div>\n        <span>RealESRGAN v2.1</span>\n      </div>\n\n      {/* CSS动画 */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n\n// 添加全局CSS样式\nconst globalStyles = `\n  /* 自定义滑块样式 */\n  input[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: 4px;\n    background: #555;\n    outline: none;\n    border-radius: 2px;\n  }\n\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 14px;\n    height: 14px;\n    background: #4a90e2;\n    cursor: pointer;\n    border-radius: 50%;\n    border: 2px solid #fff;\n    border: none;\n  }\n\n  /* 自定义复选框样式 */\n  input[type=\"checkbox\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 16px;\n    height: 16px;\n    background: #3c3c3c;\n    border: 1px solid #555;\n    border-radius: 3px;\n    cursor: pointer;\n    position: relative;\n  }\n\n  input[type=\"checkbox\"]:checked {\n    background: #4a90e2;\n    border-color: #4a90e2;\n  }\n\n  input[type=\"checkbox\"]:checked::after {\n    content: '✓';\n    position: absolute;\n    top: -2px;\n    left: 2px;\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: #2d2d2d;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: #555;\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: #666;\n  }\n`;\n\n// 注入样式\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = globalStyles;\n  document.head.appendChild(styleElement);\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMiB,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvCN,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMK,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKR,gBAAgB,CAACQ,CAAC,CAACC,MAAM,CAAChB,MAAM,CAAC;MACxDY,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCvB,SAAS,CAAC0B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,OAAO,EAAEwB,GAAG,CAAC;MAC3BvB,QAAQ,CAACuB,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA4B,SAAS,CAAC,MAAM;IACd,MAAMC,aAAa,GAAIjB,CAAC,IAAK;MAC3B;MACA,IAAIA,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClB,IAAIC,YAAY,CAACC,OAAO,EAAE;UACxBD,YAAY,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9B;MACF;MACA;MACA,IAAIvB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBI,WAAW,CAAC,CAAC;MACf;MACA;MACA,IAAIxB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,IAAIlC,MAAM,EAAE;QACxCe,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBK,aAAa,CAAC,CAAC;MACjB;MACA;MACA,IAAIzB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBM,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;MAChD;MACA;MACA,IAAI3B,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBM,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;MAC/C;MACA;MACA,IAAI3B,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,GAAG,KAAK,GAAG,EAAE;QAC9BnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClBM,YAAY,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;IAEDK,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEf,aAAa,CAAC;IACnD,OAAO,MAAMc,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEhB,aAAa,CAAC;EACrE,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMiD,SAAS,GAAG;IAChBvC,IAAI,EAAE,CACJ;MAAEwC,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA;QAAA,IAAAC,qBAAA;QAAA,QAAAA,qBAAA,GAAMhB,YAAY,CAACC,OAAO,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,KAAK,CAAC,CAAC;MAAA;IAAC,CAAC,EACvE;MAAEY,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA,KAAMX,aAAa,CAAC,CAAC;MAAEa,QAAQ,EAAE,CAACrD;IAAO,CAAC,EAC5E;MAAEkD,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMZ,WAAW,CAAC;IAAE,CAAC,CACtD;IACDe,IAAI,EAAE,CACJ;MAAEJ,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,EACjD;MAAEH,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,CAClD;IACDE,IAAI,EAAE,CACJ;MAAEL,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAMK,WAAW,CAAC,OAAO,CAAC;MAAEC,OAAO,EAAEC,QAAQ,KAAK;IAAQ,CAAC,EACpF;MAAER,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAMK,WAAW,CAAC,UAAU,CAAC;MAAEC,OAAO,EAAEC,QAAQ,KAAK;IAAW,CAAC,EACxF;MAAER,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEA,CAAA,KAAMK,WAAW,CAAC,UAAU,CAAC;MAAEC,OAAO,EAAEC,QAAQ,KAAK;IAAW,CAAC,EACzF;MAAER,KAAK,EAAE;IAAM,CAAC,EAChB;MAAEA,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMV,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC;IAAE,CAAC,EACtF;MAAEQ,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAEA,CAAA,KAAMV,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC;IAAE,CAAC,EACrF;MAAEQ,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA,KAAMV,YAAY,CAAC,GAAG;IAAE,CAAC,CAC5D;IACDkB,KAAK,EAAE,CACL;MAAET,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAEA,CAAA,KAAMS,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,IAAI,GAAG;IAAE,CAAC,EACxE;MAAEQ,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAEA,CAAA,KAAMS,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG;IAAE,CAAC,EAC9E;MAAEQ,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAMS,WAAW,CAAC,CAAC;IAAE,CAAC,EAC/C;MAAEV,KAAK,EAAE;IAAM,CAAC,EAChB;MAAEA,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAC;MAAEE,QAAQ,EAAE;IAAK,CAAC,CACpD;IACDQ,IAAI,EAAE,CACJ;MAAEX,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAEA,CAAA,KAAMW,aAAa,CAAC;IAAE,CAAC,EAC/C;MAAEZ,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAEA,CAAA,KAAMY,SAAS,CAAC;IAAE,CAAC;EAE9C,CAAC;EAED,MAAMC,eAAe,GAAIC,OAAO,IAAK;IACnCC,WAAW,CAACC,QAAQ,KAAKF,OAAO,GAAG,IAAI,GAAGA,OAAO,CAAC;EACpD,CAAC;EAED,MAAMzB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIxC,MAAM,IAAIA,MAAM,CAACoE,cAAc,EAAE;MACnC,MAAMC,IAAI,GAAGvB,QAAQ,CAACwB,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,yBAAyBvE,MAAM,CAACoE,cAAc,EAAE;MAC5DC,IAAI,CAACG,QAAQ,GAAG,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC5CL,IAAI,CAAC/B,KAAK,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1Ba,KAAK,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,CAAC;EACd,CAAC;EAED,MAAMZ,SAAS,GAAGA,CAAA,KAAM;IACtBY,KAAK,CAAC,2CAA2C,CAAC;EACpD,CAAC;EAED,MAAMpC,WAAW,GAAGA,CAAA,KAAM;IACxBtC,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;IACtBqE,YAAY,CAAC,IAAI,CAAC;IAClBC,iBAAiB,CAAC,IAAI,CAAC;IACvBpC,YAAY,CAAC,GAAG,CAAC;IACjBe,WAAW,CAAC,OAAO,CAAC;IACpBI,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACEjE,OAAA;IAAKmF,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEA1F,OAAA;MAAKmF,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BM,YAAY,EAAE,gBAAgB;QAC9BJ,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAJ,QAAA,gBAEA1F,OAAA;QAAKmF,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC/D1F,OAAA;UAAKmF,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGtG,OAAA;UAAKmF,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGtG,OAAA;UAAKmF,KAAK,EAAE;YAAEc,KAAK,EAAE,MAAM;YAAEb,MAAM,EAAE,MAAM;YAAEc,YAAY,EAAE,KAAK;YAAEb,eAAe,EAAE;UAAU;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNtG,OAAA;QAAKmF,KAAK,EAAE;UACVoB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBZ,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNtG,OAAA;QAAKmF,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAf,QAAA,EAC5D,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAACiB,GAAG,CAACrC,OAAO,iBACpDtE,OAAA;UAAmBmF,KAAK,EAAE;YAAEyB,QAAQ,EAAE;UAAW,CAAE;UAAAlB,QAAA,gBACjD1F,OAAA;YACEmF,KAAK,EAAE;cACL0B,MAAM,EAAE,SAAS;cACjBhB,OAAO,EAAE,UAAU;cACnBK,YAAY,EAAE,KAAK;cACnBb,eAAe,EAAEb,QAAQ,KAAKF,OAAO,GAAG,SAAS,GAAG;YACtD,CAAE;YACFwC,OAAO,EAAEA,CAAA,KAAMvC,WAAW,CAACC,QAAQ,KAAKF,OAAO,GAAG,IAAI,GAAGA,OAAO,CAAE;YAAAoB,QAAA,EAEjEpB,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,OAAO,GAAG,IAAI,GAAG;UAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EAGN9B,QAAQ,KAAKF,OAAO,iBACnBtE,OAAA;YAAKmF,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACP3B,eAAe,EAAE,SAAS;cAC1B4B,MAAM,EAAE,gBAAgB;cACxBf,YAAY,EAAE,KAAK;cACnBgB,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE,IAAI;cACZC,SAAS,EAAE,2BAA2B;cACtCvB,OAAO,EAAE;YACX,CAAE;YAAAH,QAAA,GACCpB,OAAO,KAAK,MAAM,iBACjBtE,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAA,IAAAO,sBAAA;kBAAE,CAAAA,sBAAA,GAAA5E,YAAY,CAACC,OAAO,cAAA2E,sBAAA,uBAApBA,sBAAA,CAAsB1E,KAAK,CAAC,CAAC;kBAAE4B,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EACtE;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBACLU,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAEzF,MAAM,GAAG,SAAS,GAAG,MAAM;kBAClCoG,QAAQ,EAAE,MAAM;kBAChBI,MAAM,EAAExG,MAAM,GAAG,SAAS,GAAG;gBAC/B,CAAE;gBACFyG,OAAO,EAAEA,CAAA,KAAM;kBAAE,IAAGzG,MAAM,EAAE;oBAAEwC,aAAa,CAAC,CAAC;oBAAE0B,WAAW,CAAC,IAAI,CAAC;kBAAE;gBAAE,CAAE;gBAAAmB,QAAA,EACvE;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEC,MAAM,EAAE,KAAK;kBAAEC,eAAe,EAAE,MAAM;kBAAEiC,MAAM,EAAE;gBAAQ;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/EtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAElE,WAAW,CAAC,CAAC;kBAAE2B,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EACtD;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAhC,OAAO,KAAK,MAAM,iBACjBtE,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBACEmF,KAAK,EAAE;kBACLU,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAE,SAAS;kBAChBW,QAAQ,EAAE,MAAM;kBAChBI,MAAM,EAAE,SAAS;kBACjBxB,eAAe,EAAEtB,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG;gBACtD,CAAE;gBACF+C,OAAO,EAAEA,CAAA,KAAM;kBAAEjD,WAAW,CAAC,OAAO,CAAC;kBAAEU,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EAC7D;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBACLU,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAE,SAAS;kBAChBW,QAAQ,EAAE,MAAM;kBAChBI,MAAM,EAAE,SAAS;kBACjBxB,eAAe,EAAEtB,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG;gBACzD,CAAE;gBACF+C,OAAO,EAAEA,CAAA,KAAM;kBAAEjD,WAAW,CAAC,UAAU,CAAC;kBAAEU,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EAChE;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBACLU,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAE,SAAS;kBAChBW,QAAQ,EAAE,MAAM;kBAChBI,MAAM,EAAE,SAAS;kBACjBxB,eAAe,EAAEtB,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG;gBACzD,CAAE;gBACF+C,OAAO,EAAEA,CAAA,KAAM;kBAAEjD,WAAW,CAAC,UAAU,CAAC;kBAAEU,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EAChE;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEC,MAAM,EAAE,KAAK;kBAAEC,eAAe,EAAE,MAAM;kBAAEiC,MAAM,EAAE;gBAAQ;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/EtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAEhE,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;kBAAEwB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EACvF;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAEhE,YAAY,CAACC,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;kBAAEwB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EACtF;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAEhE,YAAY,CAAC,GAAG,CAAC;kBAAEyB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EAC1D;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAhC,OAAO,KAAK,OAAO,iBAClBtE,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE7C,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,IAAI,GAAG,CAAC;kBAAEwB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EAC/E;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE7C,WAAW,CAAClB,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC;kBAAEwB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EACrF;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE7C,WAAW,CAAC,CAAC,CAAC;kBAAEM,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAAmB,QAAA,EACvD;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEC,MAAM,EAAE,KAAK;kBAAEC,eAAe,EAAE,MAAM;kBAAEiC,MAAM,EAAE;gBAAQ;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/EtG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,MAAM;kBAAEW,QAAQ,EAAE;gBAAO,CAAE;gBAAAf,QAAA,EAAC;cAEtE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAhC,OAAO,KAAK,MAAM,iBACjBtE,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBACb9B,KAAK,CAAC,oHAAoH,CAAC;kBAC3HT,WAAW,CAAC,IAAI,CAAC;gBACnB,CAAE;gBAAAmB,QAAA,EACH;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBACEmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEW,QAAQ,EAAE,MAAM;kBAAEI,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBACb9B,KAAK,CAAC,2CAA2C,CAAC;kBAClDT,WAAW,CAAC,IAAI,CAAC;gBACnB,CAAE;gBAAAmB,QAAA,EACH;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAhC,OAAO,KAAK,MAAM,iBACjBtE,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBAAKmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,MAAM;kBAAEW,QAAQ,EAAE;gBAAO,CAAE;gBAAAf,QAAA,EAAC;cAEtE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEU,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,MAAM;kBAAEW,QAAQ,EAAE;gBAAO,CAAE;gBAAAf,QAAA,EAAC;cAEtE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA,GApLOhC,OAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqLZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtG,OAAA;MAAKmF,KAAK,EAAE;QACVoB,IAAI,EAAE,CAAC;QACPhB,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEA1F,OAAA;QAAKmF,KAAK,EAAE;UACVoB,IAAI,EAAE,CAAC;UACPlB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBoB,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,gBAEA1F,OAAA;UAAKmF,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBE,GAAG,EAAE;UACP,CAAE;UAAAL,QAAA,gBACA1F,OAAA;YACE8G,OAAO,EAAElE,WAAY;YACrBuC,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBR,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdmB,MAAM,EAAE,MAAM;cACdf,YAAY,EAAE,KAAK;cACnBW,MAAM,EAAE,SAAS;cACjBJ,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtG,OAAA;YAAKmF,KAAK,EAAE;cAAEc,KAAK,EAAE,KAAK;cAAEb,MAAM,EAAE,MAAM;cAAEC,eAAe,EAAE;YAAO;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7EtG,OAAA;YAAMmF,KAAK,EAAE;cAAEW,KAAK,EAAE,MAAM;cAAEW,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAC9CrF,MAAM,GAAG,KAAK,GAAG;UAAQ;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNtG,OAAA;UAAKmF,KAAK,EAAE;YACVoB,IAAI,EAAE,CAAC;YACPhB,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxB1B,OAAO,EAAE,MAAM;YACfe,QAAQ,EAAE;UACZ,CAAE;UAAAlB,QAAA,GACCjF,KAAK,iBACJT,OAAA;YAAKmF,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZQ,KAAK,EAAE,MAAM;cACbnC,eAAe,EAAE,SAAS;cAC1BS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,MAAM;cAChBU,MAAM,EAAE;YACV,CAAE;YAAAzB,QAAA,GAAC,gBACG,EAACjF,KAAK;UAAA;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,EAEA/F,SAAS,iBACRP,OAAA;YAAKmF,KAAK,EAAE;cACVyB,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXS,SAAS,EAAE,uBAAuB;cAClCpC,eAAe,EAAE,iBAAiB;cAClCS,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,KAAK;cACnBM,SAAS,EAAE,QAAQ;cACnBW,MAAM,EAAE;YACV,CAAE;YAAAzB,QAAA,gBACA1F,OAAA;cAAKmF,KAAK,EAAE;gBAAEuC,YAAY,EAAE;cAAO,CAAE;cAAAhC,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDtG,OAAA;cAAKmF,KAAK,EAAE;gBACVc,KAAK,EAAE,MAAM;gBACbb,MAAM,EAAE,MAAM;gBACd6B,MAAM,EAAE,gBAAgB;gBACxBU,SAAS,EAAE,mBAAmB;gBAC9BzB,YAAY,EAAE,KAAK;gBACnB0B,SAAS,EAAE,yBAAyB;gBACpCN,MAAM,EAAE;cACV;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAjG,MAAM,gBACLL,OAAA,CAACF,UAAU;YAACO,MAAM,EAAEA,MAAO;YAACM,aAAa,EAAEA;UAAc;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5DtG,OAAA;YAAKmF,KAAK,EAAE;cACVqB,SAAS,EAAE,QAAQ;cACnBV,KAAK,EAAE,MAAM;cACbW,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,gBACA1F,OAAA;cAAKmF,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,MAAM;gBAAEiB,YAAY,EAAE;cAAO,CAAE;cAAAhC,QAAA,EAAC;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEtG,OAAA;cAAA0F,QAAA,EAAK;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtG,OAAA;QAAKmF,KAAK,EAAE;UACVc,KAAK,EAAE,OAAO;UACdZ,eAAe,EAAE,SAAS;UAC1BwC,UAAU,EAAE,gBAAgB;UAC5BtC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEA1F,OAAA;UAAKmF,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAE,MAAM;YACvBM,YAAY,EAAE,gBAAgB;YAC9BJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNtG,OAAA;UAAKmF,KAAK,EAAE;YACVoB,IAAI,EAAE,CAAC;YACPd,QAAQ,EAAE,MAAM;YAChBI,OAAO,EAAE;UACX,CAAE;UAAAH,QAAA,eACA1F,OAAA,CAACH,UAAU;YAACiI,QAAQ,EAAEjH,YAAa;YAACN,SAAS,EAAEA;UAAU;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtG,OAAA;MAAKmF,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,MAAM;QACvBsC,SAAS,EAAE,gBAAgB;QAC3BpC,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbW,QAAQ,EAAE;MACZ,CAAE;MAAAf,QAAA,gBACA1F,OAAA;QAAA0F,QAAA,EAAM;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACftG,OAAA;QAAKmF,KAAK,EAAE;UAAEoB,IAAI,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BtG,OAAA;QAAA0F,QAAA,EAAM;MAAe;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNtG,OAAA;MAAO+H,GAAG;MAAArC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAClG,EAAA,CAviBQD,GAAG;AAAA6H,EAAA,GAAH7H,GAAG;AAyiBZ,eAAeA,GAAG;;AAElB;AACA,MAAM8H,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAO9E,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAM+E,YAAY,GAAG/E,QAAQ,CAACwB,aAAa,CAAC,OAAO,CAAC;EACpDuD,YAAY,CAACC,WAAW,GAAGF,YAAY;EACvC9E,QAAQ,CAACiF,IAAI,CAACC,WAAW,CAACH,YAAY,CAAC;AACzC;AAAC,IAAAF,EAAA;AAAAM,YAAA,CAAAN,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}