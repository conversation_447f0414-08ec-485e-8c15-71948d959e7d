{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const [viewMode, setViewMode] = useState('split');\n  const [rotation, setRotation] = useState(0);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n  const handleUpload = async formData => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        setOriginalImage(e.target.result);\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: \"image/*\",\n      style: {\n        display: 'none'\n      },\n      onChange: e => {\n        const file = e.target.files[0];\n        if (file) {\n          console.log('选择了文件:', file.name);\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px',\n          marginRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ff5f57'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#ffbd2e'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          textAlign: 'center',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: '#e0e0e0'\n        },\n        children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          fontSize: '13px'\n        },\n        children: ['file', 'edit', 'view', 'tools', 'help'].map(menuKey => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              cursor: 'pointer',\n              padding: '5px 10px',\n              borderRadius: '4px',\n              backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n            },\n            onClick: () => setShowMenu(showMenu === menuKey ? null : menuKey),\n            children: menuKey === 'file' ? '文件' : menuKey === 'edit' ? '编辑' : menuKey === 'view' ? '视图' : menuKey === 'tools' ? '工具' : '帮助'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), showMenu === menuKey && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '100%',\n              left: 0,\n              backgroundColor: '#3c3c3c',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              minWidth: '180px',\n              zIndex: 1000,\n              boxShadow: '0 4px 8px rgba(0,0,0,0.3)',\n              padding: '8px 0'\n            },\n            children: [menuKey === 'file' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  var _fileInputRef$current;\n                  (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDCC1 \\u6253\\u5F00\\u56FE\\u50CF (Ctrl+O)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: result ? '#e0e0e0' : '#666',\n                  fontSize: '12px',\n                  cursor: result ? 'pointer' : 'not-allowed'\n                },\n                onClick: () => {\n                  if (result) {\n                    downloadImage();\n                    setShowMenu(null);\n                  }\n                },\n                children: \"\\uD83D\\uDCBE \\u4FDD\\u5B58\\u7ED3\\u679C (Ctrl+S)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '1px',\n                  backgroundColor: '#555',\n                  margin: '4px 0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  handleReset();\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E (Ctrl+R)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'view' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  backgroundColor: viewMode === 'split' ? '#4a90e2' : 'transparent'\n                },\n                onClick: () => {\n                  setViewMode('split');\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD00 \\u5206\\u5C4F\\u5BF9\\u6BD4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  backgroundColor: viewMode === 'original' ? '#4a90e2' : 'transparent'\n                },\n                onClick: () => {\n                  setViewMode('original');\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDDBC\\uFE0F \\u539F\\u56FE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : 'transparent'\n                },\n                onClick: () => {\n                  setViewMode('enhanced');\n                  setShowMenu(null);\n                },\n                children: \"\\u2728 \\u589E\\u5F3A\\u56FE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '1px',\n                  backgroundColor: '#555',\n                  margin: '4px 0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setZoomLevel(prev => Math.min(prev + 25, 400));\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD0D \\u653E\\u5927 (Ctrl+=)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setZoomLevel(prev => Math.max(prev - 25, 25));\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD0D \\u7F29\\u5C0F (Ctrl+-)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setZoomLevel(100);\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDCD0 \\u5B9E\\u9645\\u5927\\u5C0F (Ctrl+0)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'tools' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setRotation(prev => (prev + 90) % 360);\n                  setShowMenu(null);\n                },\n                children: \"\\u21BB \\u987A\\u65F6\\u9488\\u65CB\\u8F6C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setRotation(prev => (prev - 90 + 360) % 360);\n                  setShowMenu(null);\n                },\n                children: \"\\u21BA \\u9006\\u65F6\\u9488\\u65CB\\u8F6C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  setRotation(0);\n                  setShowMenu(null);\n                },\n                children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E\\u65CB\\u8F6C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'help' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  alert(`快捷键说明:\\n\\n文件操作:\\n• Ctrl+O: 打开图像\\n• Ctrl+S: 保存结果\\n• Ctrl+R: 重置\\n\\n视图控制:\\n• Ctrl+=: 放大\\n• Ctrl+-: 缩小\\n• Ctrl+0: 实际大小`);\n                  setShowMenu(null);\n                },\n                children: \"\\u2328\\uFE0F \\u5FEB\\u6377\\u952E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#e0e0e0',\n                  fontSize: '12px',\n                  cursor: 'pointer'\n                },\n                onClick: () => {\n                  alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能');\n                  setShowMenu(null);\n                },\n                children: \"\\u2139\\uFE0F \\u5173\\u4E8E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), menuKey === 'edit' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#666',\n                  fontSize: '12px'\n                },\n                children: \"\\u21B6 \\u64A4\\u9500 (\\u5F00\\u53D1\\u4E2D)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 12px',\n                  color: '#666',\n                  fontSize: '12px'\n                },\n                children: \"\\u21B7 \\u91CD\\u505A (\\u5F00\\u53D1\\u4E2D)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 17\n          }, this)]\n        }, menuKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReset,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83C\\uDD95 \\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            style: {\n              padding: '6px 12px',\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83D\\uDCC1 \\u6253\\u5F00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => result && downloadImage(),\n            disabled: !result,\n            style: {\n              padding: '6px 12px',\n              backgroundColor: result ? '#17a2b8' : '#6c757d',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: result ? 'pointer' : 'not-allowed',\n              fontSize: '12px'\n            },\n            children: \"\\uD83D\\uDCBE \\u4FDD\\u5B58\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '1px',\n              height: '20px',\n              backgroundColor: '#555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(prev => Math.max(prev - 25, 25)),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDD0D-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#ccc',\n                fontSize: '11px',\n                minWidth: '50px',\n                textAlign: 'center'\n              },\n              children: [zoomLevel, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setZoomLevel(prev => Math.min(prev + 25, 400)),\n              style: {\n                padding: '4px 8px',\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px'\n              },\n              children: \"\\uD83D\\uDD0D+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ccc',\n              fontSize: '12px'\n            },\n            children: isLoading ? '处理中...' : result ? '已处理' : '等待上传图像'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              left: '20px',\n              right: '20px',\n              backgroundColor: '#d32f2f',\n              color: 'white',\n              padding: '12px',\n              borderRadius: '4px',\n              fontSize: '14px',\n              zIndex: 10\n            },\n            children: [\"\\u9519\\u8BEF: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              backgroundColor: 'rgba(0,0,0,0.8)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '8px',\n              textAlign: 'center',\n              zIndex: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '30px',\n                height: '30px',\n                border: '3px solid #333',\n                borderTop: '3px solid #4a90e2',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite',\n                margin: '0 auto'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), result ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              transform: `scale(${zoomLevel / 100}) rotate(${rotation}deg)`,\n              transformOrigin: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(ResultView, {\n              result: result,\n              originalImage: originalImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#888',\n              fontSize: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '20px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u8BF7\\u5728\\u53F3\\u4FA7\\u9762\\u677F\\u4E0A\\u4F20\\u56FE\\u50CF\\u5F00\\u59CB\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          },\n          children: \"\\u53C2\\u6570\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          },\n          children: /*#__PURE__*/_jsxDEV(UploadForm, {\n            onUpload: handleUpload,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: isLoading ? '🔄 处理中...' : result ? '✅ 处理完成' : error ? '❌ 处理失败' : '⏳ 就绪'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this), imageInfo && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCC4 \", imageInfo.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCD0 \", imageInfo.dimensions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCBE \", imageInfo.size]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), processingTime && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u23F1\\uFE0F \", processingTime, \"s\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), zoomLevel !== 100 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDD0D \", zoomLevel, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), rotation !== 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '1px',\n            height: '16px',\n            backgroundColor: '#555',\n            margin: '0 10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u21BB \", rotation, \"\\xB0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\uD83D\\uDE80 RealESRGAN v2.1 Enhanced\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"GkmyJuU5iK16dA3VuAq//3myZjs=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "UploadForm", "ResultView", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "showMenu", "setShowMenu", "imageInfo", "setImageInfo", "processingTime", "setProcessingTime", "zoomLevel", "setZoomLevel", "viewMode", "setViewMode", "rotation", "setRotation", "fileInputRef", "handleKeyDown", "e", "ctrl<PERSON>ey", "key", "preventDefault", "current", "click", "handleReset", "downloadImage", "prev", "Math", "min", "max", "document", "addEventListener", "removeEventListener", "handleUpload", "formData", "startTime", "Date", "now", "file", "get", "reader", "FileReader", "onload", "target", "img", "Image", "name", "size", "toFixed", "dimensions", "width", "height", "type", "src", "readAsDataURL", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "endTime", "err", "console", "message", "enhanced_image", "link", "createElement", "href", "download", "style", "backgroundColor", "fontFamily", "display", "flexDirection", "overflow", "children", "ref", "accept", "onChange", "files", "log", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderBottom", "alignItems", "padding", "color", "gap", "marginRight", "borderRadius", "flex", "textAlign", "fontSize", "fontWeight", "map", "<PERSON><PERSON>ey", "position", "cursor", "onClick", "top", "left", "border", "min<PERSON><PERSON><PERSON>", "zIndex", "boxShadow", "_fileInputRef$current", "margin", "alert", "_fileInputRef$current2", "disabled", "justifyContent", "right", "transform", "marginBottom", "borderTop", "animation", "transform<PERSON><PERSON>in", "borderLeft", "onUpload", "jsx", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const [showMenu, setShowMenu] = useState(null);\n  const [imageInfo, setImageInfo] = useState(null);\n  const [processingTime, setProcessingTime] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(100);\n  const [viewMode, setViewMode] = useState('split');\n  const [rotation, setRotation] = useState(0);\n  const fileInputRef = useRef(null);\n\n  // 快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (e.ctrlKey && e.key === 'o') {\n        e.preventDefault();\n        if (fileInputRef.current) {\n          fileInputRef.current.click();\n        }\n      }\n      if (e.ctrlKey && e.key === 'r') {\n        e.preventDefault();\n        handleReset();\n      }\n      if (e.ctrlKey && e.key === 's' && result) {\n        e.preventDefault();\n        downloadImage();\n      }\n      if (e.ctrlKey && e.key === '=') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.min(prev + 25, 400));\n      }\n      if (e.ctrlKey && e.key === '-') {\n        e.preventDefault();\n        setZoomLevel(prev => Math.max(prev - 25, 25));\n      }\n      if (e.ctrlKey && e.key === '0') {\n        e.preventDefault();\n        setZoomLevel(100);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [result]);\n\n  const handleUpload = async (formData) => {\n    const startTime = Date.now();\n    setIsLoading(true);\n    setError(null);\n    \n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setOriginalImage(e.target.result);\n        const img = new Image();\n        img.onload = () => {\n          setImageInfo({\n            name: file.name,\n            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',\n            dimensions: `${img.width} × ${img.height}`,\n            type: file.type\n          });\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      const response = await fetch('http://localhost:8001/enhance/', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      const endTime = Date.now();\n      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const downloadImage = () => {\n    if (result && result.enhanced_image) {\n      const link = document.createElement('a');\n      link.href = `data:image/png;base64,${result.enhanced_image}`;\n      link.download = `enhanced_${Date.now()}.png`;\n      link.click();\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n    setImageInfo(null);\n    setProcessingTime(null);\n    setZoomLevel(100);\n    setViewMode('split');\n    setRotation(0);\n  };\n\n  return (\n    <div style={{ \n      height: '100vh',\n      backgroundColor: '#2b2b2b',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden'\n    }}>\n      {/* 隐藏的文件输入 */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/*\"\n        style={{ display: 'none' }}\n        onChange={(e) => {\n          const file = e.target.files[0];\n          if (file) {\n            console.log('选择了文件:', file.name);\n          }\n        }}\n      />\n\n      {/* 顶部菜单栏 */}\n      <div style={{\n        height: '60px',\n        backgroundColor: '#3c3c3c',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 20px',\n        color: '#fff'\n      }}>\n        {/* macOS风格的窗口控制按钮 */}\n        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>\n          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>\n        </div>\n        \n        {/* 应用标题 */}\n        <div style={{ \n          flex: 1, \n          textAlign: 'center', \n          fontSize: '14px', \n          fontWeight: '500',\n          color: '#e0e0e0'\n        }}>\n          图像增强工具\n        </div>\n        \n        {/* 顶部菜单 */}\n        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>\n          {['file', 'edit', 'view', 'tools', 'help'].map(menuKey => (\n            <div key={menuKey} style={{ position: 'relative' }}>\n              <span \n                style={{ \n                  cursor: 'pointer', \n                  padding: '5px 10px', \n                  borderRadius: '4px',\n                  backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'\n                }}\n                onClick={() => setShowMenu(showMenu === menuKey ? null : menuKey)}\n              >\n                {menuKey === 'file' ? '文件' : \n                 menuKey === 'edit' ? '编辑' :\n                 menuKey === 'view' ? '视图' :\n                 menuKey === 'tools' ? '工具' : '帮助'}\n              </span>\n              \n              {/* 下拉菜单 */}\n              {showMenu === menuKey && (\n                <div style={{\n                  position: 'absolute',\n                  top: '100%',\n                  left: 0,\n                  backgroundColor: '#3c3c3c',\n                  border: '1px solid #555',\n                  borderRadius: '4px',\n                  minWidth: '180px',\n                  zIndex: 1000,\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.3)',\n                  padding: '8px 0'\n                }}>\n                  {menuKey === 'file' && (\n                    <>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { fileInputRef.current?.click(); setShowMenu(null); }}\n                      >\n                        📁 打开图像 (Ctrl+O)\n                      </div>\n                      <div \n                        style={{ \n                          padding: '8px 12px', \n                          color: result ? '#e0e0e0' : '#666', \n                          fontSize: '12px', \n                          cursor: result ? 'pointer' : 'not-allowed' \n                        }}\n                        onClick={() => { if(result) { downloadImage(); setShowMenu(null); } }}\n                      >\n                        💾 保存结果 (Ctrl+S)\n                      </div>\n                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { handleReset(); setShowMenu(null); }}\n                      >\n                        🔄 重置 (Ctrl+R)\n                      </div>\n                    </>\n                  )}\n                  \n                  {menuKey === 'view' && (\n                    <>\n                      <div \n                        style={{ \n                          padding: '8px 12px', \n                          color: '#e0e0e0', \n                          fontSize: '12px', \n                          cursor: 'pointer',\n                          backgroundColor: viewMode === 'split' ? '#4a90e2' : 'transparent'\n                        }}\n                        onClick={() => { setViewMode('split'); setShowMenu(null); }}\n                      >\n                        🔀 分屏对比\n                      </div>\n                      <div \n                        style={{ \n                          padding: '8px 12px', \n                          color: '#e0e0e0', \n                          fontSize: '12px', \n                          cursor: 'pointer',\n                          backgroundColor: viewMode === 'original' ? '#4a90e2' : 'transparent'\n                        }}\n                        onClick={() => { setViewMode('original'); setShowMenu(null); }}\n                      >\n                        🖼️ 原图\n                      </div>\n                      <div \n                        style={{ \n                          padding: '8px 12px', \n                          color: '#e0e0e0', \n                          fontSize: '12px', \n                          cursor: 'pointer',\n                          backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : 'transparent'\n                        }}\n                        onClick={() => { setViewMode('enhanced'); setShowMenu(null); }}\n                      >\n                        ✨ 增强图\n                      </div>\n                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setZoomLevel(prev => Math.min(prev + 25, 400)); setShowMenu(null); }}\n                      >\n                        🔍 放大 (Ctrl+=)\n                      </div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setZoomLevel(prev => Math.max(prev - 25, 25)); setShowMenu(null); }}\n                      >\n                        🔍 缩小 (Ctrl+-)\n                      </div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setZoomLevel(100); setShowMenu(null); }}\n                      >\n                        📐 实际大小 (Ctrl+0)\n                      </div>\n                    </>\n                  )}\n                  \n                  {menuKey === 'tools' && (\n                    <>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setRotation(prev => (prev + 90) % 360); setShowMenu(null); }}\n                      >\n                        ↻ 顺时针旋转\n                      </div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setRotation(prev => (prev - 90 + 360) % 360); setShowMenu(null); }}\n                      >\n                        ↺ 逆时针旋转\n                      </div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { setRotation(0); setShowMenu(null); }}\n                      >\n                        🔄 重置旋转\n                      </div>\n                    </>\n                  )}\n                  \n                  {menuKey === 'help' && (\n                    <>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { \n                          alert(`快捷键说明:\\n\\n文件操作:\\n• Ctrl+O: 打开图像\\n• Ctrl+S: 保存结果\\n• Ctrl+R: 重置\\n\\n视图控制:\\n• Ctrl+=: 放大\\n• Ctrl+-: 缩小\\n• Ctrl+0: 实际大小`); \n                          setShowMenu(null); \n                        }}\n                      >\n                        ⌨️ 快捷键\n                      </div>\n                      <div \n                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}\n                        onClick={() => { \n                          alert('AI图像增强工具 v2.1\\n基于RealESRGAN技术\\n支持多种图像增强功能'); \n                          setShowMenu(null); \n                        }}\n                      >\n                        ℹ️ 关于\n                      </div>\n                    </>\n                  )}\n                  \n                  {menuKey === 'edit' && (\n                    <>\n                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>\n                        ↶ 撤销 (开发中)\n                      </div>\n                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>\n                        ↷ 重做 (开发中)\n                      </div>\n                    </>\n                  )}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex',\n        overflow: 'hidden'\n      }}>\n        {/* 左侧主工作区 */}\n        <div style={{\n          flex: 1,\n          backgroundColor: '#1e1e1e',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative'\n        }}>\n          {/* 工具栏 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            gap: '10px'\n          }}>\n            {/* 文件操作 */}\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#4a90e2',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🆕 新建\n            </button>\n            \n            <button\n              onClick={() => fileInputRef.current?.click()}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              📁 打开\n            </button>\n            \n            <button\n              onClick={() => result && downloadImage()}\n              disabled={!result}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: result ? '#17a2b8' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: result ? 'pointer' : 'not-allowed',\n                fontSize: '12px'\n              }}\n            >\n              💾 保存\n            </button>\n            \n            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>\n            \n            {/* 缩放控制 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>\n              <button\n                onClick={() => setZoomLevel(prev => Math.max(prev - 25, 25))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                🔍-\n              </button>\n              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '50px', textAlign: 'center' }}>\n                {zoomLevel}%\n              </span>\n              <button\n                onClick={() => setZoomLevel(prev => Math.min(prev + 25, 400))}\n                style={{\n                  padding: '4px 8px',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '3px',\n                  cursor: 'pointer',\n                  fontSize: '11px'\n                }}\n              >\n                🔍+\n              </button>\n            </div>\n            \n            <div style={{ flex: 1 }}></div>\n            \n            {/* 状态信息 */}\n            <span style={{ color: '#ccc', fontSize: '12px' }}>\n              {isLoading ? '处理中...' : result ? '已处理' : '等待上传图像'}\n            </span>\n          </div>\n\n          {/* 图像显示区域 */}\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '20px',\n            position: 'relative'\n          }}>\n            {error && (\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                left: '20px',\n                right: '20px',\n                backgroundColor: '#d32f2f',\n                color: 'white',\n                padding: '12px',\n                borderRadius: '4px',\n                fontSize: '14px',\n                zIndex: 10\n              }}>\n                错误: {error}\n              </div>\n            )}\n\n            {isLoading && (\n              <div style={{\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                backgroundColor: 'rgba(0,0,0,0.8)',\n                color: 'white',\n                padding: '20px',\n                borderRadius: '8px',\n                textAlign: 'center',\n                zIndex: 10\n              }}>\n                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>\n                <div style={{ \n                  width: '30px', \n                  height: '30px', \n                  border: '3px solid #333',\n                  borderTop: '3px solid #4a90e2',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite',\n                  margin: '0 auto'\n                }}></div>\n              </div>\n            )}\n\n            {result ? (\n              <div style={{ transform: `scale(${zoomLevel / 100}) rotate(${rotation}deg)`, transformOrigin: 'center' }}>\n                <ResultView result={result} originalImage={originalImage} />\n              </div>\n            ) : (\n              <div style={{\n                textAlign: 'center',\n                color: '#888',\n                fontSize: '16px'\n              }}>\n                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>\n                <div>请在右侧面板上传图像开始处理</div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 右侧参数面板 */}\n        <div style={{\n          width: '320px',\n          backgroundColor: '#2d2d2d',\n          borderLeft: '1px solid #555',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {/* 面板标题 */}\n          <div style={{\n            height: '50px',\n            backgroundColor: '#333',\n            borderBottom: '1px solid #555',\n            display: 'flex',\n            alignItems: 'center',\n            padding: '0 15px',\n            color: '#e0e0e0',\n            fontSize: '14px',\n            fontWeight: '500'\n          }}>\n            参数设置\n          </div>\n\n          {/* 参数内容 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '0'\n          }}>\n            <UploadForm onUpload={handleUpload} isLoading={isLoading} />\n          </div>\n        </div>\n      </div>\n\n      {/* 底部状态栏 */}\n      <div style={{\n        height: '30px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        padding: '0 15px',\n        color: '#ccc',\n        fontSize: '12px'\n      }}>\n        {/* 左侧状态信息 */}\n        <span>\n          {isLoading ? '🔄 处理中...' : \n           result ? '✅ 处理完成' : \n           error ? '❌ 处理失败' : '⏳ 就绪'}\n        </span>\n        \n        {/* 图像信息 */}\n        {imageInfo && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>📄 {imageInfo.name}</span>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>📐 {imageInfo.dimensions}</span>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>💾 {imageInfo.size}</span>\n          </>\n        )}\n        \n        {/* 处理时间 */}\n        {processingTime && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>⏱️ {processingTime}s</span>\n          </>\n        )}\n        \n        {/* 缩放信息 */}\n        {zoomLevel !== 100 && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>🔍 {zoomLevel}%</span>\n          </>\n        )}\n        \n        {/* 旋转信息 */}\n        {rotation !== 0 && (\n          <>\n            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>\n            <span>↻ {rotation}°</span>\n          </>\n        )}\n        \n        <div style={{ flex: 1 }}></div>\n        \n        {/* 右侧版本信息 */}\n        <span>🚀 RealESRGAN v2.1 Enhanced</span>\n      </div>\n\n      {/* CSS动画 */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM+B,YAAY,GAAG7B,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAD,SAAS,CAAC,MAAM;IACd,MAAM+B,aAAa,GAAIC,CAAC,IAAK;MAC3B,IAAIA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClB,IAAIL,YAAY,CAACM,OAAO,EAAE;UACxBN,YAAY,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9B;MACF;MACA,IAAIL,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBG,WAAW,CAAC,CAAC;MACf;MACA,IAAIN,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,IAAIxB,MAAM,EAAE;QACxCsB,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBI,aAAa,CAAC,CAAC;MACjB;MACA,IAAIP,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBV,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;MAChD;MACA,IAAIR,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBV,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;MAC/C;MACA,IAAIR,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,GAAG,KAAK,GAAG,EAAE;QAC9BF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBV,YAAY,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;IAEDmB,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEd,aAAa,CAAC;IACnD,OAAO,MAAMa,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEf,aAAa,CAAC;EACrE,CAAC,EAAE,CAACrB,MAAM,CAAC,CAAC;EAEZ,MAAMqC,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5BtC,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,MAAMqC,IAAI,GAAGJ,QAAQ,CAACK,GAAG,CAAC,MAAM,CAAC;IACjC,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIxB,CAAC,IAAK;QACrBf,gBAAgB,CAACe,CAAC,CAACyB,MAAM,CAAC/C,MAAM,CAAC;QACjC,MAAMgD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACF,MAAM,GAAG,MAAM;UACjBnC,YAAY,CAAC;YACXuC,IAAI,EAAER,IAAI,CAACQ,IAAI;YACfC,IAAI,EAAE,CAACT,IAAI,CAACS,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;YAClDC,UAAU,EAAE,GAAGL,GAAG,CAACM,KAAK,MAAMN,GAAG,CAACO,MAAM,EAAE;YAC1CC,IAAI,EAAEd,IAAI,CAACc;UACb,CAAC,CAAC;QACJ,CAAC;QACDR,GAAG,CAACS,GAAG,GAAGnC,CAAC,CAACyB,MAAM,CAAC/C,MAAM;MAC3B,CAAC;MACD4C,MAAM,CAACc,aAAa,CAAChB,IAAI,CAAC;IAC5B;IAEA,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAExB;MACR,CAAC,CAAC;MAEF,IAAI,CAACqB,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,MAAMI,OAAO,GAAG7B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B5B,iBAAiB,CAAC,CAAC,CAACwD,OAAO,GAAG9B,SAAS,IAAI,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5DnD,SAAS,CAACmE,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACnE,KAAK,CAAC,OAAO,EAAEkE,GAAG,CAAC;MAC3BjE,QAAQ,CAACiE,GAAG,CAACE,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRrE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI7B,MAAM,IAAIA,MAAM,CAACyE,cAAc,EAAE;MACnC,MAAMC,IAAI,GAAGxC,QAAQ,CAACyC,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG,yBAAyB5E,MAAM,CAACyE,cAAc,EAAE;MAC5DC,IAAI,CAACG,QAAQ,GAAG,YAAYrC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC5CiC,IAAI,CAAC/C,KAAK,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB3B,SAAS,CAAC,IAAI,CAAC;IACfI,QAAQ,CAAC,IAAI,CAAC;IACdE,gBAAgB,CAAC,IAAI,CAAC;IACtBI,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,YAAY,CAAC,GAAG,CAAC;IACjBE,WAAW,CAAC,OAAO,CAAC;IACpBE,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACExB,OAAA;IAAKmF,KAAK,EAAE;MACVvB,MAAM,EAAE,OAAO;MACfwB,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,mEAAmE;MAC/EC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAzF,OAAA;MACE0F,GAAG,EAAEjE,YAAa;MAClBoC,IAAI,EAAC,MAAM;MACX8B,MAAM,EAAC,SAAS;MAChBR,KAAK,EAAE;QAAEG,OAAO,EAAE;MAAO,CAAE;MAC3BM,QAAQ,EAAGjE,CAAC,IAAK;QACf,MAAMoB,IAAI,GAAGpB,CAAC,CAACyB,MAAM,CAACyC,KAAK,CAAC,CAAC,CAAC;QAC9B,IAAI9C,IAAI,EAAE;UACR6B,OAAO,CAACkB,GAAG,CAAC,QAAQ,EAAE/C,IAAI,CAACQ,IAAI,CAAC;QAClC;MACF;IAAE;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFlG,OAAA;MAAKmF,KAAK,EAAE;QACVvB,MAAM,EAAE,MAAM;QACdwB,eAAe,EAAE,SAAS;QAC1Be,YAAY,EAAE,gBAAgB;QAC9Bb,OAAO,EAAE,MAAM;QACfc,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAb,QAAA,gBAEAzF,OAAA;QAAKmF,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEiB,GAAG,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAC/DzF,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAE6C,YAAY,EAAE,KAAK;YAAErB,eAAe,EAAE;UAAU;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGlG,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAE6C,YAAY,EAAE,KAAK;YAAErB,eAAe,EAAE;UAAU;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGlG,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAE6C,YAAY,EAAE,KAAK;YAAErB,eAAe,EAAE;UAAU;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAGNlG,OAAA;QAAKmF,KAAK,EAAE;UACVuB,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBP,KAAK,EAAE;QACT,CAAE;QAAAb,QAAA,EAAC;MAEH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNlG,OAAA;QAAKmF,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEiB,GAAG,EAAE,MAAM;UAAEK,QAAQ,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAC5D,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAACqB,GAAG,CAACC,OAAO,iBACpD/G,OAAA;UAAmBmF,KAAK,EAAE;YAAE6B,QAAQ,EAAE;UAAW,CAAE;UAAAvB,QAAA,gBACjDzF,OAAA;YACEmF,KAAK,EAAE;cACL8B,MAAM,EAAE,SAAS;cACjBZ,OAAO,EAAE,UAAU;cACnBI,YAAY,EAAE,KAAK;cACnBrB,eAAe,EAAEvE,QAAQ,KAAKkG,OAAO,GAAG,SAAS,GAAG;YACtD,CAAE;YACFG,OAAO,EAAEA,CAAA,KAAMpG,WAAW,CAACD,QAAQ,KAAKkG,OAAO,GAAG,IAAI,GAAGA,OAAO,CAAE;YAAAtB,QAAA,EAEjEsB,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,MAAM,GAAG,IAAI,GACzBA,OAAO,KAAK,OAAO,GAAG,IAAI,GAAG;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EAGNrF,QAAQ,KAAKkG,OAAO,iBACnB/G,OAAA;YAAKmF,KAAK,EAAE;cACV6B,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPhC,eAAe,EAAE,SAAS;cAC1BiC,MAAM,EAAE,gBAAgB;cACxBZ,YAAY,EAAE,KAAK;cACnBa,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE,IAAI;cACZC,SAAS,EAAE,2BAA2B;cACtCnB,OAAO,EAAE;YACX,CAAE;YAAAZ,QAAA,GACCsB,OAAO,KAAK,MAAM,iBACjB/G,OAAA,CAAAE,SAAA;cAAAuF,QAAA,gBACEzF,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAA,IAAAO,qBAAA;kBAAE,CAAAA,qBAAA,GAAAhG,YAAY,CAACM,OAAO,cAAA0F,qBAAA,uBAApBA,qBAAA,CAAsBzF,KAAK,CAAC,CAAC;kBAAElB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EACtE;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBACLkB,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAEjG,MAAM,GAAG,SAAS,GAAG,MAAM;kBAClCuG,QAAQ,EAAE,MAAM;kBAChBK,MAAM,EAAE5G,MAAM,GAAG,SAAS,GAAG;gBAC/B,CAAE;gBACF6G,OAAO,EAAEA,CAAA,KAAM;kBAAE,IAAG7G,MAAM,EAAE;oBAAE6B,aAAa,CAAC,CAAC;oBAAEpB,WAAW,CAAC,IAAI,CAAC;kBAAE;gBAAE,CAAE;gBAAA2E,QAAA,EACvE;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEvB,MAAM,EAAE,KAAK;kBAAEwB,eAAe,EAAE,MAAM;kBAAEsC,MAAM,EAAE;gBAAQ;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ElG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAEjF,WAAW,CAAC,CAAC;kBAAEnB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EACtD;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAa,OAAO,KAAK,MAAM,iBACjB/G,OAAA,CAAAE,SAAA;cAAAuF,QAAA,gBACEzF,OAAA;gBACEmF,KAAK,EAAE;kBACLkB,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAE,SAAS;kBAChBM,QAAQ,EAAE,MAAM;kBAChBK,MAAM,EAAE,SAAS;kBACjB7B,eAAe,EAAE/D,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG;gBACtD,CAAE;gBACF6F,OAAO,EAAEA,CAAA,KAAM;kBAAE5F,WAAW,CAAC,OAAO,CAAC;kBAAER,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EAC7D;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBACLkB,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAE,SAAS;kBAChBM,QAAQ,EAAE,MAAM;kBAChBK,MAAM,EAAE,SAAS;kBACjB7B,eAAe,EAAE/D,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG;gBACzD,CAAE;gBACF6F,OAAO,EAAEA,CAAA,KAAM;kBAAE5F,WAAW,CAAC,UAAU,CAAC;kBAAER,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EAChE;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBACLkB,OAAO,EAAE,UAAU;kBACnBC,KAAK,EAAE,SAAS;kBAChBM,QAAQ,EAAE,MAAM;kBAChBK,MAAM,EAAE,SAAS;kBACjB7B,eAAe,EAAE/D,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAG;gBACzD,CAAE;gBACF6F,OAAO,EAAEA,CAAA,KAAM;kBAAE5F,WAAW,CAAC,UAAU,CAAC;kBAAER,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EAChE;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEvB,MAAM,EAAE,KAAK;kBAAEwB,eAAe,EAAE,MAAM;kBAAEsC,MAAM,EAAE;gBAAQ;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ElG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE9F,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;kBAAErB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EACvF;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE9F,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;kBAAErB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EACtF;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE9F,YAAY,CAAC,GAAG,CAAC;kBAAEN,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EAC1D;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAa,OAAO,KAAK,OAAO,iBAClB/G,OAAA,CAAAE,SAAA;cAAAuF,QAAA,gBACEzF,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE1F,WAAW,CAACW,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,IAAI,GAAG,CAAC;kBAAErB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EAC/E;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE1F,WAAW,CAACW,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC;kBAAErB,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EACrF;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBAAE1F,WAAW,CAAC,CAAC,CAAC;kBAAEV,WAAW,CAAC,IAAI,CAAC;gBAAE,CAAE;gBAAA2E,QAAA,EACvD;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAa,OAAO,KAAK,MAAM,iBACjB/G,OAAA,CAAAE,SAAA;cAAAuF,QAAA,gBACEzF,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBACbS,KAAK,CAAC,oHAAoH,CAAC;kBAC3H7G,WAAW,CAAC,IAAI,CAAC;gBACnB,CAAE;gBAAA2E,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBACEmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,SAAS;kBAAEM,QAAQ,EAAE,MAAM;kBAAEK,MAAM,EAAE;gBAAU,CAAE;gBACtFC,OAAO,EAAEA,CAAA,KAAM;kBACbS,KAAK,CAAC,2CAA2C,CAAC;kBAClD7G,WAAW,CAAC,IAAI,CAAC;gBACnB,CAAE;gBAAA2E,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EAEAa,OAAO,KAAK,MAAM,iBACjB/G,OAAA,CAAAE,SAAA;cAAAuF,QAAA,gBACEzF,OAAA;gBAAKmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,MAAM;kBAAEM,QAAQ,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EAAC;cAEtE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlG,OAAA;gBAAKmF,KAAK,EAAE;kBAAEkB,OAAO,EAAE,UAAU;kBAAEC,KAAK,EAAE,MAAM;kBAAEM,QAAQ,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EAAC;cAEtE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA,GAhLOa,OAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiLZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAKmF,KAAK,EAAE;QACVuB,IAAI,EAAE,CAAC;QACPpB,OAAO,EAAE,MAAM;QACfE,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEAzF,OAAA;QAAKmF,KAAK,EAAE;UACVuB,IAAI,EAAE,CAAC;UACPtB,eAAe,EAAE,SAAS;UAC1BE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvByB,QAAQ,EAAE;QACZ,CAAE;QAAAvB,QAAA,gBAEAzF,OAAA;UAAKmF,KAAK,EAAE;YACVvB,MAAM,EAAE,MAAM;YACdwB,eAAe,EAAE,MAAM;YACvBe,YAAY,EAAE,gBAAgB;YAC9Bb,OAAO,EAAE,MAAM;YACfc,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBE,GAAG,EAAE;UACP,CAAE;UAAAd,QAAA,gBAEAzF,OAAA;YACEkH,OAAO,EAAEjF,WAAY;YACrBkD,KAAK,EAAE;cACLkB,OAAO,EAAE,UAAU;cACnBjB,eAAe,EAAE,SAAS;cAC1BkB,KAAK,EAAE,OAAO;cACde,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBQ,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlG,OAAA;YACEkH,OAAO,EAAEA,CAAA;cAAA,IAAAU,sBAAA;cAAA,QAAAA,sBAAA,GAAMnG,YAAY,CAACM,OAAO,cAAA6F,sBAAA,uBAApBA,sBAAA,CAAsB5F,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CmD,KAAK,EAAE;cACLkB,OAAO,EAAE,UAAU;cACnBjB,eAAe,EAAE,SAAS;cAC1BkB,KAAK,EAAE,OAAO;cACde,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBQ,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlG,OAAA;YACEkH,OAAO,EAAEA,CAAA,KAAM7G,MAAM,IAAI6B,aAAa,CAAC,CAAE;YACzC2F,QAAQ,EAAE,CAACxH,MAAO;YAClB8E,KAAK,EAAE;cACLkB,OAAO,EAAE,UAAU;cACnBjB,eAAe,EAAE/E,MAAM,GAAG,SAAS,GAAG,SAAS;cAC/CiG,KAAK,EAAE,OAAO;cACde,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBQ,MAAM,EAAE5G,MAAM,GAAG,SAAS,GAAG,aAAa;cAC1CuG,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlG,OAAA;YAAKmF,KAAK,EAAE;cAAExB,KAAK,EAAE,KAAK;cAAEC,MAAM,EAAE,MAAM;cAAEwB,eAAe,EAAE;YAAO;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG7ElG,OAAA;YAAKmF,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEc,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAM,CAAE;YAAAd,QAAA,gBAChEzF,OAAA;cACEkH,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACE,GAAG,CAACH,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAE;cAC7DgD,KAAK,EAAE;gBACLkB,OAAO,EAAE,SAAS;gBAClBjB,eAAe,EAAE,SAAS;gBAC1BkB,KAAK,EAAE,OAAO;gBACde,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBL,QAAQ,EAAE;cACZ,CAAE;cAAAnB,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cAAMmF,KAAK,EAAE;gBAAEmB,KAAK,EAAE,MAAM;gBAAEM,QAAQ,EAAE,MAAM;gBAAEU,QAAQ,EAAE,MAAM;gBAAEX,SAAS,EAAE;cAAS,CAAE;cAAAlB,QAAA,GACrFtE,SAAS,EAAC,GACb;YAAA;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlG,OAAA;cACEkH,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAACe,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,GAAG,CAAC,CAAE;cAC9DgD,KAAK,EAAE;gBACLkB,OAAO,EAAE,SAAS;gBAClBjB,eAAe,EAAE,SAAS;gBAC1BkB,KAAK,EAAE,OAAO;gBACde,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBL,QAAQ,EAAE;cACZ,CAAE;cAAAnB,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlG,OAAA;YAAKmF,KAAK,EAAE;cAAEuB,IAAI,EAAE;YAAE;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAG/BlG,OAAA;YAAMmF,KAAK,EAAE;cAAEmB,KAAK,EAAE,MAAM;cAAEM,QAAQ,EAAE;YAAO,CAAE;YAAAnB,QAAA,EAC9ClF,SAAS,GAAG,QAAQ,GAAGF,MAAM,GAAG,KAAK,GAAG;UAAQ;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNlG,OAAA;UAAKmF,KAAK,EAAE;YACVuB,IAAI,EAAE,CAAC;YACPpB,OAAO,EAAE,MAAM;YACfc,UAAU,EAAE,QAAQ;YACpB0B,cAAc,EAAE,QAAQ;YACxBzB,OAAO,EAAE,MAAM;YACfW,QAAQ,EAAE;UACZ,CAAE;UAAAvB,QAAA,GACChF,KAAK,iBACJT,OAAA;YAAKmF,KAAK,EAAE;cACV6B,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZW,KAAK,EAAE,MAAM;cACb3C,eAAe,EAAE,SAAS;cAC1BkB,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfI,YAAY,EAAE,KAAK;cACnBG,QAAQ,EAAE,MAAM;cAChBW,MAAM,EAAE;YACV,CAAE;YAAA9B,QAAA,GAAC,gBACG,EAAChF,KAAK;UAAA;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,EAEA3F,SAAS,iBACRP,OAAA;YAAKmF,KAAK,EAAE;cACV6B,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXY,SAAS,EAAE,uBAAuB;cAClC5C,eAAe,EAAE,iBAAiB;cAClCkB,KAAK,EAAE,OAAO;cACdD,OAAO,EAAE,MAAM;cACfI,YAAY,EAAE,KAAK;cACnBE,SAAS,EAAE,QAAQ;cACnBY,MAAM,EAAE;YACV,CAAE;YAAA9B,QAAA,gBACAzF,OAAA;cAAKmF,KAAK,EAAE;gBAAE8C,YAAY,EAAE;cAAO,CAAE;cAAAxC,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDlG,OAAA;cAAKmF,KAAK,EAAE;gBACVxB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdyD,MAAM,EAAE,gBAAgB;gBACxBa,SAAS,EAAE,mBAAmB;gBAC9BzB,YAAY,EAAE,KAAK;gBACnB0B,SAAS,EAAE,yBAAyB;gBACpCT,MAAM,EAAE;cACV;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEA7F,MAAM,gBACLL,OAAA;YAAKmF,KAAK,EAAE;cAAE6C,SAAS,EAAE,SAAS7G,SAAS,GAAG,GAAG,YAAYI,QAAQ,MAAM;cAAE6G,eAAe,EAAE;YAAS,CAAE;YAAA3C,QAAA,eACvGzF,OAAA,CAACF,UAAU;cAACO,MAAM,EAAEA,MAAO;cAACM,aAAa,EAAEA;YAAc;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,gBAENlG,OAAA;YAAKmF,KAAK,EAAE;cACVwB,SAAS,EAAE,QAAQ;cACnBL,KAAK,EAAE,MAAM;cACbM,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,gBACAzF,OAAA;cAAKmF,KAAK,EAAE;gBAAEyB,QAAQ,EAAE,MAAM;gBAAEqB,YAAY,EAAE;cAAO,CAAE;cAAAxC,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChElG,OAAA;cAAAyF,QAAA,EAAK;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlG,OAAA;QAAKmF,KAAK,EAAE;UACVxB,KAAK,EAAE,OAAO;UACdyB,eAAe,EAAE,SAAS;UAC1BiD,UAAU,EAAE,gBAAgB;UAC5B/C,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAEAzF,OAAA;UAAKmF,KAAK,EAAE;YACVvB,MAAM,EAAE,MAAM;YACdwB,eAAe,EAAE,MAAM;YACvBe,YAAY,EAAE,gBAAgB;YAC9Bb,OAAO,EAAE,MAAM;YACfc,UAAU,EAAE,QAAQ;YACpBC,OAAO,EAAE,QAAQ;YACjBC,KAAK,EAAE,SAAS;YAChBM,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAApB,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGNlG,OAAA;UAAKmF,KAAK,EAAE;YACVuB,IAAI,EAAE,CAAC;YACPlB,QAAQ,EAAE,MAAM;YAChBa,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,eACAzF,OAAA,CAACH,UAAU;YAACyI,QAAQ,EAAE5F,YAAa;YAACnC,SAAS,EAAEA;UAAU;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAKmF,KAAK,EAAE;QACVvB,MAAM,EAAE,MAAM;QACdwB,eAAe,EAAE,MAAM;QACvB8C,SAAS,EAAE,gBAAgB;QAC3B5C,OAAO,EAAE,MAAM;QACfc,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,MAAM;QACbM,QAAQ,EAAE;MACZ,CAAE;MAAAnB,QAAA,gBAEAzF,OAAA;QAAAyF,QAAA,EACGlF,SAAS,GAAG,WAAW,GACvBF,MAAM,GAAG,QAAQ,GACjBI,KAAK,GAAG,QAAQ,GAAG;MAAM;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,EAGNnF,SAAS,iBACRf,OAAA,CAAAE,SAAA;QAAAuF,QAAA,gBACEzF,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAEwB,eAAe,EAAE,MAAM;YAAEsC,MAAM,EAAE;UAAS;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FlG,OAAA;UAAAyF,QAAA,GAAM,eAAG,EAAC1E,SAAS,CAACwC,IAAI;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChClG,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAEwB,eAAe,EAAE,MAAM;YAAEsC,MAAM,EAAE;UAAS;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FlG,OAAA;UAAAyF,QAAA,GAAM,eAAG,EAAC1E,SAAS,CAAC2C,UAAU;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtClG,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAEwB,eAAe,EAAE,MAAM;YAAEsC,MAAM,EAAE;UAAS;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FlG,OAAA;UAAAyF,QAAA,GAAM,eAAG,EAAC1E,SAAS,CAACyC,IAAI;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,eAChC,CACH,EAGAjF,cAAc,iBACbjB,OAAA,CAAAE,SAAA;QAAAuF,QAAA,gBACEzF,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAEwB,eAAe,EAAE,MAAM;YAAEsC,MAAM,EAAE;UAAS;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FlG,OAAA;UAAAyF,QAAA,GAAM,eAAG,EAACxE,cAAc,EAAC,GAAC;QAAA;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eACjC,CACH,EAGA/E,SAAS,KAAK,GAAG,iBAChBnB,OAAA,CAAAE,SAAA;QAAAuF,QAAA,gBACEzF,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAEwB,eAAe,EAAE,MAAM;YAAEsC,MAAM,EAAE;UAAS;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FlG,OAAA;UAAAyF,QAAA,GAAM,eAAG,EAACtE,SAAS,EAAC,GAAC;QAAA;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eAC5B,CACH,EAGA3E,QAAQ,KAAK,CAAC,iBACbvB,OAAA,CAAAE,SAAA;QAAAuF,QAAA,gBACEzF,OAAA;UAAKmF,KAAK,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,MAAM;YAAEwB,eAAe,EAAE,MAAM;YAAEsC,MAAM,EAAE;UAAS;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FlG,OAAA;UAAAyF,QAAA,GAAM,SAAE,EAAClE,QAAQ,EAAC,MAAC;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eAC1B,CACH,eAEDlG,OAAA;QAAKmF,KAAK,EAAE;UAAEuB,IAAI,EAAE;QAAE;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG/BlG,OAAA;QAAAyF,QAAA,EAAM;MAA2B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAGNlG,OAAA;MAAOuI,GAAG;MAAA9C,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAC9F,EAAA,CA/nBQD,GAAG;AAAAqI,EAAA,GAAHrI,GAAG;AAioBZ,eAAeA,GAAG;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}