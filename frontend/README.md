# 图像增强Web应用 - 前端界面

基于React的现代化图像增强前端应用，提供直观的用户界面和流畅的交互体验。

## 🚀 快速启动

### 方法一：使用项目根目录脚本（推荐）
```bash
# 在项目根目录执行（自动停止已运行的服务）
./run.sh

# 停止所有服务
./stop.sh
```

### 方法二：手动启动
```bash
# 1. 安装依赖（首次运行）
npm install

# 2. 启动开发服务器
npm start
```

### 方法三：生产环境构建
```bash
# 构建生产版本
npm run build

# 使用静态服务器运行
npx serve -s build
```

## 📋 环境要求

- Node.js 14.0+
- npm 6.0+ 或 yarn 1.22+
- 现代浏览器（Chrome 88+, Firefox 85+, Safari 14+）

## 🔧 依赖管理

### 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 主要依赖
- **React**: ^18.2.0 - 核心UI框架
- **React DOM**: ^18.2.0 - DOM渲染
- **React Scripts**: 5.0.1 - 构建工具链

## 🌐 访问地址

- **开发环境**: http://localhost:3000
- **生产环境**: 根据部署配置

## 🎨 功能特性

### 核心功能
- ✅ 图像文件上传（拖拽/点击选择）
- ✅ 文件类型验证（支持常见图像格式）
- ✅ 文件大小限制（最大10MB）
- ✅ 实时上传进度显示
- ✅ 原图与增强图对比展示
- ✅ 增强图像下载功能
- ✅ 一键重置功能

### 用户体验
- ✅ 响应式设计（适配手机/平板/桌面）
- ✅ 加载状态动画
- ✅ 友好的错误提示
- ✅ 直观的操作界面
- ✅ 无刷新页面交互

## 📁 项目结构

```
frontend/
├── public/                # 静态资源
│   ├── index.html        # HTML模板
│   ├── manifest.json     # PWA配置
│   └── favicon.ico       # 网站图标
├── src/                  # 源代码
│   ├── App.js           # 主应用组件
│   ├── UploadForm.js    # 文件上传组件
│   ├── ResultView.js    # 结果展示组件
│   └── index.js         # 应用入口
├── package.json         # 项目配置和依赖
└── README.md           # 本文件
```

## 🔧 开发指令

```bash
# 启动开发服务器
npm start

# 运行测试
npm test

# 构建生产版本
npm run build

# 代码检查
npm run lint

# 格式化代码
npm run format
```

## 🔗 API集成

### 后端服务配置
前端默认连接到本地后端服务：
- **API地址**: http://localhost:8001
- **上传接口**: POST /enhance/
- **结果接口**: GET /result/{filename}

### 修改API地址
如需修改后端地址，请编辑以下文件中的URL：
- `src/App.js` - 主要API调用
- `src/ResultView.js` - 结果图像URL

## 🎯 组件说明

### App.js - 主应用组件
- 管理应用状态（加载、错误、结果）
- 处理文件上传逻辑
- 协调各子组件交互

### UploadForm.js - 上传表单组件
- 文件选择和验证
- 拖拽上传支持
- 图像预览功能

### ResultView.js - 结果展示组件
- 原图与增强图对比
- 图像下载功能
- 新窗口查看功能

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3000
   # 或使用其他端口启动
   PORT=3001 npm start
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **构建失败**
   ```bash
   # 检查Node.js版本
   node --version
   # 更新到推荐版本
   ```

4. **API连接失败**
   - 确保后端服务正在运行（http://localhost:8001）
   - 检查CORS配置
   - 查看浏览器控制台错误信息

### 浏览器兼容性
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 🔄 开发模式

### 热重载
开发模式下，代码修改会自动触发页面刷新，无需手动重启服务。

### 调试技巧
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 使用Network面板监控API请求
4. 使用React Developer Tools调试组件状态

## 📱 移动端适配

应用已针对移动设备进行优化：
- 响应式布局设计
- 触摸友好的交互元素
- 适配不同屏幕尺寸
- 优化加载性能

## 📝 更新日志

- **v1.0.0** (2025-01-13): 初始版本
  - 基础图像上传和展示功能
  - 响应式设计
  - 完整的错误处理
  - 原图与增强图对比功能