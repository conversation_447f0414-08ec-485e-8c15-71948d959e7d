import React, { useState } from 'react';
import UploadForm from './UploadForm';
import ResultView from './ResultView';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);

  const handleUpload = async (formData) => {
    const startTime = Date.now();
    setIsLoading(true);
    setError(null);

    // 保存原始图像用于对比和获取图像信息
    const file = formData.get('file');
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target.result);
        // 获取图像信息
        const img = new Image();
        img.onload = () => {
          setImageInfo({
            name: file.name,
            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
            dimensions: `${img.width} × ${img.height}`,
            type: file.type
          });
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }

    try {
      const response = await fetch('http://localhost:8001/enhance/', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      const endTime = Date.now();
      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setError(null);
    setOriginalImage(null);
  };

  return (
    <div style={{ 
      height: '100vh',
      backgroundColor: '#2b2b2b',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 顶部菜单栏 */}
      <div style={{
        height: '60px',
        backgroundColor: '#3c3c3c',
        borderBottom: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        color: '#fff'
      }}>
        {/* macOS风格的窗口控制按钮 */}
        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>
        </div>
        
        {/* 应用标题 */}
        <div style={{ 
          flex: 1, 
          textAlign: 'center', 
          fontSize: '14px', 
          fontWeight: '500',
          color: '#e0e0e0'
        }}>
          图像增强工具
        </div>
        
        {/* 顶部菜单 */}
        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>文件</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>编辑</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>视图</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>工具</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>帮助</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 左侧主工作区 */}
        <div style={{
          flex: 1,
          backgroundColor: '#1e1e1e',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}>
          {/* 工具栏 */}
          <div style={{
            height: '50px',
            backgroundColor: '#333',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            padding: '0 15px',
            gap: '10px'
          }}>
            <button
              onClick={handleReset}
              style={{
                padding: '6px 12px',
                backgroundColor: '#4a90e2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              新建
            </button>
            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>
            <span style={{ color: '#ccc', fontSize: '12px' }}>
              {result ? '已处理' : '等待上传图像'}
            </span>
          </div>

          {/* 图像显示区域 */}
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px',
            position: 'relative'
          }}>
            {error && (
              <div style={{
                position: 'absolute',
                top: '20px',
                left: '20px',
                right: '20px',
                backgroundColor: '#d32f2f',
                color: 'white',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '14px',
                zIndex: 10
              }}>
                错误: {error}
              </div>
            )}

            {isLoading && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '20px',
                borderRadius: '8px',
                textAlign: 'center',
                zIndex: 10
              }}>
                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>
                <div style={{ 
                  width: '30px', 
                  height: '30px', 
                  border: '3px solid #333',
                  borderTop: '3px solid #4a90e2',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto'
                }}></div>
              </div>
            )}

            {result ? (
              <ResultView result={result} originalImage={originalImage} />
            ) : (
              <div style={{
                textAlign: 'center',
                color: '#888',
                fontSize: '16px'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>
                <div>请在右侧面板上传图像开始处理</div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧参数面板 */}
        <div style={{
          width: '320px',
          backgroundColor: '#2d2d2d',
          borderLeft: '1px solid #555',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板标题 */}
          <div style={{
            height: '50px',
            backgroundColor: '#333',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            padding: '0 15px',
            color: '#e0e0e0',
            fontSize: '14px',
            fontWeight: '500'
          }}>
            参数设置
          </div>

          {/* 参数内容 */}
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '0'
          }}>
            <UploadForm onUpload={handleUpload} isLoading={isLoading} />
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div style={{
        height: '30px',
        backgroundColor: '#333',
        borderTop: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 15px',
        color: '#ccc',
        fontSize: '12px'
      }}>
        <span>就绪</span>
        <div style={{ flex: 1 }}></div>
        <span>RealESRGAN v2.1</span>
      </div>

      {/* CSS动画 */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

export default App;

// 添加全局CSS样式
const globalStyles = `
  /* 自定义滑块样式 */
  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    background: #555;
    outline: none;
    border-radius: 2px;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    background: #4a90e2;
    cursor: pointer;
    border-radius: 50%;
    border: 2px solid #fff;
  }

  input[type="range"]::-moz-range-thumb {
    width: 14px;
    height: 14px;
    background: #4a90e2;
    cursor: pointer;
    border-radius: 50%;
    border: 2px solid #fff;
    border: none;
  }

  /* 自定义复选框样式 */
  input[type="checkbox"] {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: #3c3c3c;
    border: 1px solid #555;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
  }

  input[type="checkbox"]:checked {
    background: #4a90e2;
    border-color: #4a90e2;
  }

  input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #2d2d2d;
  }

  ::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #666;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = globalStyles;
  document.head.appendChild(styleElement);
}
