import React, { useState, useEffect, useRef } from 'react';
import UploadForm from './UploadForm';
import ResultView from './ResultView';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const [showMenu, setShowMenu] = useState(null);
  const [imageInfo, setImageInfo] = useState(null);
  const [processingTime, setProcessingTime] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(100);
  const fileInputRef = useRef(null);

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+O 打开文件
      if (e.ctrlKey && e.key === 'o') {
        e.preventDefault();
        if (fileInputRef.current) {
          fileInputRef.current.click();
        }
      }
      // Ctrl+R 重置
      if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        handleReset();
      }
      // Ctrl+S 保存（如果有结果）
      if (e.ctrlKey && e.key === 's' && result) {
        e.preventDefault();
        downloadImage();
      }
      // Ctrl+= 放大
      if (e.ctrlKey && e.key === '=') {
        e.preventDefault();
        setZoomLevel(prev => Math.min(prev + 25, 400));
      }
      // Ctrl+- 缩小
      if (e.ctrlKey && e.key === '-') {
        e.preventDefault();
        setZoomLevel(prev => Math.max(prev - 25, 25));
      }
      // Ctrl+0 重置缩放
      if (e.ctrlKey && e.key === '0') {
        e.preventDefault();
        setZoomLevel(100);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [result]);

  // 菜单功能
  const menuItems = {
    file: [
      { label: '打开图像 (Ctrl+O)', action: () => fileInputRef.current?.click() },
      { label: '保存结果 (Ctrl+S)', action: downloadImage, disabled: !result },
      { label: '重置 (Ctrl+R)', action: handleReset }
    ],
    edit: [
      { label: '撤销', action: () => {}, disabled: true },
      { label: '重做', action: () => {}, disabled: true }
    ],
    view: [
      { label: '放大 (Ctrl+=)', action: () => setZoomLevel(prev => Math.min(prev + 25, 400)) },
      { label: '缩小 (Ctrl+-)', action: () => setZoomLevel(prev => Math.max(prev - 25, 25)) },
      { label: '实际大小 (Ctrl+0)', action: () => setZoomLevel(100) }
    ],
    tools: [
      { label: '批量处理', action: () => {}, disabled: true },
      { label: '设置', action: () => {}, disabled: true }
    ],
    help: [
      { label: '快捷键', action: () => alert('快捷键:\nCtrl+O: 打开文件\nCtrl+S: 保存结果\nCtrl+R: 重置\nCtrl+=: 放大\nCtrl+-: 缩小\nCtrl+0: 实际大小') },
      { label: '关于', action: () => alert('AI图像增强工具 v2.1\n基于RealESRGAN技术') }
    ]
  };

  const handleUpload = async (formData) => {
    const startTime = Date.now();
    setIsLoading(true);
    setError(null);
    
    // 保存原始图像用于对比和获取图像信息
    const file = formData.get('file');
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target.result);
        // 获取图像信息
        const img = new Image();
        img.onload = () => {
          setImageInfo({
            name: file.name,
            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
            dimensions: `${img.width} × ${img.height}`,
            type: file.type
          });
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }

    try {
      const response = await fetch('http://localhost:8001/enhance/', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      const endTime = Date.now();
      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setError(null);
    setOriginalImage(null);
    setImageInfo(null);
    setProcessingTime(null);
    setZoomLevel(100);
  };

  const downloadImage = () => {
    if (result) {
      const link = document.createElement('a');
      link.href = `http://localhost:8001/result/${result.enhanced_url}`;
      link.download = `enhanced_${result.filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleMenuClick = (menuKey) => {
    setShowMenu(showMenu === menuKey ? null : menuKey);
  };

  const handleMenuItemClick = (action) => {
    action();
    setShowMenu(null);
  };

  return (
    <div style={{ 
      height: '100vh',
      backgroundColor: '#2b2b2b',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files[0];
          if (file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('params', JSON.stringify({
              scale: 4,
              use_realesrgan: true,
              sharpening: 0.0,
              denoising: 0,
              saturation: 1.0,
              contrast: 1.0,
              brightness: 0,
              beauty: 0.0
            }));
            handleUpload(formData);
          }
        }}
      />

      {/* 顶部菜单栏 */}
      <div style={{
        height: '60px',
        backgroundColor: '#3c3c3c',
        borderBottom: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        color: '#fff',
        position: 'relative'
      }}>
        {/* macOS风格的窗口控制按钮 */}
        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>
        </div>
        
        {/* 应用标题 */}
        <div style={{ 
          flex: 1, 
          textAlign: 'center', 
          fontSize: '14px', 
          fontWeight: '500',
          color: '#e0e0e0'
        }}>
          图像增强工具
        </div>
        
        {/* 顶部菜单 */}
        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>
          {Object.keys(menuItems).map(menuKey => (
            <div key={menuKey} style={{ position: 'relative' }}>
              <span 
                style={{ 
                  cursor: 'pointer', 
                  padding: '5px 10px', 
                  borderRadius: '4px',
                  backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'
                }}
                onClick={() => handleMenuClick(menuKey)}
              >
                {menuKey === 'file' ? '文件' : 
                 menuKey === 'edit' ? '编辑' :
                 menuKey === 'view' ? '视图' :
                 menuKey === 'tools' ? '工具' : '帮助'}
              </span>
              
              {/* 下拉菜单 */}
              {showMenu === menuKey && (
                <div style={{
                  position: 'absolute',
                  top: '100%',
                  left: 0,
                  backgroundColor: '#3c3c3c',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  minWidth: '150px',
                  zIndex: 1000,
                  boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
                }}>
                  {menuItems[menuKey].map((item, index) => (
                    <div
                      key={index}
                      style={{
                        padding: '8px 12px',
                        cursor: item.disabled ? 'not-allowed' : 'pointer',
                        color: item.disabled ? '#888' : '#e0e0e0',
                        fontSize: '12px',
                        borderBottom: index < menuItems[menuKey].length - 1 ? '1px solid #555' : 'none'
                      }}
                      onClick={() => !item.disabled && handleMenuItemClick(item.action)}
                      onMouseEnter={(e) => {
                        if (!item.disabled) {
                          e.target.style.backgroundColor = '#4a90e2';
                        }
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = 'transparent';
                      }}
                    >
                      {item.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 主内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 左侧主工作区 */}
        <div style={{
          flex: 1,
          backgroundColor: '#1e1e1e',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}>
          {/* 工具栏 */}
          <div style={{
            height: '50px',
            backgroundColor: '#333',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            padding: '0 15px',
            gap: '10px'
          }}>
            <button
              onClick={handleReset}
              style={{
                padding: '6px 12px',
                backgroundColor: '#4a90e2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              新建
            </button>
            
            <button
              onClick={() => fileInputRef.current?.click()}
              style={{
                padding: '6px 12px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              打开
            </button>
            
            {result && (
              <button
                onClick={downloadImage}
                style={{
                  padding: '6px 12px',
                  backgroundColor: '#17a2b8',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                保存
              </button>
            )}
            
            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>
            
            {/* 缩放控制 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <button
                onClick={() => setZoomLevel(prev => Math.max(prev - 25, 25))}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  fontSize: '11px'
                }}
              >
                -
              </button>
              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '40px', textAlign: 'center' }}>
                {zoomLevel}%
              </span>
              <button
                onClick={() => setZoomLevel(prev => Math.min(prev + 25, 400))}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  fontSize: '11px'
                }}
              >
                +
              </button>
            </div>
            
            <div style={{ flex: 1 }}></div>
            
            <span style={{ color: '#ccc', fontSize: '12px' }}>
              {result ? '已处理' : isLoading ? '处理中...' : '等待上传图像'}
            </span>
          </div>

          {/* 图像显示区域 */}
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px',
            position: 'relative'
          }}>
            {error && (
              <div style={{
                position: 'absolute',
                top: '20px',
                left: '20px',
                right: '20px',
                backgroundColor: '#d32f2f',
                color: 'white',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '14px',
                zIndex: 10
              }}>
                错误: {error}
              </div>
            )}

            {isLoading && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '20px',
                borderRadius: '8px',
                textAlign: 'center',
                zIndex: 10
              }}>
                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>
                <div style={{ 
                  width: '30px', 
                  height: '30px', 
                  border: '3px solid #333',
                  borderTop: '3px solid #4a90e2',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto'
                }}></div>
              </div>
            )}

            {result ? (
              <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'center' }}>
                <ResultView result={result} originalImage={originalImage} />
              </div>
            ) : (
              <div style={{
                textAlign: 'center',
                color: '#888',
                fontSize: '16px'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>
                <div>请在右侧面板上传图像开始处理</div>
                <div style={{ fontSize: '14px', marginTop: '10px', color: '#666' }}>
                  或按 Ctrl+O 打开文件
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧参数面板 */}
        <div style={{
          width: '320px',
          backgroundColor: '#2d2d2d',
          borderLeft: '1px solid #555',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板标题 */}
          <div style={{
            height: '50px',
            backgroundColor: '#333',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            padding: '0 15px',
            color: '#e0e0e0',
            fontSize: '14px',
            fontWeight: '500'
          }}>
            参数设置
          </div>

          {/* 参数内容 */}
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '0'
          }}>
            <UploadForm onUpload={handleUpload} isLoading={isLoading} />
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div style={{
        height: '30px',
        backgroundColor: '#333',
        borderTop: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 15px',
        color: '#ccc',
        fontSize: '12px'
      }}>
        <span>{isLoading ? '处理中...' : '就绪'}</span>
        
        {imageInfo && (
          <>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>{imageInfo.name}</span>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>{imageInfo.dimensions}</span>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>{imageInfo.size}</span>
          </>
        )}
        
        {processingTime && (
          <>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>处理时间: {processingTime}s</span>
          </>
        )}
        
        <div style={{ flex: 1 }}></div>
        <span>RealESRGAN v2.1</span>
      </div>

      {/* CSS样式 */}
      <style jsx global>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

export default App;
