import React from 'react';

function App() {
  return React.createElement('div', {
    style: {
      height: '100vh',
      backgroundColor: '#2b2b2b',
      color: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '24px',
      fontFamily: 'Arial, sans-serif'
    }
  }, React.createElement('div', null, 
    React.createElement('h1', null, 'AI图像增强工具'),
    React.createElement('p', null, '页面正在加载...'),
    React.createElement('p', { style: { fontSize: '16px', color: '#ccc' } }, 
      '时间: ' + new Date().toLocaleTimeString()
    )
  ));
}

export default App;
