import React, { useState } from 'react';
import UploadForm from './UploadForm';
import ResultView from './ResultView';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);

  const handleUpload = async (formData) => {
    setIsLoading(true);
    setError(null);
    
    // 保存原始图像用于对比
    const file = formData.get('file');
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setOriginalImage(e.target.result);
      reader.readAsDataURL(file);
    }

    try {
      const response = await fetch('http://localhost:8001/enhance/', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setError(null);
    setOriginalImage(null);
  };

  return (
    <div style={{
      height: '100vh',
      backgroundColor: '#2b2b2b',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 顶部菜单栏 */}
      <div style={{
        height: '60px',
        backgroundColor: '#3c3c3c',
        borderBottom: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        color: '#fff'
      }}>
        {/* macOS风格的窗口控制按钮 */}
        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>
        </div>

        {/* 应用标题 */}
        <div style={{
          flex: 1,
          textAlign: 'center',
          fontSize: '14px',
          fontWeight: '500',
          color: '#e0e0e0'
        }}>
          图像增强工具
        </div>

        {/* 顶部菜单 */}
        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>文件</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>编辑</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>视图</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>工具</span>
          <span style={{ cursor: 'pointer', padding: '5px 10px', borderRadius: '4px' }}>帮助</span>
        </div>
      </div>

        {error && (
          <div style={{ 
            backgroundColor: '#f8d7da', 
            color: '#721c24', 
            padding: '15px', 
            borderRadius: '5px', 
            marginBottom: '20px',
            border: '1px solid #f5c6cb'
          }}>
            <strong>错误:</strong> {error}
            <button 
              onClick={handleReset}
              style={{
                marginLeft: '10px',
                padding: '5px 10px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer'
              }}
            >
              重试
            </button>
          </div>
        )}

        {isLoading && (
          <div style={{ 
            backgroundColor: '#d1ecf1', 
            color: '#0c5460', 
            padding: '15px', 
            borderRadius: '5px', 
            marginBottom: '20px',
            border: '1px solid #bee5eb',
            textAlign: 'center'
          }}>
            <div style={{ marginBottom: '10px' }}>
              正在处理图像，请稍候...
            </div>
          </div>
        )}

        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: result ? '1fr 1fr' : '1fr',
          gap: '20px',
          alignItems: 'start'
        }}>
          <div>
            <UploadForm onUpload={handleUpload} isLoading={isLoading} />
            
            {result && (
              <div style={{ textAlign: 'center', marginTop: '20px' }}>
                <button 
                  onClick={handleReset}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  处理新图像
                </button>
              </div>
            )}
          </div>
          
          {result && (
            <div>
              <ResultView result={result} originalImage={originalImage} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
