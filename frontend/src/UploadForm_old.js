import React, { useState, useEffect } from 'react';

const UploadForm = ({ onUpload, isLoading }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [error, setError] = useState(null);
  const [presets, setPresets] = useState({});
  const [selectedPreset, setSelectedPreset] = useState('default');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [params, setParams] = useState({
    scale: 4,
    use_realesrgan: true,
    sharpening: 0.0,
    denoising: 0,
    saturation: 1.0,
    contrast: 1.0,
    brightness: 0,
    beauty: 0.0
  });

  // 获取预设配置
  useEffect(() => {
    fetch('http://localhost:8001/presets/')
      .then(res => res.json())
      .then(data => {
        setPresets(data.presets);
        if (data.presets.default) {
          setParams(data.presets.default.params);
        }
      })
      .catch(err => console.error('获取预设配置失败:', err));
  }, []);

  // 处理预设选择
  const handlePresetChange = (presetKey) => {
    setSelectedPreset(presetKey);
    if (presets[presetKey]) {
      setParams(presets[presetKey].params);
    }
  };

  // 处理参数变化
  const handleParamChange = (key, value) => {
    setParams(prev => ({
      ...prev,
      [key]: value
    }));
    setSelectedPreset('custom'); // 切换到自定义模式
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setError(null);

    if (file) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        setError('请选择图像文件');
        return;
      }

      // 验证文件大小 (限制为10MB)
      if (file.size > 10 * 1024 * 1024) {
        setError('文件大小不能超过10MB');
        return;
      }

      setSelectedFile(file);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => setPreviewUrl(e.target.result);
      reader.readAsDataURL(file);
    } else {
      setSelectedFile(null);
      setPreviewUrl(null);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!selectedFile) {
      setError('请选择要增强的图像');
      return;
    }

    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("params", JSON.stringify(params));
    onUpload(formData);
  };

  return (
    <div style={{ marginBottom: '20px' }}>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="file-input" style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            选择图像文件:
          </label>
          <input
            id="file-input"
            type="file"
            onChange={handleFileChange}
            accept="image/*"
            required
            disabled={isLoading}
            style={{ marginBottom: '10px' }}
          />
        </div>

        {error && (
          <div style={{ color: 'red', marginBottom: '10px', padding: '5px', border: '1px solid red', borderRadius: '3px' }}>
            {error}
          </div>
        )}

        {previewUrl && (
          <div style={{ marginBottom: '15px' }}>
            <p style={{ fontWeight: 'bold' }}>原始图像预览:</p>
            <img
              src={previewUrl}
              alt="原始图像"
              style={{ maxWidth: '300px', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '5px' }}
            />
          </div>
        )}

        {/* 参数配置面板 */}
        <div style={{ marginBottom: '15px', padding: '12px', border: '1px solid #ddd', borderRadius: '5px', backgroundColor: '#f9f9f9' }}>
          <h3 style={{ marginTop: 0, marginBottom: '12px', fontSize: '16px' }}>增强设置</h3>

          {/* 预设选择和高级设置 */}
          <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '10px', marginBottom: '12px' }}>
            <div>
              <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '14px' }}>
                预设配置:
              </label>
              <select
                value={selectedPreset}
                onChange={(e) => handlePresetChange(e.target.value)}
                style={{ width: '100%', padding: '6px', borderRadius: '3px', border: '1px solid #ccc', fontSize: '14px' }}
              >
                <option value="default">默认设置</option>
                <option value="portrait">人像优化</option>
                <option value="landscape">风景增强</option>
                <option value="vintage">复古风格</option>
                <option value="fast">快速处理</option>
                <option value="custom">自定义</option>
              </select>
            </div>

            <div style={{ display: 'flex', alignItems: 'end' }}>
              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', fontSize: '14px' }}>
                <input
                  type="checkbox"
                  checked={showAdvanced}
                  onChange={(e) => setShowAdvanced(e.target.checked)}
                  style={{ marginRight: '6px' }}
                />
                高级设置
              </label>
            </div>
          </div>

          {/* 基础设置 */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '10px' }}>
            {/* 超分倍数 */}
            <div>
              <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '14px' }}>
                超分倍数: {params.scale}x
              </label>
              <input
                type="range"
                min="2"
                max="4"
                step="2"
                value={params.scale}
                onChange={(e) => handleParamChange('scale', parseInt(e.target.value))}
                style={{ width: '100%' }}
              />
            </div>

            {/* 使用RealESRGAN */}
            <div style={{ display: 'flex', alignItems: 'end', paddingBottom: '8px' }}>
              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', fontSize: '14px' }}>
                <input
                  type="checkbox"
                  checked={params.use_realesrgan}
                  onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}
                  style={{ marginRight: '6px' }}
                />
                RealESRGAN模型
              </label>
            </div>
          </div>

          {/* 高级设置 */}
          {showAdvanced && (
            <div style={{ marginTop: '12px', paddingTop: '12px', borderTop: '1px solid #ddd' }}>
              <h4 style={{ marginTop: 0, marginBottom: '10px', fontSize: '14px' }}>高级参数</h4>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>

                {/* 锐化强度 */}
                <div>
                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>
                    锐化: {(params.sharpening * 100).toFixed(0)}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={params.sharpening}
                    onChange={(e) => handleParamChange('sharpening', parseFloat(e.target.value))}
                    style={{ width: '100%' }}
                  />
                </div>

                {/* 降噪强度 */}
                <div>
                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>
                    降噪: {params.denoising}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="30"
                    step="1"
                    value={params.denoising}
                    onChange={(e) => handleParamChange('denoising', parseInt(e.target.value))}
                    style={{ width: '100%' }}
                  />
                </div>

                {/* 饱和度 */}
                <div>
                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>
                    饱和度: {params.saturation.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={params.saturation}
                    onChange={(e) => handleParamChange('saturation', parseFloat(e.target.value))}
                    style={{ width: '100%' }}
                  />
                </div>

                {/* 对比度 */}
                <div>
                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>
                    对比度: {params.contrast.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.05"
                    value={params.contrast}
                    onChange={(e) => handleParamChange('contrast', parseFloat(e.target.value))}
                    style={{ width: '100%' }}
                  />
                </div>

                {/* 亮度 */}
                <div>
                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>
                    亮度: {params.brightness > 0 ? '+' : ''}{params.brightness}
                  </label>
                  <input
                    type="range"
                    min="-100"
                    max="100"
                    step="5"
                    value={params.brightness}
                    onChange={(e) => handleParamChange('brightness', parseInt(e.target.value))}
                    style={{ width: '100%' }}
                  />
                </div>

                {/* 美颜强度 */}
                <div>
                  <label style={{ display: 'block', marginBottom: '3px', fontWeight: 'bold', fontSize: '13px' }}>
                    美颜: {(params.beauty * 100).toFixed(0)}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={params.beauty}
                    onChange={(e) => handleParamChange('beauty', parseFloat(e.target.value))}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        <button
          type="submit"
          disabled={!selectedFile || isLoading}
          style={{
            padding: '12px 24px',
            backgroundColor: isLoading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            width: '100%'
          }}
        >
          {isLoading ? '正在增强...' : '开始增强图像'}
        </button>
      </form>
    </div>
  );
};

export default UploadForm;