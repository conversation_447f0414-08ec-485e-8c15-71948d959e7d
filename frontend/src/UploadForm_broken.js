import React, { useState, useEffect } from 'react';

const UploadForm = ({ onUpload, isLoading }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [error, setError] = useState(null);
  const [presets, setPresets] = useState({});
  const [selectedPreset, setSelectedPreset] = useState('default');
  const [isDragOver, setIsDragOver] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [params, setParams] = useState({
    scale: 4,
    use_realesrgan: true,
    sharpening: 0.0,
    denoising: 0,
    saturation: 1.0,
    contrast: 1.0,
    brightness: 0,
    beauty: 0.0
  });

  // 获取预设配置
  useEffect(() => {
    fetch('http://localhost:8001/presets/')
      .then(res => res.json())
      .then(data => {
        setPresets(data.presets);
        if (data.presets.default) {
          setParams(data.presets.default.params);
        }
      })
      .catch(err => console.error('获取预设配置失败:', err));
  }, []);

  // 处理预设选择
  const handlePresetChange = (presetKey) => {
    setSelectedPreset(presetKey);
    if (presets[presetKey]) {
      setParams(presets[presetKey].params);
    }
  };

  // 处理参数变化
  const handleParamChange = (key, value) => {
    setParams(prev => ({
      ...prev,
      [key]: value
    }));
    setSelectedPreset('custom');
  };

  const validateAndSetFile = (file) => {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      setError('请选择图像文件');
      return false;
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('文件大小不能超过10MB');
      return false;
    }

    setSelectedFile(file);
    setError(null);

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => setPreviewUrl(e.target.result);
    reader.readAsDataURL(file);
    return true;
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      validateAndSetFile(file);
    } else {
      setSelectedFile(null);
      setPreviewUrl(null);
    }
  };

  // 拖拽处理
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetFile(files[0]);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('请选择要增强的图像');
      return;
    }
    
    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("params", JSON.stringify(params));
    onUpload(formData);
  };

  return (
    <div style={{ 
      height: '100%',
      backgroundColor: '#2d2d2d',
      color: '#e0e0e0',
      fontSize: '13px'
    }}>
      <form onSubmit={handleSubmit} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        
        {/* 文件上传区域 */}
        <div style={{
          padding: '15px',
          borderBottom: '1px solid #444'
        }}>
          <div style={{
            marginBottom: '10px',
            fontWeight: '500',
            color: '#fff'
          }}>
            图像文件
          </div>

          {/* 拖拽上传区域 */}
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            style={{
              border: `2px dashed ${isDragOver ? '#4a90e2' : '#555'}`,
              borderRadius: '8px',
              padding: '20px',
              textAlign: 'center',
              backgroundColor: isDragOver ? 'rgba(74, 144, 226, 0.1)' : '#3c3c3c',
              transition: 'all 0.3s ease',
              marginBottom: '10px',
              cursor: 'pointer'
            }}
            onClick={() => document.getElementById('file-input').click()}
          >
            <div style={{ fontSize: '24px', marginBottom: '8px' }}>
              {isDragOver ? '📁' : '📷'}
            </div>
            <div style={{ color: '#e0e0e0', fontSize: '12px', marginBottom: '4px' }}>
              {isDragOver ? '释放文件以上传' : '点击选择图像或拖拽到此处'}
            </div>
            <div style={{ color: '#888', fontSize: '10px' }}>
              支持 JPG, PNG, GIF 等格式，最大 10MB
            </div>
          </div>

          <input
            id="file-input"
            type="file"
            onChange={handleFileChange}
            accept="image/*"
            required
            disabled={isLoading}
            style={{ display: 'none' }}
          />

          {previewUrl && (
            <div style={{ marginTop: '10px' }}>
              <img
                src={previewUrl}
                alt="预览"
                style={{
                  width: '100%',
                  maxHeight: '120px',
                  objectFit: 'contain',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  backgroundColor: '#1e1e1e'
                }}
              />
            </div>
          )}

          {error && (
            <div style={{
              marginTop: '8px',
              padding: '6px',
              backgroundColor: '#d32f2f',
              color: 'white',
              borderRadius: '3px',
              fontSize: '11px'
            }}>
              {error}
            </div>
          )}
        </div>

        {/* 预设配置 */}
        <div style={{ 
          padding: '15px',
          borderBottom: '1px solid #444'
        }}>
          <div style={{ 
            marginBottom: '8px', 
            fontWeight: '500',
            color: '#fff'
          }}>
            预设配置
          </div>
          
          <select 
            value={selectedPreset}
            onChange={(e) => handlePresetChange(e.target.value)}
            style={{ 
              width: '100%', 
              padding: '6px', 
              backgroundColor: '#3c3c3c',
              border: '1px solid #555',
              borderRadius: '4px',
              color: '#e0e0e0',
              fontSize: '12px'
            }}
          >
            <option value="default">默认设置</option>
            <option value="portrait">人像优化</option>
            <option value="landscape">风景增强</option>
            <option value="vintage">复古风格</option>
            <option value="fast">快速处理</option>
            <option value="custom">自定义</option>
          </select>
        </div>

        {/* 基础参数 */}
        <div style={{ 
          padding: '15px',
          borderBottom: '1px solid #444'
        }}>
          <div style={{ 
            marginBottom: '12px', 
            fontWeight: '500',
            color: '#fff'
          }}>
            基础设置
          </div>

          {/* 超分倍数 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>超分倍数</span>
              <span>{params.scale}x</span>
            </div>
            <input 
              type="range"
              min="2"
              max="4"
              step="2"
              value={params.scale}
              onChange={(e) => handleParamChange('scale', parseInt(e.target.value))}
              style={{ 
                width: '100%',
                height: '4px',
                backgroundColor: '#555',
                outline: 'none',
                borderRadius: '2px'
              }}
            />
          </div>

          {/* RealESRGAN开关 */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            marginBottom: '8px'
          }}>
            <span style={{ fontSize: '12px' }}>RealESRGAN模型</span>
            <input 
              type="checkbox"
              checked={params.use_realesrgan}
              onChange={(e) => handleParamChange('use_realesrgan', e.target.checked)}
              style={{ 
                width: '16px',
                height: '16px'
              }}
            />
          </div>
        </div>

        {/* 高级参数 */}
        <div style={{
          flex: 1,
          overflow: 'auto'
        }}>
          <div style={{
            padding: '15px 15px 0 15px',
            borderBottom: showAdvanced ? 'none' : '1px solid #444'
          }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '12px',
                fontWeight: '500',
                color: '#fff',
                cursor: 'pointer'
              }}
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              <span>高级参数</span>
              <span style={{
                fontSize: '12px',
                transform: showAdvanced ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.3s ease'
              }}>
                ▼
              </span>
            </div>
          </div>

          {showAdvanced && (
            <div style={{ padding: '0 15px 15px 15px' }}>

          {/* 锐化 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>锐化</span>
              <span>{(params.sharpening * 100).toFixed(0)}%</span>
            </div>
            <input 
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={params.sharpening}
              onChange={(e) => handleParamChange('sharpening', parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          {/* 降噪 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>降噪</span>
              <span>{params.denoising}</span>
            </div>
            <input 
              type="range"
              min="0"
              max="30"
              step="1"
              value={params.denoising}
              onChange={(e) => handleParamChange('denoising', parseInt(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          {/* 饱和度 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>饱和度</span>
              <span>{params.saturation.toFixed(1)}</span>
            </div>
            <input 
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={params.saturation}
              onChange={(e) => handleParamChange('saturation', parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          {/* 对比度 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>对比度</span>
              <span>{params.contrast.toFixed(1)}</span>
            </div>
            <input 
              type="range"
              min="0"
              max="2"
              step="0.05"
              value={params.contrast}
              onChange={(e) => handleParamChange('contrast', parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          {/* 亮度 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>亮度</span>
              <span>{params.brightness > 0 ? '+' : ''}{params.brightness}</span>
            </div>
            <input 
              type="range"
              min="-100"
              max="100"
              step="5"
              value={params.brightness}
              onChange={(e) => handleParamChange('brightness', parseInt(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          {/* 美颜 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '4px',
              fontSize: '12px'
            }}>
              <span>美颜</span>
              <span>{(params.beauty * 100).toFixed(0)}%</span>
            </div>
            <input 
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={params.beauty}
              onChange={(e) => handleParamChange('beauty', parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
            )}
        </div>

        {/* 底部按钮 */}
        <div style={{ 
          padding: '15px',
          borderTop: '1px solid #444'
        }}>
          <button 
            type="submit" 
            disabled={!selectedFile || isLoading}
            style={{
              width: '100%',
              padding: '10px',
              backgroundColor: !selectedFile || isLoading ? '#555' : '#4a90e2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: !selectedFile || isLoading ? 'not-allowed' : 'pointer',
              fontSize: '13px',
              fontWeight: '500'
            }}
          >
            {isLoading ? '处理中...' : '开始增强'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UploadForm;
