import React, { useState, useEffect, useRef } from 'react';
import UploadForm from './UploadForm';
import ResultView from './ResultView';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const [showMenu, setShowMenu] = useState(null);
  const [imageInfo, setImageInfo] = useState(null);
  const [processingTime, setProcessingTime] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [viewMode, setViewMode] = useState('split');
  const [rotation, setRotation] = useState(0);
  const fileInputRef = useRef(null);

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === 'o') {
        e.preventDefault();
        if (fileInputRef.current) {
          fileInputRef.current.click();
        }
      }
      if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        handleReset();
      }
      if (e.ctrlKey && e.key === 's' && result) {
        e.preventDefault();
        downloadImage();
      }
      if (e.ctrlKey && e.key === '=') {
        e.preventDefault();
        setZoomLevel(prev => Math.min(prev + 25, 400));
      }
      if (e.ctrlKey && e.key === '-') {
        e.preventDefault();
        setZoomLevel(prev => Math.max(prev - 25, 25));
      }
      if (e.ctrlKey && e.key === '0') {
        e.preventDefault();
        setZoomLevel(100);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [result]);

  const handleUpload = async (formData) => {
    const startTime = Date.now();
    setIsLoading(true);
    setError(null);
    
    const file = formData.get('file');
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target.result);
        const img = new Image();
        img.onload = () => {
          setImageInfo({
            name: file.name,
            size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
            dimensions: `${img.width} × ${img.height}`,
            type: file.type
          });
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }

    try {
      const response = await fetch('http://localhost:8001/enhance/', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      const endTime = Date.now();
      setProcessingTime(((endTime - startTime) / 1000).toFixed(1));
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadImage = () => {
    if (result && result.enhanced_image) {
      const link = document.createElement('a');
      link.href = `data:image/png;base64,${result.enhanced_image}`;
      link.download = `enhanced_${Date.now()}.png`;
      link.click();
    }
  };

  const handleReset = () => {
    setResult(null);
    setError(null);
    setOriginalImage(null);
    setImageInfo(null);
    setProcessingTime(null);
    setZoomLevel(100);
    setViewMode('split');
    setRotation(0);
  };

  return (
    <div style={{ 
      height: '100vh',
      backgroundColor: '#2b2b2b',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files[0];
          if (file) {
            console.log('选择了文件:', file.name);
          }
        }}
      />

      {/* 顶部菜单栏 */}
      <div style={{
        height: '60px',
        backgroundColor: '#3c3c3c',
        borderBottom: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        color: '#fff'
      }}>
        {/* macOS风格的窗口控制按钮 */}
        <div style={{ display: 'flex', gap: '8px', marginRight: '20px' }}>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }}></div>
          <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }}></div>
        </div>
        
        {/* 应用标题 */}
        <div style={{ 
          flex: 1, 
          textAlign: 'center', 
          fontSize: '14px', 
          fontWeight: '500',
          color: '#e0e0e0'
        }}>
          图像增强工具
        </div>
        
        {/* 顶部菜单 */}
        <div style={{ display: 'flex', gap: '20px', fontSize: '13px' }}>
          {['file', 'edit', 'view', 'tools', 'help'].map(menuKey => (
            <div key={menuKey} style={{ position: 'relative' }}>
              <span 
                style={{ 
                  cursor: 'pointer', 
                  padding: '5px 10px', 
                  borderRadius: '4px',
                  backgroundColor: showMenu === menuKey ? '#4a90e2' : 'transparent'
                }}
                onClick={() => setShowMenu(showMenu === menuKey ? null : menuKey)}
              >
                {menuKey === 'file' ? '文件' : 
                 menuKey === 'edit' ? '编辑' :
                 menuKey === 'view' ? '视图' :
                 menuKey === 'tools' ? '工具' : '帮助'}
              </span>
              
              {/* 下拉菜单 */}
              {showMenu === menuKey && (
                <div style={{
                  position: 'absolute',
                  top: '100%',
                  left: 0,
                  backgroundColor: '#3c3c3c',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  minWidth: '180px',
                  zIndex: 1000,
                  boxShadow: '0 4px 8px rgba(0,0,0,0.3)',
                  padding: '8px 0'
                }}>
                  {menuKey === 'file' && (
                    <>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { fileInputRef.current?.click(); setShowMenu(null); }}
                      >
                        📁 打开图像 (Ctrl+O)
                      </div>
                      <div 
                        style={{ 
                          padding: '8px 12px', 
                          color: result ? '#e0e0e0' : '#666', 
                          fontSize: '12px', 
                          cursor: result ? 'pointer' : 'not-allowed' 
                        }}
                        onClick={() => { if(result) { downloadImage(); setShowMenu(null); } }}
                      >
                        💾 保存结果 (Ctrl+S)
                      </div>
                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { handleReset(); setShowMenu(null); }}
                      >
                        🔄 重置 (Ctrl+R)
                      </div>
                    </>
                  )}
                  
                  {menuKey === 'view' && (
                    <>
                      <div 
                        style={{ 
                          padding: '8px 12px', 
                          color: '#e0e0e0', 
                          fontSize: '12px', 
                          cursor: 'pointer',
                          backgroundColor: viewMode === 'split' ? '#4a90e2' : 'transparent'
                        }}
                        onClick={() => { setViewMode('split'); setShowMenu(null); }}
                      >
                        🔀 分屏对比
                      </div>
                      <div 
                        style={{ 
                          padding: '8px 12px', 
                          color: '#e0e0e0', 
                          fontSize: '12px', 
                          cursor: 'pointer',
                          backgroundColor: viewMode === 'original' ? '#4a90e2' : 'transparent'
                        }}
                        onClick={() => { setViewMode('original'); setShowMenu(null); }}
                      >
                        🖼️ 原图
                      </div>
                      <div 
                        style={{ 
                          padding: '8px 12px', 
                          color: '#e0e0e0', 
                          fontSize: '12px', 
                          cursor: 'pointer',
                          backgroundColor: viewMode === 'enhanced' ? '#4a90e2' : 'transparent'
                        }}
                        onClick={() => { setViewMode('enhanced'); setShowMenu(null); }}
                      >
                        ✨ 增强图
                      </div>
                      <div style={{ height: '1px', backgroundColor: '#555', margin: '4px 0' }}></div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { setZoomLevel(prev => Math.min(prev + 25, 400)); setShowMenu(null); }}
                      >
                        🔍 放大 (Ctrl+=)
                      </div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { setZoomLevel(prev => Math.max(prev - 25, 25)); setShowMenu(null); }}
                      >
                        🔍 缩小 (Ctrl+-)
                      </div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { setZoomLevel(100); setShowMenu(null); }}
                      >
                        📐 实际大小 (Ctrl+0)
                      </div>
                    </>
                  )}
                  
                  {menuKey === 'tools' && (
                    <>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { setRotation(prev => (prev + 90) % 360); setShowMenu(null); }}
                      >
                        ↻ 顺时针旋转
                      </div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { setRotation(prev => (prev - 90 + 360) % 360); setShowMenu(null); }}
                      >
                        ↺ 逆时针旋转
                      </div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { setRotation(0); setShowMenu(null); }}
                      >
                        🔄 重置旋转
                      </div>
                    </>
                  )}
                  
                  {menuKey === 'help' && (
                    <>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { 
                          alert(`快捷键说明:\n\n文件操作:\n• Ctrl+O: 打开图像\n• Ctrl+S: 保存结果\n• Ctrl+R: 重置\n\n视图控制:\n• Ctrl+=: 放大\n• Ctrl+-: 缩小\n• Ctrl+0: 实际大小`); 
                          setShowMenu(null); 
                        }}
                      >
                        ⌨️ 快捷键
                      </div>
                      <div 
                        style={{ padding: '8px 12px', color: '#e0e0e0', fontSize: '12px', cursor: 'pointer' }}
                        onClick={() => { 
                          alert('AI图像增强工具 v2.1\n基于RealESRGAN技术\n支持多种图像增强功能'); 
                          setShowMenu(null); 
                        }}
                      >
                        ℹ️ 关于
                      </div>
                    </>
                  )}
                  
                  {menuKey === 'edit' && (
                    <>
                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>
                        ↶ 撤销 (开发中)
                      </div>
                      <div style={{ padding: '8px 12px', color: '#666', fontSize: '12px' }}>
                        ↷ 重做 (开发中)
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 主内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 左侧主工作区 */}
        <div style={{
          flex: 1,
          backgroundColor: '#1e1e1e',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}>
          {/* 工具栏 */}
          <div style={{
            height: '50px',
            backgroundColor: '#333',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            padding: '0 15px',
            gap: '10px'
          }}>
            {/* 文件操作 */}
            <button
              onClick={handleReset}
              style={{
                padding: '6px 12px',
                backgroundColor: '#4a90e2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              🆕 新建
            </button>
            
            <button
              onClick={() => fileInputRef.current?.click()}
              style={{
                padding: '6px 12px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              📁 打开
            </button>
            
            <button
              onClick={() => result && downloadImage()}
              disabled={!result}
              style={{
                padding: '6px 12px',
                backgroundColor: result ? '#17a2b8' : '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: result ? 'pointer' : 'not-allowed',
                fontSize: '12px'
              }}
            >
              💾 保存
            </button>
            
            <div style={{ width: '1px', height: '20px', backgroundColor: '#555' }}></div>
            
            {/* 缩放控制 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <button
                onClick={() => setZoomLevel(prev => Math.max(prev - 25, 25))}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  fontSize: '11px'
                }}
              >
                🔍-
              </button>
              <span style={{ color: '#ccc', fontSize: '11px', minWidth: '50px', textAlign: 'center' }}>
                {zoomLevel}%
              </span>
              <button
                onClick={() => setZoomLevel(prev => Math.min(prev + 25, 400))}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  fontSize: '11px'
                }}
              >
                🔍+
              </button>
            </div>
            
            <div style={{ flex: 1 }}></div>
            
            {/* 状态信息 */}
            <span style={{ color: '#ccc', fontSize: '12px' }}>
              {isLoading ? '处理中...' : result ? '已处理' : '等待上传图像'}
            </span>
          </div>

          {/* 图像显示区域 */}
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px',
            position: 'relative'
          }}>
            {error && (
              <div style={{
                position: 'absolute',
                top: '20px',
                left: '20px',
                right: '20px',
                backgroundColor: '#d32f2f',
                color: 'white',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '14px',
                zIndex: 10
              }}>
                错误: {error}
              </div>
            )}

            {isLoading && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '20px',
                borderRadius: '8px',
                textAlign: 'center',
                zIndex: 10
              }}>
                <div style={{ marginBottom: '10px' }}>正在处理图像...</div>
                <div style={{ 
                  width: '30px', 
                  height: '30px', 
                  border: '3px solid #333',
                  borderTop: '3px solid #4a90e2',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto'
                }}></div>
              </div>
            )}

            {result ? (
              <div style={{ transform: `scale(${zoomLevel / 100}) rotate(${rotation}deg)`, transformOrigin: 'center' }}>
                <ResultView result={result} originalImage={originalImage} />
              </div>
            ) : (
              <div style={{
                textAlign: 'center',
                color: '#888',
                fontSize: '16px'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>📷</div>
                <div>请在右侧面板上传图像开始处理</div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧参数面板 */}
        <div style={{
          width: '320px',
          backgroundColor: '#2d2d2d',
          borderLeft: '1px solid #555',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板标题 */}
          <div style={{
            height: '50px',
            backgroundColor: '#333',
            borderBottom: '1px solid #555',
            display: 'flex',
            alignItems: 'center',
            padding: '0 15px',
            color: '#e0e0e0',
            fontSize: '14px',
            fontWeight: '500'
          }}>
            参数设置
          </div>

          {/* 参数内容 */}
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '0'
          }}>
            <UploadForm onUpload={handleUpload} isLoading={isLoading} />
            <div style={{ borderTop: '1px solid #555', marginTop: '10px' }}>
              <TestImage />
            </div>
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div style={{
        height: '30px',
        backgroundColor: '#333',
        borderTop: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        padding: '0 15px',
        color: '#ccc',
        fontSize: '12px'
      }}>
        {/* 左侧状态信息 */}
        <span>
          {isLoading ? '🔄 处理中...' : 
           result ? '✅ 处理完成' : 
           error ? '❌ 处理失败' : '⏳ 就绪'}
        </span>
        
        {/* 图像信息 */}
        {imageInfo && (
          <>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>📄 {imageInfo.name}</span>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>📐 {imageInfo.dimensions}</span>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>💾 {imageInfo.size}</span>
          </>
        )}
        
        {/* 处理时间 */}
        {processingTime && (
          <>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>⏱️ {processingTime}s</span>
          </>
        )}
        
        {/* 缩放信息 */}
        {zoomLevel !== 100 && (
          <>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>🔍 {zoomLevel}%</span>
          </>
        )}
        
        {/* 旋转信息 */}
        {rotation !== 0 && (
          <>
            <div style={{ width: '1px', height: '16px', backgroundColor: '#555', margin: '0 10px' }}></div>
            <span>↻ {rotation}°</span>
          </>
        )}
        
        <div style={{ flex: 1 }}></div>
        
        {/* 右侧版本信息 */}
        <span>🚀 RealESRGAN v2.1 Enhanced</span>
      </div>

      {/* CSS动画 */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

export default App;
