import React, { useState, useRef, useEffect } from 'react';

const ResultView = ({ result, originalImage }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showParams, setShowParams] = useState(false);
  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比
  const [isDragging, setIsDragging] = useState(false);
  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'
  const containerRef = useRef(null);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  const downloadImage = () => {
    const link = document.createElement('a');
    link.href = `http://localhost:8001/result/${result.enhanced_url}`;
    link.download = `enhanced_${result.filename}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 分割线拖拽处理
  const handleMouseDown = (e) => {
    setIsDragging(true);
    e.preventDefault();
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSplitPosition(percentage);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  // 重置分割线位置
  const resetSplit = () => {
    setSplitPosition(50);
  };

  return (
    <div style={{ marginTop: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '10px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px', flexWrap: 'wrap', gap: '10px' }}>
        <h2 style={{ color: '#28a745', margin: 0 }}>✅ 处理完成</h2>

        {/* 视图模式切换 */}
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <button
            onClick={() => setViewMode('split')}
            style={{
              padding: '6px 12px',
              backgroundColor: viewMode === 'split' ? '#007bff' : '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            分割对比
          </button>
          <button
            onClick={() => setViewMode('side-by-side')}
            style={{
              padding: '6px 12px',
              backgroundColor: viewMode === 'side-by-side' ? '#007bff' : '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            并排对比
          </button>
          {viewMode === 'split' && (
            <button
              onClick={resetSplit}
              style={{
                padding: '6px 12px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              重置
            </button>
          )}
        </div>
      </div>

      {result.message && (
        <p style={{ color: '#6c757d', marginBottom: '15px' }}>{result.message}</p>
      )}

      {/* 显示使用的参数 */}
      {result.params_used && (
        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
            <h4 style={{ margin: 0, marginRight: '10px' }}>处理参数</h4>
            <button
              onClick={() => setShowParams(!showParams)}
              style={{
                padding: '4px 8px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {showParams ? '隐藏' : '显示'}
            </button>
          </div>

          {showParams && (
            <div style={{ fontSize: '14px', color: '#495057' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '8px' }}>
                <div><strong>超分倍数:</strong> {result.params_used.scale}x</div>
                <div><strong>AI模型:</strong> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>
                <div><strong>锐化:</strong> {(result.params_used.sharpening * 100).toFixed(0)}%</div>
                <div><strong>降噪:</strong> {result.params_used.denoising}</div>
                <div><strong>饱和度:</strong> {result.params_used.saturation.toFixed(1)}</div>
                <div><strong>对比度:</strong> {result.params_used.contrast.toFixed(1)}</div>
                <div><strong>亮度:</strong> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>
                <div><strong>美颜:</strong> {(result.params_used.beauty * 100).toFixed(0)}%</div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 图像对比区域 */}
      {viewMode === 'split' ? (
        // 分割线对比模式
        <div
          ref={containerRef}
          style={{
            position: 'relative',
            width: '100%',
            maxWidth: '800px',
            margin: '0 auto',
            cursor: isDragging ? 'ew-resize' : 'default',
            userSelect: 'none'
          }}
        >
          <div style={{ textAlign: 'center', marginBottom: '15px' }}>
            <h3 style={{ margin: 0, color: '#495057', fontSize: '18px' }}>拖拽分割线对比原图与增强效果</h3>
            <p style={{ margin: '5px 0', fontSize: '14px', color: '#6c757d' }}>
              左侧：原始图像 | 右侧：增强图像 | 当前位置：{splitPosition.toFixed(0)}%
            </p>
          </div>

          {originalImage && (
            <div style={{ position: 'relative', display: 'inline-block', width: '100%' }}>
              {/* 增强图像作为背景 */}
              {imageError ? (
                <div style={{
                  width: '100%',
                  height: '400px',
                  border: '2px dashed #dc3545',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#dc3545',
                  backgroundColor: '#f8d7da'
                }}>
                  增强图像加载失败
                </div>
              ) : (
                <img
                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}
                  alt="增强图像"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                  style={{
                    width: '100%',
                    maxWidth: '800px',
                    height: 'auto',
                    border: '2px solid #28a745',
                    borderRadius: '8px',
                    display: 'block',
                    opacity: imageLoaded ? 1 : 0.5
                  }}
                />
              )}

              {/* 原始图像覆盖层 */}
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: `${splitPosition}%`,
                  height: '100%',
                  overflow: 'hidden',
                  borderRadius: '8px 0 0 8px'
                }}
              >
                <img
                  src={originalImage}
                  alt="原始图像"
                  style={{
                    width: `${100 * 100 / splitPosition}%`,
                    height: '100%',
                    objectFit: 'cover',
                    border: '2px solid #ddd',
                    borderRadius: '8px'
                  }}
                />
              </div>

              {/* 分割线 */}
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: `${splitPosition}%`,
                  width: '4px',
                  height: '100%',
                  backgroundColor: '#fff',
                  cursor: 'ew-resize',
                  boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                  transform: 'translateX(-2px)',
                  zIndex: 10
                }}
                onMouseDown={handleMouseDown}
              >
                {/* 分割线手柄 */}
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '20px',
                    height: '40px',
                    backgroundColor: '#007bff',
                    borderRadius: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}
                >
                  ⟷
                </div>
              </div>

              {!imageLoaded && !imageError && (
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: '#6c757d',
                  backgroundColor: 'rgba(255,255,255,0.8)',
                  padding: '10px',
                  borderRadius: '5px'
                }}>
                  加载中...
                </div>
              )}
            </div>
          )}
        </div>
      ) : (
        // 并排对比模式
        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', justifyContent: 'center' }}>
          {/* 原始图像 */}
          {originalImage && (
            <div style={{ textAlign: 'center', flex: '1', minWidth: '300px', maxWidth: '400px' }}>
              <h3 style={{ marginBottom: '10px', color: '#495057' }}>原始图像</h3>
              <img
                src={originalImage}
                alt="原始图像"
                style={{
                  width: '100%',
                  height: 'auto',
                  maxHeight: '400px',
                  border: '2px solid #007bff',
                  borderRadius: '8px',
                  objectFit: 'contain'
                }}
              />
            </div>
          )}

          {/* 增强图像 */}
          <div style={{ textAlign: 'center', flex: '1', minWidth: '300px', maxWidth: '400px' }}>
            <h3 style={{ marginBottom: '10px', color: '#495057' }}>增强图像</h3>

            {!imageLoaded && !imageError && (
              <div style={{
                width: '100%',
                height: '300px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '2px dashed #ccc',
                borderRadius: '8px',
                color: '#6c757d'
              }}>
                正在加载图像...
              </div>
            )}

            {imageError && (
              <div style={{
                width: '100%',
                height: '300px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '2px solid #dc3545',
                borderRadius: '8px',
                color: '#dc3545',
                backgroundColor: '#f8d7da'
              }}>
                图像加载失败
              </div>
            )}

            <img
              src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}
              alt="增强图像"
              onLoad={handleImageLoad}
              onError={handleImageError}
              style={{
                width: '100%',
                height: 'auto',
                maxHeight: '400px',
                border: '2px solid #28a745',
                borderRadius: '8px',
                objectFit: 'contain',
                display: imageLoaded ? 'block' : 'none'
              }}
            />
          </div>
        </div>
      )}

      {imageLoaded && (
        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <button
            onClick={downloadImage}
            style={{
              padding: '10px 20px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '16px',
              marginRight: '10px'
            }}
          >
            📥 下载增强图像
          </button>

          <a
            href={`http://localhost:8001/result/${result.enhanced_url}`}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '5px',
              fontSize: '16px'
            }}
          >
            🔍 在新窗口查看
          </a>
        </div>
      )}
    </div>
  );
};

export default ResultView;