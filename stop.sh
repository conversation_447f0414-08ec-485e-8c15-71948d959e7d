#!/bin/bash

# 图像增强Web应用停止脚本
# 停止前后端服务

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "${PROJECT_DIR}"

echo "=== 图像增强Web应用停止脚本 ==="

# 停止占用3000端口的进程（前端）
echo "检查前端服务 (端口3000)..."
FRONTEND_PIDS=$(lsof -ti:3000 2>/dev/null || echo "")
if [ ! -z "$FRONTEND_PIDS" ]; then
    echo "发现前端服务正在运行，正在停止..."
    for pid in $FRONTEND_PIDS; do
        echo "停止前端进程 PID: $pid"
        kill -TERM $pid 2>/dev/null || true
    done
    sleep 2
    
    # 检查是否还有进程在运行，强制杀死
    FRONTEND_PIDS=$(lsof -ti:3000 2>/dev/null || echo "")
    if [ ! -z "$FRONTEND_PIDS" ]; then
        echo "强制停止前端服务..."
        for pid in $FRONTEND_PIDS; do
            kill -KILL $pid 2>/dev/null || true
        done
    fi
    echo "✅ 前端服务已停止"
else
    echo "ℹ️  前端服务未运行"
fi

# 停止占用8001端口的进程（后端）
echo "检查后端服务 (端口8001)..."
BACKEND_PIDS=$(lsof -ti:8001 2>/dev/null || echo "")
if [ ! -z "$BACKEND_PIDS" ]; then
    echo "发现后端服务正在运行，正在停止..."
    for pid in $BACKEND_PIDS; do
        echo "停止后端进程 PID: $pid"
        kill -TERM $pid 2>/dev/null || true
    done
    sleep 2
    
    # 检查是否还有进程在运行，强制杀死
    BACKEND_PIDS=$(lsof -ti:8001 2>/dev/null || echo "")
    if [ ! -z "$BACKEND_PIDS" ]; then
        echo "强制停止后端服务..."
        for pid in $BACKEND_PIDS; do
            kill -KILL $pid 2>/dev/null || true
        done
    fi
    echo "✅ 后端服务已停止"
else
    echo "ℹ️  后端服务未运行"
fi

# 清理PID文件
if [ -f ".backend.pid" ]; then
    rm -f .backend.pid
    echo "清理后端PID文件"
fi

if [ -f ".frontend.pid" ]; then
    rm -f .frontend.pid
    echo "清理前端PID文件"
fi

# 额外检查：停止可能的Python和Node.js相关进程
echo "检查其他相关进程..."

# 查找可能的后端Python进程
PYTHON_PIDS=$(ps aux | grep "test_simple.py\|uvicorn.*8001" | grep -v grep | awk '{print $2}' || echo "")
if [ ! -z "$PYTHON_PIDS" ]; then
    echo "发现相关Python进程，正在停止..."
    for pid in $PYTHON_PIDS; do
        echo "停止Python进程 PID: $pid"
        kill -TERM $pid 2>/dev/null || true
    done
fi

# 查找可能的前端Node.js进程
NODE_PIDS=$(ps aux | grep "react-scripts.*start\|node.*3000" | grep -v grep | awk '{print $2}' || echo "")
if [ ! -z "$NODE_PIDS" ]; then
    echo "发现相关Node.js进程，正在停止..."
    for pid in $NODE_PIDS; do
        echo "停止Node.js进程 PID: $pid"
        kill -TERM $pid 2>/dev/null || true
    done
fi

echo ""
echo "🎉 所有服务已停止"
echo "现在可以安全地重新启动服务：./run.sh"
