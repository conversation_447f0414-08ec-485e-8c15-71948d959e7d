#!/bin/bash

# 图像增强Web应用启动脚本
# 启动前后端服务

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "${PROJECT_DIR}"

echo "=== 图像增强Web应用启动脚本 ==="
echo "项目目录: ${PROJECT_DIR}"

# 停止已运行的服务
echo "检查并停止已运行的服务..."

# 停止占用3000端口的进程（前端）
FRONTEND_PID=$(lsof -ti:3000 2>/dev/null || echo "")
if [ ! -z "$FRONTEND_PID" ]; then
    echo "发现前端服务正在运行 (PID: $FRONTEND_PID)，正在停止..."
    kill -TERM $FRONTEND_PID 2>/dev/null || true
    sleep 2
    # 如果进程仍在运行，强制杀死
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "强制停止前端服务..."
        kill -KILL $FRONTEND_PID 2>/dev/null || true
    fi
    echo "前端服务已停止"
else
    echo "前端服务未运行"
fi

# 停止占用8001端口的进程（后端）
BACKEND_PID=$(lsof -ti:8001 2>/dev/null || echo "")
if [ ! -z "$BACKEND_PID" ]; then
    echo "发现后端服务正在运行 (PID: $BACKEND_PID)，正在停止..."
    kill -TERM $BACKEND_PID 2>/dev/null || true
    sleep 2
    # 如果进程仍在运行，强制杀死
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "强制停止后端服务..."
        kill -KILL $BACKEND_PID 2>/dev/null || true
    fi
    echo "后端服务已停止"
else
    echo "后端服务未运行"
fi

# 等待端口释放
echo "等待端口释放..."
sleep 3

# 激活环境
echo "激活项目环境..."
source ./activate_env.sh

# 检查后端依赖
echo "检查后端依赖..."
cd backend

# 检查RealESRGAN模型文件
MODEL_FILE="realesr-general-x4v3.pth"
if [ ! -f "$MODEL_FILE" ]; then
    echo "⚠️  警告: RealESRGAN模型文件不存在: $MODEL_FILE"
    echo "   系统将使用简单插值算法作为备选方案"
    echo "   如需获得最佳效果，请下载模型文件到 backend/ 目录"
else
    echo "✅ RealESRGAN模型文件检查通过"
fi

# 安装后端依赖（如果需要）
if [ ! -f ".deps_installed" ]; then
    echo "安装后端Python依赖..."
    pip install -r requirments.txt
    pip install python-multipart  # 确保安装文件上传依赖
    touch .deps_installed
    echo "后端依赖安装完成"
else
    echo "后端依赖已安装"
fi

# 启动后端服务
echo "启动后端服务..."
echo "后端将在 http://localhost:8001 运行"
python test_simple.py &
BACKEND_PID=$!

cd "${PROJECT_DIR}"

# 检查前端依赖
echo "检查前端依赖..."
cd frontend

# 安装前端依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
    echo "前端依赖安装完成"
else
    echo "前端依赖已安装"
fi

# 启动前端服务
echo "启动前端服务..."
echo "前端将在 http://localhost:3000 运行"
BROWSER=none npm start &
FRONTEND_PID=$!

cd "${PROJECT_DIR}"

echo ""
echo "=== 服务启动完成 ==="
echo "后端服务: http://localhost:8001"
echo "前端服务: http://localhost:3000"
echo "后端PID: ${BACKEND_PID}"
echo "前端PID: ${FRONTEND_PID}"
echo ""
echo "按 Ctrl+C 停止所有服务"
echo "或者运行 './stop.sh' 来停止服务"

# 保存PID到文件，方便后续停止
echo "${BACKEND_PID}" > .backend.pid
echo "${FRONTEND_PID}" > .frontend.pid

# 等待用户中断
cleanup() {
    echo ""
    echo "正在停止服务..."

    # 停止后端服务
    if [ ! -z "${BACKEND_PID}" ] && kill -0 ${BACKEND_PID} 2>/dev/null; then
        echo "停止后端服务 (PID: ${BACKEND_PID})..."
        kill -TERM ${BACKEND_PID} 2>/dev/null || true
        sleep 2
        if kill -0 ${BACKEND_PID} 2>/dev/null; then
            kill -KILL ${BACKEND_PID} 2>/dev/null || true
        fi
    fi

    # 停止前端服务
    if [ ! -z "${FRONTEND_PID}" ] && kill -0 ${FRONTEND_PID} 2>/dev/null; then
        echo "停止前端服务 (PID: ${FRONTEND_PID})..."
        kill -TERM ${FRONTEND_PID} 2>/dev/null || true
        sleep 2
        if kill -0 ${FRONTEND_PID} 2>/dev/null; then
            kill -KILL ${FRONTEND_PID} 2>/dev/null || true
        fi
    fi

    # 清理PID文件
    rm -f .backend.pid .frontend.pid

    echo "所有服务已停止"
    exit 0
}

trap cleanup INT TERM

# 保持脚本运行
wait
