<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分割线对比功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .split-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            cursor: ew-resize;
            user-select: none;
        }
        .enhanced-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .original-overlay {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            overflow: hidden;
            border-radius: 8px 0 0 8px;
        }
        .original-image {
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }
        .split-line {
            position: absolute;
            top: 0;
            width: 4px;
            height: 100%;
            background-color: #fff;
            cursor: ew-resize;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            transform: translateX(-2px);
            z-index: 10;
        }
        .split-handle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 40px;
            background-color: #007bff;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .info {
            text-align: center;
            color: #666;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 分割线对比功能测试</h1>
        <p>这是一个独立的分割线对比功能演示，展示了如何通过拖拽分割线来对比原图和增强后的图像。</p>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="setSplitPosition(25)">25%</button>
            <button class="btn btn-primary" onclick="setSplitPosition(50)">50%</button>
            <button class="btn btn-primary" onclick="setSplitPosition(75)">75%</button>
            <button class="btn btn-success" onclick="resetSplit()">重置</button>
        </div>
        
        <div class="info">
            <p>左侧：原始图像 | 右侧：增强图像 | 当前位置：<span id="position">50</span>%</p>
        </div>
        
        <div class="split-container" id="splitContainer">
            <!-- 增强图像作为背景 -->
            <img src="https://via.placeholder.com/800x400/28a745/ffffff?text=Enhanced+Image" 
                 alt="增强图像" class="enhanced-image">
            
            <!-- 原始图像覆盖层 -->
            <div class="original-overlay" id="originalOverlay" style="width: 50%;">
                <img src="https://via.placeholder.com/800x400/007bff/ffffff?text=Original+Image" 
                     alt="原始图像" class="original-image" id="originalImage">
            </div>
            
            <!-- 分割线 -->
            <div class="split-line" id="splitLine" style="left: 50%;">
                <div class="split-handle">⟷</div>
            </div>
        </div>
        
        <div class="info">
            <p><strong>使用说明：</strong></p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>拖拽中间的蓝色分割线来调整对比位置</li>
                <li>左侧显示原始图像，右侧显示增强后的图像</li>
                <li>点击上方按钮快速设置分割位置</li>
                <li>这个功能已集成到主应用中</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:3000" class="btn btn-primary" style="text-decoration: none;">
                访问完整应用
            </a>
        </div>
    </div>

    <script>
        let isDragging = false;
        let splitPosition = 50;
        
        const container = document.getElementById('splitContainer');
        const overlay = document.getElementById('originalOverlay');
        const splitLine = document.getElementById('splitLine');
        const originalImage = document.getElementById('originalImage');
        const positionSpan = document.getElementById('position');
        
        function updateSplitPosition(percentage) {
            splitPosition = Math.max(0, Math.min(100, percentage));
            
            overlay.style.width = splitPosition + '%';
            splitLine.style.left = splitPosition + '%';
            originalImage.style.width = (100 * 100 / splitPosition) + '%';
            positionSpan.textContent = splitPosition.toFixed(0);
        }
        
        function setSplitPosition(percentage) {
            updateSplitPosition(percentage);
        }
        
        function resetSplit() {
            updateSplitPosition(50);
        }
        
        // 鼠标事件处理
        splitLine.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const rect = container.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = (x / rect.width) * 100;
            updateSplitPosition(percentage);
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
        
        // 触摸事件支持（移动端）
        splitLine.addEventListener('touchstart', (e) => {
            isDragging = true;
            e.preventDefault();
        });
        
        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            
            const rect = container.getBoundingClientRect();
            const x = e.touches[0].clientX - rect.left;
            const percentage = (x / rect.width) * 100;
            updateSplitPosition(percentage);
        });
        
        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    </script>
</body>
</html>
